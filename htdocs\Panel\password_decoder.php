<?php
// <PERSON><PERSON><PERSON> tra đăng nhập và quyền admin
session_start();
if (!isset($_SESSION['loggedin']) || !isset($_SESSION['is_admin']) || $_SESSION['is_admin'] != 1) {
    header("Location: ../index.php?error=" . urlencode('Bạn không có quyền truy cập trang này!'));
    exit();
}

$username = $_SESSION['team_user'] ?? 'Unknown';
$message = '';
$messageType = '';
$decodedPassword = '';
$encodedPassword = '';

// Hàm mã hóa mật khẩu theo kiểu MySQL PASSWORD()
function sqlPassword($input)
{
    $pass = strtoupper(sha1(sha1($input, true)));
    return '*' . $pass;
}

// Đường dẫn đến file từ điển mật khẩu
$passwordDictionaryFile = __DIR__ . '/../assets/wordlists/passwords.txt';

// Danh sách mật khẩu phổ biến mở rộng
$commonPasswords = [
    'Admin123',
    '123456',
    'password',
    'admin',
    'admin123',
    'qwerty',
    '123456789',
    '12345678',
    'abc123',
    '1234567',
    'password1',
    '12345',
    '1234567890',
    'default',
    '123123',
    '000000',
    'iloveyou',
    '1234',
    'qwerty123',
    'test',
    'welcome',
    'monkey',
    'login',
    'princess',
    'sunshine',
    'letmein',
    'dragon',
    'baseball',
    'football',
    'master',
    'michael',
    'superman',
    'batman',
    'trustno1',
    'passw0rd',
    'hello',
    'charlie',
    'shadow',
    'summer',
    'tigger',
    'jordan',
    'harley',
    'ranger',
    'thomas',
    'robert',
    'soccer',
    'hockey',
    'killer',
    'george',
    'andrew',
    'charlie',
    'michelle',
    'jennifer',
    'daniel',
    'maggie',
    'starwars'
];

// Xử lý form submit
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        if ($_POST['action'] === 'decode' && !empty($_POST['encoded_password'])) {
            $encodedPassword = $_POST['encoded_password'];

            // Kiểm tra xem hash có đúng định dạng MySQL PASSWORD() không
            if (strlen($encodedPassword) === 41 && $encodedPassword[0] === '*') {
                // Kiểm tra danh sách mật khẩu phổ biến
                $foundPassword = false;

                foreach ($commonPasswords as $password) {
                    if (sqlPassword($password) === $encodedPassword) {
                        $decodedPassword = "Mật khẩu đã mã hóa: $encodedPassword\n\n";
                        $decodedPassword .= "Mật khẩu gốc: $password\n\n";
                        $decodedPassword .= "Phương thức mã hóa: MySQL PASSWORD()\n";
                        $message = 'Đã tìm thấy mật khẩu gốc!';
                        $messageType = 'success';
                        $foundPassword = true;
                        break;
                    }
                }

                // Nếu không tìm thấy trong danh sách phổ biến, thử với từ điển
                if (!$foundPassword && file_exists($passwordDictionaryFile)) {
                    $handle = fopen($passwordDictionaryFile, "r");
                    if ($handle) {
                        $count = 0;
                        $maxAttempts = 50000; // Tăng giới hạn số lần thử

                        while (($password = fgets($handle)) !== false && $count < $maxAttempts) {
                            $password = trim($password);
                            if (sqlPassword($password) === $encodedPassword) {
                                $decodedPassword = "Mật khẩu đã mã hóa: $encodedPassword\n\n";
                                $decodedPassword .= "Mật khẩu gốc: $password\n\n";
                                $decodedPassword .= "Phương thức mã hóa: MySQL PASSWORD()\n";
                                $message = 'Đã tìm thấy mật khẩu gốc trong từ điển!';
                                $messageType = 'success';
                                $foundPassword = true;
                                break;
                            }
                            $count++;
                        }
                        fclose($handle);
                    }
                }

                // Thử với các biến thể của mật khẩu phổ biến nếu vẫn không tìm thấy
                if (!$foundPassword) {
                    // Thử với các biến thể của tên người dùng
                    $userVariations = [
                        'admin',
                        'Admin',
                        'ADMIN',
                        'administrator',
                        'Administrator',
                        'root',
                        'Root',
                        'ROOT',
                        'user',
                        'User',
                        'USER'
                    ];

                    $commonSuffixes = ['123', '1234', '12345', '123456', '2023', '2024', '!', '@', '#', '$'];

                    foreach ($userVariations as $user) {
                        // Thử username nguyên bản
                        if (sqlPassword($user) === $encodedPassword) {
                            $decodedPassword = "Mật khẩu đã mã hóa: $encodedPassword\n\n";
                            $decodedPassword .= "Mật khẩu gốc: $user\n\n";
                            $decodedPassword .= "Phương thức mã hóa: MySQL PASSWORD()\n";
                            $message = 'Đã tìm thấy mật khẩu gốc!';
                            $messageType = 'success';
                            $foundPassword = true;
                            break;
                        }

                        // Thử username + suffix
                        foreach ($commonSuffixes as $suffix) {
                            $testPass = $user . $suffix;
                            if (sqlPassword($testPass) === $encodedPassword) {
                                $decodedPassword = "Mật khẩu đã mã hóa: $encodedPassword\n\n";
                                $decodedPassword .= "Mật khẩu gốc: $testPass\n\n";
                                $decodedPassword .= "Phương thức mã hóa: MySQL PASSWORD()\n";
                                $message = 'Đã tìm thấy mật khẩu gốc!';
                                $messageType = 'success';
                                $foundPassword = true;
                                break 2;
                            }
                        }
                    }
                }

                // Kiểm tra các hash đã biết
                if (!$foundPassword) {
                    $knownHashes = [
                        '*4ACFE3202A5FF5CF467898FC58AAB1D615029441' => 'Admin123',
                        '*6BB4837EB74329105EE4568DDA7DC67ED2CA2AD9' => '123456',
                        '*E56A114692FE0DE073F9A1DD68A00EEB9703F3F1' => 'password',
                        '*23AE809DDACAF96AF0FD78ED04B6A265E05AA257' => 'admin',
                        '*CCB9A4164C68554750FF8C3BB168FDC55A93F0F1' => 'admin123'
                    ];

                    if (isset($knownHashes[$encodedPassword])) {
                        $decodedPassword = "Mật khẩu đã mã hóa: $encodedPassword\n\n";
                        $decodedPassword .= "Mật khẩu gốc: " . $knownHashes[$encodedPassword] . "\n\n";
                        $decodedPassword .= "Phương thức mã hóa: MySQL PASSWORD()\n";
                        $message = 'Đã tìm thấy mật khẩu gốc từ cơ sở dữ liệu đã biết!';
                        $messageType = 'success';
                        $foundPassword = true;
                    }
                }

                // Nếu vẫn không tìm thấy
                if (!$foundPassword) {
                    $decodedPassword = "Mật khẩu đã mã hóa: $encodedPassword\n\n";
                    $decodedPassword .= "Không thể tìm thấy mật khẩu gốc.\n\n";
                    $decodedPassword .= "Phương thức mã hóa có thể là:\n";
                    $decodedPassword .= "- MySQL PASSWORD()\n";
                    $decodedPassword .= "- SHA-256\n";
                    $decodedPassword .= "- MD5\n";
                    $decodedPassword .= "- SHA1\n";
                    $decodedPassword .= "- Bcrypt\n";
                    $message = 'Không thể giải mã mật khẩu này.';
                    $messageType = 'warning';
                }
            } else {
                // Kiểm tra các định dạng hash khác
                $foundPassword = false;
                $plainPassword = '';

                // Kiểm tra MD5 (32 ký tự hex)
                if (strlen($encodedPassword) === 32 && ctype_xdigit($encodedPassword)) {
                    foreach ($commonPasswords as $password) {
                        if (md5($password) === $encodedPassword) {
                            $plainPassword = $password;
                            $foundPassword = true;
                            break;
                        }
                    }

                    if ($foundPassword) {
                        $decodedPassword = "Mật khẩu đã mã hóa: $encodedPassword\n\n";
                        $decodedPassword .= "Mật khẩu gốc: $plainPassword\n\n";
                        $decodedPassword .= "Phương thức mã hóa: MD5\n";
                        $message = 'Đã tìm thấy mật khẩu gốc!';
                        $messageType = 'success';
                    }
                }

                // Kiểm tra SHA1 (40 ký tự hex)
                if (!$foundPassword && strlen($encodedPassword) === 40 && ctype_xdigit($encodedPassword)) {
                    foreach ($commonPasswords as $password) {
                        if (sha1($password) === $encodedPassword) {
                            $plainPassword = $password;
                            $foundPassword = true;
                            break;
                        }
                    }

                    if ($foundPassword) {
                        $decodedPassword = "Mật khẩu đã mã hóa: $encodedPassword\n\n";
                        $decodedPassword .= "Mật khẩu gốc: $plainPassword\n\n";
                        $decodedPassword .= "Phương thức mã hóa: SHA1\n";
                        $message = 'Đã tìm thấy mật khẩu gốc!';
                        $messageType = 'success';
                    }
                }

                // Kiểm tra SHA-256 (64 ký tự hex)
                if (!$foundPassword && strlen($encodedPassword) === 64 && ctype_xdigit($encodedPassword)) {
                    foreach ($commonPasswords as $password) {
                        if (hash('sha256', $password) === $encodedPassword) {
                            $plainPassword = $password;
                            $foundPassword = true;
                            break;
                        }
                    }

                    if ($foundPassword) {
                        $decodedPassword = "Mật khẩu đã mã hóa: $encodedPassword\n\n";
                        $decodedPassword .= "Mật khẩu gốc: $plainPassword\n\n";
                        $decodedPassword .= "Phương thức mã hóa: SHA-256\n";
                        $message = 'Đã tìm thấy mật khẩu gốc!';
                        $messageType = 'success';
                    }
                }

                if (!$foundPassword) {
                    $decodedPassword = "Mật khẩu đã mã hóa: $encodedPassword\n\n";
                    $decodedPassword .= "Định dạng không phải là MySQL PASSWORD().\n\n";
                    $decodedPassword .= "Đã thử các phương thức mã hóa khác nhưng không tìm thấy kết quả.\n";
                    $message = 'Định dạng hash không hợp lệ hoặc không thể giải mã.';
                    $messageType = 'danger';
                }
            }
        } elseif ($_POST['action'] === 'encode' && !empty($_POST['plain_password'])) {
            // Giữ nguyên phần mã hóa
            $plainPassword = $_POST['plain_password'];
            $encodedPassword = "Mật khẩu gốc: $plainPassword\n\n";

            // MySQL PASSWORD() style
            $mysqlPass = strtoupper(sha1(sha1($plainPassword, true)));
            $encodedPassword .= "MySQL PASSWORD(): *$mysqlPass\n";

            // SHA-256
            $encodedPassword .= "SHA-256: " . hash('sha256', $plainPassword) . "\n";

            // MD5
            $encodedPassword .= "MD5: " . md5($plainPassword) . "\n";

            // SHA1
            $encodedPassword .= "SHA1: " . sha1($plainPassword) . "\n";

            // Bcrypt
            $encodedPassword .= "Bcrypt: " . password_hash($plainPassword, PASSWORD_BCRYPT) . "\n";

            $message = 'Đã mã hóa mật khẩu thành công.';
            $messageType = 'success';
        }
    }
} else {
    $message = 'Nhập mật khẩu đã mã hóa để giải mã hoặc mật khẩu gốc để mã hóa.';
    $messageType = 'info';
}
?>

<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Giải mã mật khẩu - KPAH</title>
    <link href="../assets/homepage/css/bootstrap.min.css" rel="stylesheet" type="text/css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <script src="../assets/homepage/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/homepage/js/jquery-1.9.1.min.js"></script>
    <style>
        body {
            background-color: #1a1a1a;
            color: #f9d686;
            font-family: Arial, sans-serif;
        }

        .admin-container {
            padding: 20px;
        }

        .admin-header {
            background-color: #000;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border: 1px solid #f9d686;
        }

        .admin-title {
            margin: 0;
            color: #f9d686;
            font-weight: bold;
        }

        .admin-user {
            color: #fff;
        }

        .admin-card {
            background-color: #2a2a2a;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #f9d686;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .admin-card h2 {
            color: #f9d686;
            font-size: 1.5rem;
            margin-bottom: 15px;
            border-bottom: 1px solid #f9d686;
            padding-bottom: 10px;
        }

        .btn-admin {
            background-color: #f9d686;
            color: #000;
            border: none;
            font-weight: bold;
            padding: 8px 16px;
            margin-right: 5px;
        }

        .btn-admin:hover {
            background-color: #e5c677;
            color: #000;
        }

        .admin-menu {
            background-color: #2a2a2a;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid #f9d686;
        }

        .admin-menu ul {
            list-style: none;
            padding: 0;
            margin: 0;
            display: flex;
            flex-wrap: wrap;
        }

        .admin-menu li {
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .admin-menu a {
            display: block;
            padding: 8px 15px;
            background-color: #333;
            color: #f9d686;
            text-decoration: none;
            border-radius: 3px;
            border: 1px solid rgba(249, 214, 134, 0.3);
            transition: all 0.3s ease;
        }

        .admin-menu a:hover,
        .admin-menu a.active {
            background-color: #f9d686;
            color: #000;
        }

        .admin-menu a i {
            margin-right: 5px;
        }

        .password-output {
            background-color: #333;
            color: #fff;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            margin-top: 20px;
        }

        @media (max-width: 768px) {
            .admin-header {
                flex-direction: column;
                text-align: center;
            }

            .admin-user {
                margin-top: 10px;
            }

            .admin-menu ul {
                flex-direction: column;
            }

            .admin-menu li {
                margin-right: 0;
                margin-bottom: 5px;
            }
        }
    </style>
</head>

<body>
    <div class="container admin-container">
        <div class="admin-header">
            <h1 class="admin-title">KPAH - Giải mã mật khẩu</h1>
            <div class="admin-user">
                Xin chào, <strong><?php echo htmlspecialchars($username); ?></strong> |
                <a href="../Views/Account.php" class="text-light">Tài khoản</a> |
                <a href="../handle/logout.php" class="text-light">Đăng xuất</a>
            </div>
        </div>

        <div class="admin-menu">
            <ul>
                <li><a href="index.php"><i class="fas fa-tachometer-alt"></i> Tổng quan</a></li>
                <li><a href="users.php"><i class="fas fa-users"></i> Quản lý người dùng</a></li>
                <li><a href="bank_topups.php"><i class="fas fa-university"></i> Quản lý nạp ATM</a></li>
                <li><a href="bank_accounts.php"><i class="fas fa-credit-card"></i> Quản lý tài khoản ngân hàng</a></li>
                <li><a href="activations.php"><i class="fas fa-check-circle"></i> Kích hoạt tài khoản</a></li>
                <li><a href="giftcodes.php"><i class="fas fa-gift"></i> Quản lý Giftcode</a></li>
                <li><a href="api_management.php"><i class="fas fa-plug"></i> Quản lý API</a></li>
                <li><a href="password_decoder.php" class="active"><i class="fas fa-key"></i> Giải mã mật khẩu</a></li>
                <li><a href="site_settings.php"><i class="fas fa-cogs"></i> Cấu hình trang web</a></li>

                <li><a href="../index.php"><i class="fas fa-home"></i> Về trang chủ</a></li>
            </ul>
        </div>

        <?php if (!empty($message)): ?>
            <div class="alert alert-<?php echo $messageType; ?>" role="alert">
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>

        <div class="row">
            <div class="col-md-6">
                <div class="admin-card">
                    <h2><i class="fas fa-unlock-alt"></i> Giải mã mật khẩu</h2>
                    <p>Nhập mật khẩu đã mã hóa để xem thông tin về phương thức mã hóa.</p>
                    <form method="post" action="">
                        <div class="mb-3">
                            <label for="encoded_password" class="form-label">Mật khẩu đã mã hóa</label>
                            <input type="text" class="form-control" id="encoded_password" name="encoded_password"
                                required>
                        </div>
                        <button type="submit" name="action" value="decode" class="btn btn-admin">Kiểm tra</button>
                    </form>
                </div>
            </div>

            <div class="col-md-6">
                <div class="admin-card">
                    <h2><i class="fas fa-lock"></i> Mã hóa mật khẩu</h2>
                    <p>Nhập mật khẩu gốc để xem các phiên bản mã hóa.</p>
                    <form method="post" action="">
                        <div class="mb-3">
                            <label for="plain_password" class="form-label">Mật khẩu gốc</label>
                            <input type="text" class="form-control" id="plain_password" name="plain_password" required>
                        </div>
                        <button type="submit" name="action" value="encode" class="btn btn-admin">Mã hóa</button>
                    </form>
                </div>
            </div>
        </div>

        <?php if (!empty($decodedPassword) || !empty($encodedPassword)): ?>
            <div class="admin-card">
                <h2><i class="fas fa-file-code"></i> Kết quả</h2>
                <div class="password-output">
                    <?php echo !empty($decodedPassword) ? htmlspecialchars($decodedPassword) : htmlspecialchars($encodedPassword); ?>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <script>
        $(document).ready(function() {
            // Add any JavaScript functionality here
        });
    </script>
</body>

</html>