<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Đặt lại mật khẩu - KPAH Game</title>
    <link href="../assets/homepage/css/bootstrap.min.css" rel="stylesheet" type="text/css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            background-color: #1a1a1a;
            color: #f9d686;
            font-family: Arial, sans-serif;
            background-image: url('../assets/homepage/images/bg-body.jpg');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
        }

        .reset-container {
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: rgba(0, 0, 0, 0.7);
            border-radius: 10px;
            border: 1px solid #f9d686;
            box-shadow: 0 0 20px rgba(249, 214, 134, 0.3);
        }

        .reset-header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #f9d686;
        }

        .reset-header h2 {
            color: #f9d686;
            font-weight: bold;
        }

        .reset-link {
            background-color: rgba(0, 0, 0, 0.5);
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            word-break: break-all;
        }

        .btn-primary {
            background-color: #f9d686;
            border-color: #f9d686;
            color: #000;
            font-weight: bold;
        }

        .btn-primary:hover {
            background-color: #e5c677;
            border-color: #e5c677;
            color: #000;
        }

        .home-link {
            text-align: center;
            margin-top: 20px;
        }

        .home-link a {
            color: #f9d686;
            text-decoration: none;
        }

        .home-link a:hover {
            text-decoration: underline;
        }

        .alert {
            border-radius: 5px;
            margin-bottom: 20px;
        }

        .alert-warning {
            background-color: rgba(255, 193, 7, 0.8);
            border-color: #ffc107;
            color: #212529;
        }

        .alert-success {
            background-color: rgba(40, 167, 69, 0.8);
            border-color: #28a745;
            color: white;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="reset-container">
            <div class="reset-header">
                <h2><i class="fas fa-key"></i> Đặt lại mật khẩu</h2>
            </div>

            <div class="alert alert-warning">
                <p><strong>Lưu ý:</strong> Không thể gửi email đặt lại mật khẩu. Thay vào đó, bạn có thể sử dụng liên kết bên dưới để đặt lại mật khẩu.</p>
            </div>

            <div class="alert alert-success">
                <p>Xin chào <strong><?php echo htmlspecialchars($username); ?></strong>,</p>
                <p>Chúng tôi đã tạo liên kết đặt lại mật khẩu cho tài khoản của bạn. Vui lòng nhấp vào liên kết bên dưới để đặt lại mật khẩu:</p>
            </div>

            <?php
            // Create the reset link
            $resetLink = $app_config['site_url'] . '/password_reset/reset.php?token=' . $token;
            ?>

            <div class="reset-link">
                <a href="<?php echo htmlspecialchars($resetLink); ?>" target="_blank"><?php echo htmlspecialchars($resetLink); ?></a>
            </div>

            <div class="alert alert-warning">
                <p><strong>Lưu ý:</strong> Liên kết này sẽ hết hạn sau <?php echo $app_config['token_expiry']; ?> giờ.</p>
                <p>Nếu bạn không yêu cầu đặt lại mật khẩu, vui lòng bỏ qua thông báo này.</p>
            </div>

            <div class="text-center">
                <a href="<?php echo htmlspecialchars($resetLink); ?>" class="btn btn-primary" target="_blank">Đặt lại mật khẩu</a>
            </div>

            <div class="home-link mt-3">
                <a href="../index.php"><i class="fas fa-home"></i> Trang chủ</a>
            </div>
        </div>
    </div>
</body>

</html>