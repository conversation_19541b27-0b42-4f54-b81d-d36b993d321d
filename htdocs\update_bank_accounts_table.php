<?php
// B<PERSON>t hiển thị lỗi để debug
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Thông tin kết nối cơ sở dữ liệu
require_once 'Controllers/database.php';

echo "<h1>Cậ<PERSON> nhật cấu trúc bảng bank_accounts</h1>";

try {
    // Kết nối đến cơ sở dữ liệu
    $conn = connectPDO($config1);

    echo "<p>Kết nối thành công đến cơ sở dữ liệu</p>";

    // Kiểm tra xem bảng bank_accounts có tồn tại không
    $tableCheck = $conn->query("SHOW TABLES LIKE 'bank_accounts'");
    $tableExists = $tableCheck->rowCount() > 0;

    echo "<p>Bảng bank_accounts " . ($tableExists ? "đã tồn tại" : "chưa tồn tại") . "</p>";

    if (!$tableExists) {
        // Tạo bảng bank_accounts nếu chưa tồn tại
        $conn->exec("CREATE TABLE bank_accounts (
            id INT AUTO_INCREMENT PRIMARY KEY,
            bank_name VARCHAR(255) NOT NULL,
            account_name VARCHAR(255) NOT NULL,
            account_number VARCHAR(50) NOT NULL,
            is_active TINYINT(1) NOT NULL DEFAULT 1,
            qr_code_path VARCHAR(255),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");

        echo "<p style='color:green'>Đã tạo bảng bank_accounts thành công</p>";

        // Thêm dữ liệu mẫu
        $conn->exec("INSERT INTO bank_accounts (bank_name, account_name, account_number, is_active) VALUES 
            ('Vietcombank', 'KPAH Game', '**********', 1),
            ('Techcombank', 'KPAH Game', '**********', 1),
            ('MBBank', 'KPAH Game', '**********', 1)
        ");

        echo "<p style='color:green'>Đã thêm dữ liệu mẫu vào bảng bank_accounts</p>";
    } else {
        // Kiểm tra cấu trúc bảng hiện tại
        $result = $conn->query("DESCRIBE bank_accounts");
        $columns = $result->fetchAll(PDO::FETCH_COLUMN);

        echo "<p>Các cột hiện tại trong bảng bank_accounts: " . implode(", ", $columns) . "</p>";

        // Kiểm tra xem cột qr_code_path đã tồn tại chưa
        $hasQrCodePath = in_array('qr_code_path', $columns);

        if (!$hasQrCodePath) {
            // Thêm cột qr_code_path nếu chưa tồn tại
            $conn->exec("ALTER TABLE bank_accounts ADD COLUMN qr_code_path VARCHAR(255) AFTER account_number");
            echo "<p style='color:green'>Đã thêm cột qr_code_path vào bảng bank_accounts</p>";
        } else {
            echo "<p>Cột qr_code_path đã tồn tại trong bảng bank_accounts</p>";
        }

        // Kiểm tra xem cột updated_at đã tồn tại chưa
        $hasUpdatedAt = in_array('updated_at', $columns);

        if (!$hasUpdatedAt) {
            // Thêm cột updated_at nếu chưa tồn tại
            $conn->exec("ALTER TABLE bank_accounts ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP");
            echo "<p style='color:green'>Đã thêm cột updated_at vào bảng bank_accounts</p>";
        } else {
            echo "<p>Cột updated_at đã tồn tại trong bảng bank_accounts</p>";
        }
    }

    // Hiển thị dữ liệu trong bảng
    $result = $conn->query("SELECT * FROM bank_accounts");
    $accounts = $result->fetchAll(PDO::FETCH_ASSOC);

    echo "<h2>Dữ liệu trong bảng bank_accounts:</h2>";

    if (count($accounts) > 0) {
        echo "<table border='1'>";
        echo "<tr>";
        foreach (array_keys($accounts[0]) as $column) {
            echo "<th>$column</th>";
        }
        echo "</tr>";

        foreach ($accounts as $account) {
            echo "<tr>";
            foreach ($account as $value) {
                echo "<td>" . htmlspecialchars($value) . "</td>";
            }
            echo "</tr>";
        }

        echo "</table>";
    } else {
        echo "<p>Không có dữ liệu trong bảng bank_accounts</p>";
    }

    echo "<p style='color:green'>Cập nhật cấu trúc bảng bank_accounts hoàn tất</p>";
} catch (PDOException $e) {
    echo "<h2 style='color:red'>Lỗi cơ sở dữ liệu</h2>";
    echo "<p>Lỗi: " . $e->getMessage() . "</p>";
}
