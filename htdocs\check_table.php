<?php
// Include database configuration
require_once 'Controllers/database.php';

// Connect to database
try {
    $conn = connectPDO($config1);
    echo "Connected to database successfully.<br>";
    
    // Check if card_api_config table exists
    $tableCheck = $conn->query("SHOW TABLES LIKE 'card_api_config'");
    echo "Table card_api_config exists: " . ($tableCheck->rowCount() > 0 ? 'Yes' : 'No') . "<br>";
    
    if ($tableCheck->rowCount() > 0) {
        // Check existing records
        $stmt = $conn->query("SELECT * FROM card_api_config");
        $configs = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "Found " . count($configs) . " API configurations:<br>";
        foreach ($configs as $config) {
            echo "ID: " . $config['id'] . ", API Name: " . $config['api_name'] . ", Merchant ID: " . $config['merchant_id'] . "<br>";
        }
    }
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage();
}
?>
