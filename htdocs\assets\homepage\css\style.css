@charset "utf-8";
@import url('https://fonts.googleapis.com/css2?family=Roboto&amp;display=swap');
/* CSS Document */
*,
.box_sizing_border_box,
*:before,
*:after {
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
}

::-webkit-scrollbar {
	width: 6px;
}

::-webkit-scrollbar-thumb {
	border-radius: 4px;
	background-color: #f9d686;
}

::-webkit-scrollbar-track {
	-webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.2);
}

* {
	box-sizing: border-box;
	appearance: none;
	-webkit-appearance: none;
	-moz-appearance: none;
	text-decoration: none
}

*:focus {
	outline: 0 !important;
	box-shadow: none !important;
}

@font-face {
	font-family: 'svntungsten';
	src: url("../fonts/SVN-Tungsten-Semibold.ttf") format("truetype");
	font-weight: normal;
	font-style: normal;
}

.f-svntungsten {
	font-family: 'svntungsten', sans-serif;
}

@font-face {
	font-family: 'utmalexander';
	src: url("../fonts/UTM-Alexander.ttf") format("truetype");
	font-weight: normal;
	font-style: normal;
}

.f-utmalexander {
	font-family: 'utmalexander', sans-serif;
}


body {
	width: 100%;
	overflow-x: hidden;
	overflow-y: auto;
	margin: 0;
	padding: 0;
	background: none;
	font-family: 'utmalexander', Verdana, Geneva, Tahoma, sans-serif;
	position: relative;
}

body,
html {
	padding: 0;
	margin: 0;
	background: #101216;
}

ul {
	list-style: none;
	margin: 0
}

a {
	transition: all 0.3s ease;
	-moz-transition: all 0.3s ease;
	-webkit-transition: all 0.3s ease;
}

.wrapper {
	transform-origin: top left;
	position: absolute;
}

.wrapper {
	margin: 0 auto;
	width: 100%;
}

.wrapper {
	width: 2000px;
	max-width: 2000px;
}

.container1 {
	display: block;
	margin: 0 auto;
	width: 1200px;
	position: relative;
}

.frame {
	width: 960px;
	height: auto;
	position: relative;
	margin: 0 auto;
}

.desktop {
	display: block !important
}

.mobile {
	display: none !important
}

.opacity-5 {
	opacity: 0.5;
}

.zoom {
	transition: transform .2s;
	/* Animation */
}

.zoom:hover {
	transform: scale(1.1);
	/* (150% zoom - Note: if the zoom is too large, it will go outside of the viewport) */
}

.zoom-50 {
	transition: transform .3s;
	/* Animation */
}

.zoom-50:hover {
	transform: scale(1.05);
	/* (150% zoom - Note: if the zoom is too large, it will go outside of the viewport) */
}

.brightness:hover {
	filter: brightness(1.2)
}

.section {
	position: relative;
	display: block;
	max-width: 100%;
	pointer-events: auto !important;
}

.section-background {
	position: absolute;
	display: block;
	width: 100%;
	margin: 0 auto;
	top: 0;
	left: 0;
}

.section-content {
	position: relative;
	display: block;
	width: 100%;
	margin: 0 auto;
	height: 100%;
}

@media (min-width: 2000px) {
	.wrapper {
		transform-origin: top center;
		position: relative;
	}

	html,
	body {
		margin: 0 auto;
	}
}

@media (min-width: 1440px) {
	.container {
		max-width: 1200px;
	}
}

@media (max-width: 960px) {
	.desktop {
		display: none !important
	}

	.mobile {
		display: block !important
	}

	.wrapper {
		width: 960px;
	}

	.container1 {
		width: 100%;
		max-width: 100%;
	}
}

@media (max-width: 575px) {
	.frame {
		max-width: calc(100% - 5px * 2)
	}
}

@media (orientation: portrait) {
	.frame {
		max-width: calc(100% - 10px * 2)
	}

}

.cursor-pointer {
	cursor: pointer;
}

.warning {
	position: absolute;
	top: 0;
	left: 0;
	z-index: 999;
}
@media (max-width: 960px) { 
	.warning {
		top: 106px;
		left: 0;
		z-index: 999;
	}
	.bg-bottom {
		display: none;
	}
}

.bg-bottom {
	position: absolute;
	top: 780px;
	left: 0;
	right: 0;
	z-index: 1;
}
/*------------Header-------------*/
.header {
	position: absolute;
	top: 0;
	z-index: 99;
}
.header .btn-group1,
.header .btn-group2 {
	width: 272px;
}
/*------------Section 1-------------*/
.section1 {
	width: 100%;
	height: 1000px;
	position: relative;
}
.section1 .logo {
	position: absolute;
	top: 16px;
	left: 0;
	right: 0;
	text-align: center;
	margin: 0 auto;
}
.section1 .art {
	position: absolute;
	top: 220px;
	left: 0;
	right: 0;
	text-align: center;
	margin: 0 auto;
	z-index: 10;
	animation: moveYDiv1 3s ease-in-out infinite;
}
.section1 .slogan {
	position: absolute;
	top: 485px;
	left: 192px;
	right: 0;
	text-align: center;
	margin: 0 auto;
	z-index: 12;
	pointer-events: none;
	animation: sloganAni .4s .1s linear both;
}
.section1 .btn-group-download {
	position: absolute;
	top: 760px;
	left: 0;
	right: 0;
	text-align: center;
	margin: 0 auto;
	width: 963px;
	height: 233px;
	background: url(../images/bg-download-page1.png) no-repeat;
	background-size: 100%;
	z-index: 11;
	animation: fadeup 1s ease-in-out;
}
.section1 .btn-group-download .btn-group1,
.section1 .btn-group-download .btn-group2 {
	width: 207px;
}
@media (max-width: 960px) {
	.section1 {
		width: 100%;
		height: 1590px;
		position: relative;
	}
	.section1 .logo {
		top: 260px;
        transform: scale(1.5);
        transform-origin: center top;
	}
	.section1 .slogan {
		position: absolute;
		top: 1200px;
		left: 40px;
		right: 0;
		text-align: center;
		margin: 0 auto;
		z-index: 12;
		pointer-events: none;
	}
	.section1 .btn-group-download  {
		display: none !important;
	}
	.section1 .art {
		position: absolute;
		top: 624px;
		left: 0;
		right: 0;
		text-align: center;
		margin: 0 auto;
		z-index: 10;
		animation: moveYDiv1 3s ease-in-out infinite;
	}
	
}

/*------------Section 2-------------*/
.section2 {
	width: 100%;
	height: 1432px;
	position: relative;
	overflow: hidden;
	z-index: 20;

}

.section2 .title-page2 {
	text-align: center;
	margin-top: 48px;
	margin-bottom: -14px;
}
.section2 .content-hoatdong {
	width: 1016px;
	height: 1264px;
	background: url(../images/bg-hoatdongsukien.png) no-repeat;
	background-size: 100%;
	margin: 0 auto;
	overflow: hidden;
}

.section2 .content-hoatdong .item1 {
	margin-top: 80px;
}
.section2 .content-hoatdong .item .left, .section2 .content-hoatdong .item .right {
	width: 340px;
}
.section2 .content-hoatdong .item .left {
	text-align: right;
}
.section2 .content-hoatdong .item .left h2.title {
	margin-bottom: 0;
}
.section2 .content-hoatdong .item .left h2.title a {
	font-family: 'svntungsten', sans-serif;
	font-size: 50px;
	color: #a00000;
	margin-bottom: 3px;
	font-weight: 600;
	text-decoration: none;
	text-transform: uppercase;
}
.section2 .content-hoatdong .item .left .status {
	font-family: 'utmalexander', sans-serif;
	font-size: 24px;
	color: #ae8e63;
	text-transform: uppercase;
}
.section2 .content-hoatdong .item .left .status {
	font-family: 'utmalexander', sans-serif;
	font-size: 24px;
	color: #ae8e63;
	text-transform: uppercase;
	margin-bottom: 0;
}
.section2 .content-hoatdong .item .right {
	font-family: 'utmalexander', sans-serif;
	font-size: 24px;
	color: #ae8e63;
	text-transform: uppercase;
}
.section2 .content-hoatdong .item:nth-child(even) .right {
	font-family: 'utmalexander', sans-serif;
	font-size: 24px;
	color: #ae8e63;
	text-transform: uppercase;
}
.section2 .art-page2 {
	position: absolute;
	bottom: 50px;
	left: 416px;
	z-index: 26;
	-webkit-animation: ani2 5s linear infinite;
    animation: ani2 5s linear infinite;
}

@media (max-width: 960px) {
	.section2 {
		width: 100%;
		height: 1410px;
		position: relative;
	}
	.section2 .art-page2 {
		position: absolute;
		bottom: 30px;
		left: -90px;
		z-index: 26;
		-webkit-animation: ani1 5s linear infinite;
		animation: ani1 5s linear infinite;
	}

}



/*----------------------------Section 3 Footer---------------------------------*/
.section3 {
	height: 260px;
	margin-top: 0;
	position: relative;
	z-index: 50;

}

.footer-content {
	font-family: 'Roboto', sans-serif;
	text-align: center;
	font-size: 18px;
	color: #ffe5c4;
	line-height: 30px;

}


.vtc-logo {
	margin: 0 auto;
	text-align: center;
}

.footer-text {
	padding: 14px 0 0 0;
	color: #fff;
	line-height: 20px;
}

.footer-text a {
	color: #c6cad9;
}

@media (max-width: 960px) { 
	.section3 {
		height: 380px;
		margin-top: 0;
		position: relative;
		z-index: 50;
		background-color: #992825;
	}
	.footer-content {
		font-size: 24px;	
	}
	.footer-text {
		line-height: 30px;
	}
}
/*----------------------------Menu Fixed right---------------------------------*/

.download {
	align-items: center;
	background: url(../images/bg-download.png) no-repeat;
	background-size: 100% 100%;
	display: flex;
	flex-direction: column;
	height: 450px;
	position: fixed;
	right: 0;
	top: 50%;
	-webkit-transform: translateY(-50%);
	-ms-transform: translateY(-50%);
	transform: translateY(-50%);
	transform-origin: right top;
	width: 168px;
	z-index: 10;
	animation: fadeleft 1.5s ease-in-out;
}

.download.active {
	pointer-events: auto;
}

@media (prefers-reduced-motion:reduce) {
	.download {
		transition: none
	}
}

@media screen and (min-width:2200px) {
	.download {
		transform: scale(1.3) translateY(-50%);
		-webkit-transform: scale(1.3) translateY(-50%);
		transform-origin: top right;
	}
}

@media screen and (min-width:3000px) {
	.download {
		transform: scale(2) translateY(-50%);
		-webkit-transform: scale(2) translateY(-50%);
		transform-origin: top right;
	}
}

@media (max-width: 1680px) {
	.download {
		transform: scale(.85) translateY(-50%);
		-webkit-transform: scale(.85) translateY(-50%);
		transform-origin: top right;
	}
}

@media (max-width:991.98px) {
	.download {
		display: none
	}
}

.download-btn-logo {
	padding-top: 0;
	width: 168px;
	text-align: center;
	margin-top: -34px;
}


.download .btn-img {
	width: 153px;
	margin-bottom: -2px;
}



.download .btn-group-social {
	margin-top: -16px;
	max-width: 172px;
}



/*----------------------------Light---------------------------------*/

.light{
    width:100%;
    height:100%;
    position:absolute;
    bottom:0;
    left:0;
    opacity:1;
    -webkit-animation:showsss 1s ease-in-out forwards;
    animation:showsss 1s ease-in-out forwards;
    z-index:0;
    opacity:.8
}
@media only screen and (max-width: 960px){
.light{
        display:none
    }
}
.light img{
    width:100%;
    height:100%;
    position:absolute;
    bottom:0;
    display:block;
    background-size:cover
}
.light img:nth-child(1){
    animation:light 20s infinite linear;
    -webkit-animation:light 20s infinite linear
}
.light img:nth-child(2){
    animation:light 20s 10s infinite linear;
    -webkit-animation:light 20s 10s infinite linear;
    left:-20%;
    bottom:-50%;
}
@keyframes light {
  from {
    left: 0;
    bottom: -50%; }
  to {
    left: 0;
    bottom: 60%;
	opacity:.5 } }




/*------------------Animation------------------*/
@keyframes blink {
	0% {
		opacity: 1;
		filter: brightness(1.1);
	}

	100% {
		opacity: 1;
	}
}

@-webkit-keyframes blink {
	0% {
		opacity: 1;
		filter: brightness(1.1);
	}

	100% {
		opacity: 1;
	}
}

@keyframes moveYDiv1 {
	0% {
		transform: translateY(0px);
	}

	50% {
		transform: translateY(20px);
	}

	100% {
		transform: translateY(0px);
	}
}

@-webkit-keyframes moveYDiv1 {
	0% {
		transform: translateY(0px);
	}

	50% {
		transform: translateY(20px);
	}

	100% {
		transform: translateY(0px);
	}
}

@keyframes turn1 {
	0% {
		-webkit-transform: rotate(0deg);
	}

	100% {
		-webkit-transform: rotate(360deg);
	}
}

@-webkit-keyframes turn1 {
	0% {
		-webkit-transform: rotate(0deg);
	}

	100% {
		-webkit-transform: rotate(360deg);
	}
}

.pulse {
	animation-name: pulse;
	-webkit-animation-name: pulse;

	animation-duration: 1.5s;
	-webkit-animation-duration: 1.5s;

	animation-iteration-count: infinite;
	-webkit-animation-iteration-count: infinite;
}

@keyframes pulse {
	0% {
		transform: scale(0.95);
		opacity: 1;
	}

	50% {
		transform: scale(1);
		opacity: 1;
	}

	100% {
		transform: scale(0.95);
		opacity: 1;
	}
}

@-webkit-keyframes pulse {
	0% {
		-webkit-transform: scale(0.95);
		opacity: 1;
	}

	50% {
		-webkit-transform: scale(1);
		opacity: 1;
	}

	100% {
		-webkit-transform: scale(0.95);
		opacity: 1;
	}
}

.blink {
	animation: blink 0.5s ease-in-out infinite;
}

@keyframes blink {
	0% {
		opacity: 1;
		filter: brightness(1.2);
	}

	100% {
		opacity: 1;
	}
}

@-webkit-keyframes blink {
	0% {
		opacity: 1;
		filter: brightness(1.2);
	}

	100% {
		opacity: 1;
	}
}

@-webkit-keyframes moveYDiv1 {
	0% {
		transform: translateY(0px);
	}

	50% {
		transform: translateY(-10px);
	}

	100% {
		transform: translateY(0px);
	}
}

@keyframes moveYDiv1 {
	0% {
		transform: translateY(0px);
	}

	50% {
		transform: translateY(-10px);
	}

	100% {
		transform: translateY(0px);
	}
}
@-webkit-keyframes moveYDiv {
	0% {
		transform: translateY(-5px);
	}

	50% {
		transform: translateY(0);
	}

	100% {
		transform: translateY(-5px);
	}
}

@keyframes moveYDiv {
	0% {
		transform: translateY(-5px);
	}

	50% {
		transform: translateY(0);
	}

	100% {
		transform: translateY(-5px);
	}
}
@-webkit-keyframes fadeleft {
	0% {
		right: -200px;
		opacity: 0;
	}

	100% {
		right: 0;
		opacity: 1;
	}
}

@keyframes fadeleft {
	0% {
		right: -200px;
		opacity: 0;
	}
	100% {
		right: 0;
		opacity: 1;
	}
}
@-webkit-keyframes fadeup {
	0% {
		top: 860px;
		opacity: 0;
	}

	100% {
		top: 760px;
		opacity: 1;
	}
}

@keyframes fadeup {
	0% {
		top: 860px;
		opacity: 0;
	}
	100% {
		top: 760px;
		opacity: 1;
	}
}
@-webkit-keyframes ani1 {
	0% {
		left: -70px;
	}

	50% {
		left: -110px;
	}

	100% {
		left: -70px;
	}
}

@keyframes ani1 {
	0% {
		left: -70px;
	}

	50% {
		left: -110px;
	}

	100% {
		left: -70px;
	}
}
@-webkit-keyframes ani2 {
	0% {
		left: 400px
	}

	30% {
		left: 420px
	}

	60% {
		left: 410px
	}

	100% {
		left: 400px
	}
}

@keyframes ani2 {
	0% {
		left: 400px
	}

	30% {
		left: 420px
	}

	60% {
		left: 410px
	}

	100% {
		left: 400px
	}
}
@-webkit-keyframes sloganAni {
	0% {
		-webkit-transform: scale(1.8);
		opacity: 0;
	}

	60% {
		-webkit-transform: scale(1);
		opacity: 1;
	}

	65% {
		-webkit-transform: translate(-4px, -4px);
	}

	70% {
		-webkit-transform: translate(0, 0);
	}

	75% {
		-webkit-transform: translate(4px, 4px);
	}

	80% {
		-webkit-transform: translate(0, 0);
	}

	85% {
		-webkit-transform: translate(-4px, 4px);
	}

	90% {
		-webkit-transform: translate(0, 0);
	}

	95% {
		-webkit-transform: translate(4px, -4px);
	}

	100% {
		-webkit-transform: translate(0, 0);
		opacity: 1;
	}
}

@keyframes sloganAni {
	0% {
		transform: scale(1.8);
		opacity: 0;
	}

	60% {
		transform: scale(1);
		opacity: 1;
	}

	65% {
		transform: translate(-4px, -4px);
	}

	70% {
		transform: translate(0, 0);
	}

	75% {
		transform: translate(4px, 4px);
	}

	80% {
		transform: translate(0, 0);
	}

	85% {
		transform: translate(-4px, 4px);
	}

	90% {
		transform: translate(0, 0);
	}

	95% {
		transform: translate(4px, -4px);
	}

	100% {
		transform: translate(0, 0);
		opacity: 1;
	}
}