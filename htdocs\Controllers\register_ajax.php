<?php
// B<PERSON>t hiển thị lỗi để debug
ini_set('display_errors', 0); // Tắt hiển thị lỗi cho AJAX
ini_set('display_startup_errors', 0);
error_reporting(E_ALL);

// Tạo file log riêng cho Register.php
$logFile = 'register_debug.log';
function debugLog($message)
{
    global $logFile;
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents($logFile, "[$timestamp] $message\n", FILE_APPEND);
}

debugLog("register_ajax.php started");

// Kết nối đến cơ sở dữ liệu (Header.php sẽ gọi session_start())
require_once '/Header.php';

// Biến toàn cục
$_AuthLog = 0; // 0: Cho phép đăng ký, 1: <PERSON><PERSON>o trì đăng ký

// Đ<PERSON><PERSON> bảo đây là AJAX request
header('Content-Type: application/json');

// Hàm mã hóa mật khẩu
function sqlPassword($input)
{
    debugLog("Hashing password with SHA-256");
    $pass = strtoupper(sha1(sha1($input, true)));
    return '*' . $pass;
}

// Hàm kiểm tra đầu vào hợp lệ
function isValidInput($input)
{
    debugLog("Validating input: " . substr($input, 0, 3) . "...");
    return preg_match('/^[a-zA-Z0-9]+$/', $input);
}

// Hàm kiểm tra tên đăng nhập đã tồn tại chưa
function checkExistingUsername($conn, $Username)
{
    debugLog("Checking if username exists: $Username");
    try {
        $stmt = $conn->prepare("SELECT COUNT(*) FROM team_user WHERE username = ?");
        $stmt->execute([$Username]);
        $result = $stmt->fetchColumn() > 0;
        debugLog("Username exists: " . ($result ? "Yes" : "No"));
        return $result;
    } catch (Exception $e) {
        debugLog("Error checking username: " . $e->getMessage());
        return false;
    }
}

// Hàm thêm tài khoản mới
function insertAccount($conn, $Username, $Password, $Email = '', $Phone = '')
{
    debugLog("Inserting new account: Username=$Username, Email=$Email, Phone=$Phone");
    try {
        // Tạo câu lệnh SQL phù hợp với cấu trúc bảng team_user
        $sql = "INSERT INTO team_user (username, password, email, phone, regdate, ban, provider, fromgame)
                VALUES (?, ?, ?, ?, NOW(), 0, 'local', 'KPAH')";

        debugLog("SQL: $sql");
        $stmt = $conn->prepare($sql);
        $result = $stmt->execute([$Username, $Password, $Email, $Phone]);

        if ($result) {
            $lastId = $conn->lastInsertId();
            debugLog("Account inserted successfully. ID: $lastId");
        } else {
            $errorInfo = $stmt->errorInfo();
            debugLog("Failed to insert account. Error: " . implode(", ", $errorInfo));
        }

        return $result;
    } catch (Exception $e) {
        debugLog("Error inserting account: " . $e->getMessage());
        return false;
    }
}

// Kiểm tra token bảo mật
if (!isset($_SESSION['checkreg'])) {
    $_SESSION['checkreg'] = md5(rand(10000, 99999));
}

// Xử lý đăng ký
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    debugLog("POST request received: " . json_encode($_POST));

    if (!isset($_POST['checkreg']) || $_POST['checkreg'] != $_SESSION['checkreg']) {
        // Token không hợp lệ
        $_SESSION['checkreg'] = md5(rand(10000, 99999));
        echo json_encode(['success' => false, 'message' => 'Yêu cầu không hợp lệ.']);
        exit();
    }

    // Token hợp lệ, tiếp tục xử lý
    $_SESSION['checkreg'] = md5(rand(10000, 99999));

    // Lấy dữ liệu từ form
    $Username = isset($_POST["username"]) ? htmlspecialchars(trim($_POST["username"]), ENT_QUOTES, 'UTF-8') : '';
    $Password = isset($_POST["password"]) ? htmlspecialchars(trim($_POST["password"]), ENT_QUOTES, 'UTF-8') : '';
    $RePassword = isset($_POST["repassword"]) ? htmlspecialchars(trim($_POST["repassword"]), ENT_QUOTES, 'UTF-8') : '';

    // Lấy email và phone nếu có
    $Email = isset($_POST["email"]) ? htmlspecialchars(trim($_POST["email"]), ENT_QUOTES, 'UTF-8') : '';
    $Phone = isset($_POST["phone"]) ? htmlspecialchars(trim($_POST["phone"]), ENT_QUOTES, 'UTF-8') : '';

    debugLog("Processing registration: Username=$Username, Email=$Email, Phone=$Phone");

    // Kiểm tra các điều kiện
    if ($_AuthLog == 1) {
        echo json_encode(['success' => false, 'message' => 'Đang bảo trì đăng nhập, vui lòng thử lại sau!']);
        exit();
    }

    if (preg_match('/[A-Z]/', $Username)) {
        echo json_encode(['success' => false, 'message' => 'Tên đăng nhập không được chứa ký tự viết hoa.']);
        exit();
    }

    if (!isValidInput($Username) || !isValidInput($Password)) {
        echo json_encode(['success' => false, 'message' => 'Tên đăng nhập và mật khẩu không được chứa kí tự đặc biệt.']);
        exit();
    }

    if ($Password !== $RePassword) {
        echo json_encode(['success' => false, 'message' => 'Mật khẩu nhập lại không khớp!']);
        exit();
    }

    // Kiểm tra tên đăng nhập đã tồn tại chưa
    if (checkExistingUsername($conn, $Username)) {
        echo json_encode(['success' => false, 'message' => 'Tài khoản đã tồn tại.']);
        exit();
    }

    // Thêm tài khoản mới
    if (insertAccount($conn, $Username, sqlPassword($Password), $Email, $Phone)) {
        // Thêm bản ghi vào bảng 5h_active để kích hoạt tài khoản
        try {
            $userID = $conn->lastInsertId();
            debugLog("Account created with ID: $userID. Attempting to activate account.");

            // Kết nối đến cơ sở dữ liệu game_db
            try {
                // Sử dụng cấu hình từ Header.php
                global $config2;
                $conn1 = new PDO("mysql:host={$config2['host']};dbname={$config2['database']};charset=utf8", $config2['username'], $config2['password']);
                $conn1->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                debugLog("Connected to game_db successfully");
            } catch (PDOException $e) {
                debugLog("Error connecting to game_db: " . $e->getMessage());
            }

            if (isset($conn1)) {
                $query = "INSERT INTO 5h_active (userID, username, time_end) VALUES (:userID, :username, 0)";
                $stmt = $conn1->prepare($query);
                $stmt->bindParam(":userID", $userID, PDO::PARAM_INT);
                $stmt->bindParam(":username", $Username, PDO::PARAM_STR);
                $stmt->execute();
                debugLog("Added user to 5h_active table");
            } else {
                debugLog("conn1 is not defined, cannot add user to 5h_active table");
            }
        } catch (Exception $e) {
            debugLog("Error activating account: " . $e->getMessage());
        }

        echo json_encode(['success' => true, 'message' => 'Đăng ký thành công!']);
        exit();
    } else {
        echo json_encode(['success' => false, 'message' => 'Đăng ký thất bại.']);
        exit();
    }
} else {
    // Không phải POST request
    echo json_encode(['success' => false, 'message' => 'Phương thức không hợp lệ.']);
    exit();
}
