<?php
// Include database configuration
require_once __DIR__ . '/database.php';

// Function to log debug messages
if (!function_exists('debugLog')) {
    function debugLog($message) {
        error_log("[PASSWORD_RESET] " . $message);
    }
}

try {
    // Connect to account database
    $conn = connectPDO($config1);
    
    // Create password_reset_tokens table if it doesn't exist
    $conn->exec("CREATE TABLE IF NOT EXISTS password_reset_tokens (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        token VARCHAR(255) NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        expires_at DATETIME NOT NULL,
        used TINYINT(1) DEFAULT 0,
        FOREIGN KEY (user_id) REFERENCES team_user(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
    
    debugLog("Password reset tokens table created successfully");
    echo "Password reset tokens table created successfully";
} catch (PDOException $e) {
    debugLog("Error creating password reset tokens table: " . $e->getMessage());
    echo "Error creating password reset tokens table: " . $e->getMessage();
}
?>
