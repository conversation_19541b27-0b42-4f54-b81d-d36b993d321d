// JavaScript Document


$(document).ready(function () {
  var h = $(window).height();
  var w = $(window).width();
  if (w > 960) {
    $('.wrapper').css({ transform: "scale(" + w / 2000 + ")" });
    var iw = $(".wrapper")[0].getBoundingClientRect().width;
    var ih = $(".wrapper")[0].getBoundingClientRect().height;
    $('body').css({ height: ih, width: iw });

  }

  else if (320 < w < 960) {
    $('.wrapper').css({ transform: "scale(" + w / 960 + ")" });
    var iw = $(".wrapper")[0].getBoundingClientRect().width;
    var ih = $(".wrapper")[0].getBoundingClientRect().height;
    $('body').css({ height: ih, width: iw });
  }

  $(window).on('resize', function () {
    var h = $(window).height();
    var w = $(window).width();
    if (w > 960) {
      $('.wrapper').css({ transform: "scale(" + w / 2000 + ")" });
      var iw = $(".wrapper")[0].getBoundingClientRect().width;
      var ih = $(".wrapper")[0].getBoundingClientRect().height;
      $('body').css({ height: ih, width: iw });

    }
    else if (320 < w < 960) {
      $('.wrapper').css({ transform: "scale(" + w / 960 + ")" });
      var iw = $(".wrapper")[0].getBoundingClientRect().width;
      var ih = $(".wrapper")[0].getBoundingClientRect().height;
      $('body').css({ height: ih, width: iw });
    }


  });

});
