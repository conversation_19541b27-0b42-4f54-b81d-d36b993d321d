<?php
// Bật hiển thị lỗi để debug
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Hàm ghi log debug
if (!function_exists('debugLog')) {
    function debugLog($message)
    {
        // Sử dụng error_log thay vì ghi vào file riêng
        error_log("[ACCOUNT] " . $message);
    }
}

// Khởi động session nếu chưa được khởi động
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

/**
 * Class AccountManager
 * Quản lý các chức năng liên quan đến tài khoản người dùng
 */
class AccountManager
{
    private $conn; // Kết nối đến database account
    private $conn1; // Kết nối đến database game_db

    /**
     * Khởi tạo AccountManager
     *
     * @param PDO $conn Kết nối đến database account
     * @param PDO $conn1 Kết nối đến database game_db
     */
    public function __construct($conn, $conn1 = null)
    {
        $this->conn = $conn;
        $this->conn1 = $conn1;

        // Khởi tạo các bảng cần thiết
        $this->initializeTables();
    }

    /**
     * Khởi tạo các bảng cần thiết nếu chưa tồn tại
     */
    private function initializeTables()
    {
        try {
            // Kiểm tra và tạo bảng bank_transactions nếu chưa tồn tại
            $this->conn->exec("CREATE TABLE IF NOT EXISTS bank_transactions (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                bank_account_id INT NOT NULL,
                amount DECIMAL(10,2) NOT NULL,
                xu_amount INT NOT NULL,
                transaction_code VARCHAR(255),
                user_account VARCHAR(50),
                status ENUM('pending', 'approved', 'rejected') NOT NULL DEFAULT 'pending',
                notes TEXT,
                transaction_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )");

            // Kiểm tra và tạo bảng bank_accounts nếu chưa tồn tại
            $this->conn->exec("CREATE TABLE IF NOT EXISTS bank_accounts (
                id INT AUTO_INCREMENT PRIMARY KEY,
                bank_name VARCHAR(255) NOT NULL,
                account_name VARCHAR(255) NOT NULL,
                account_number VARCHAR(50) NOT NULL,
                is_active TINYINT(1) NOT NULL DEFAULT 1,
                qr_code_url VARCHAR(255),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )");

            // Kiểm tra và tạo bảng transaction_log nếu chưa tồn tại
            $this->conn->exec("CREATE TABLE IF NOT EXISTS transaction_log (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                amount DECIMAL(10,2) NOT NULL,
                type VARCHAR(50) NOT NULL,
                description TEXT,
                transaction_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )");

            // Thêm một số tài khoản ngân hàng mặc định nếu bảng trống
            $stmt = $this->conn->query("SELECT COUNT(*) FROM bank_accounts");
            $count = $stmt->fetchColumn();

            if ($count == 0) {
                $this->conn->exec("INSERT INTO bank_accounts (bank_name, account_name, account_number, is_active) VALUES
                    ('Vietcombank', 'NGUYEN VAN A', '**********', 1),
                    ('Techcombank', 'NGUYEN VAN A', '**********', 1),
                    ('MBBank', 'NGUYEN VAN A', '**********', 1)
                ");
            }

            debugLog("Tables initialized successfully");
        } catch (PDOException $e) {
            debugLog("Error initializing tables: " . $e->getMessage());
        }
    }

    /**
     * Lấy thông tin chi tiết của người dùng
     *
     * @param int $userId ID của người dùng
     * @return array Thông tin chi tiết của người dùng
     */
    public function getUserDetails($userId)
    {
        try {
            // Lấy thông tin người dùng từ bảng team_user
            $stmt = $this->conn->prepare("SELECT * FROM team_user WHERE id = :userId");
            $stmt->bindParam(':userId', $userId, PDO::PARAM_INT);
            $stmt->execute();
            $user = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$user) {
                debugLog("User not found with ID: $userId");
                return [
                    'user' => [],
                    'activation' => ['status' => 'Không xác định', 'time_end' => 0],
                    'character' => false
                ];
            }

            // Lấy thông tin kích hoạt từ bảng 5h_active
            $activation = $this->getActivationStatus($userId, $user['username']);

            // Lấy thông tin nhân vật từ bảng tob_char
            $character = $this->getCharacterInfo($userId, $user['username']);

            // Trả về thông tin chi tiết
            return [
                'user' => $user,
                'activation' => $activation,
                'character' => $character
            ];
        } catch (PDOException $e) {
            debugLog("Error getting user details: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Lấy trạng thái kích hoạt của người dùng
     *
     * @param int $userId ID của người dùng
     * @param string $username Tên đăng nhập của người dùng
     * @return array Thông tin kích hoạt
     */
    private function getActivationStatus($userId, $username)
    {
        try {
            $stmt = $this->conn1->prepare("SELECT * FROM 5h_active WHERE userID = :userId OR username = :username");
            $stmt->bindParam(':userId', $userId, PDO::PARAM_INT);
            $stmt->bindParam(':username', $username, PDO::PARAM_STR);
            $stmt->execute();
            $activation = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$activation) {
                return [
                    'status' => 'Chưa kích hoạt',
                    'time_end' => 0
                ];
            }

            // Xác định trạng thái kích hoạt
            $status = 'Chưa kích hoạt';
            if ($activation['time_end'] == -1) {
                $status = 'Đã kích hoạt';
            } elseif ($activation['time_end'] > 0) {
                $status = 'Kích hoạt tạm thời (hết hạn: ' . date('d/m/Y H:i', $activation['time_end']) . ')';
            }

            return [
                'status' => $status,
                'time_end' => $activation['time_end']
            ];
        } catch (PDOException $e) {
            debugLog("Error getting activation status: " . $e->getMessage());
            return [
                'status' => 'Không xác định',
                'time_end' => 0
            ];
        }
    }

    /**
     * Lấy thông tin nhân vật của người dùng
     *
     * @param int $userId ID của người dùng
     * @param string $username Tên đăng nhập của người dùng
     * @return array|false Thông tin nhân vật hoặc false nếu không tìm thấy
     */
    private function getCharacterInfo($userId, $username)
    {
        try {
            // Kiểm tra xem bảng tob_char có tồn tại không
            $tableCheck = $this->conn1->query("SHOW TABLES LIKE 'tob_char'");
            if ($tableCheck->rowCount() == 0) {
                debugLog("Table tob_char does not exist");
                return false;
            }

            // Lấy thông tin nhân vật từ bảng tob_char
            $stmt = $this->conn1->prepare("SELECT * FROM tob_char WHERE id = :userId");
            $stmt->bindParam(':userId', $userId, PDO::PARAM_INT);
            $stmt->execute();
            $character = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$character) {
                debugLog("Character not found for user ID: $userId");
                return false;
            }

            // Thêm các thông tin khác nếu có
            $character['lastLv'] = 1; // Mặc định cấp độ 1
            $character['gold'] = 0; // Mặc định gold 0
            $character['topNap'] = 0; // Mặc định top nạp 0

            return $character;
        } catch (PDOException $e) {
            debugLog("Error getting character info: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Kiểm tra xem người dùng có phải là admin không
     *
     * @return bool True nếu là admin, ngược lại là false
     */
    public function isAdmin()
    {
        return isset($_SESSION['is_admin']) && $_SESSION['is_admin'];
    }

    /**
     * Cập nhật email cho người dùng
     *
     * @param int $userId ID của người dùng
     * @param string $email Email mới
     * @return bool True nếu thành công, ngược lại là false
     */
    public function updateEmail($userId, $email)
    {
        try {
            // Kiểm tra email hợp lệ
            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                debugLog("Invalid email format: $email");
                return false;
            }

            // Cập nhật email trong bảng team_user
            $stmt = $this->conn->prepare("UPDATE team_user SET email = :email WHERE id = :userId");
            $stmt->bindParam(':email', $email, PDO::PARAM_STR);
            $stmt->bindParam(':userId', $userId, PDO::PARAM_INT);
            $result = $stmt->execute();

            if ($result) {
                debugLog("Email updated successfully for user ID: $userId");
                return true;
            } else {
                debugLog("Failed to update email for user ID: $userId");
                return false;
            }
        } catch (PDOException $e) {
            debugLog("Error updating email: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Xử lý nạp thẻ
     *
     * @param int $userId ID của người dùng
     * @param string $cardType Loại thẻ
     * @param string $cardNumber Mã thẻ
     * @param string $cardSerial Số serial thẻ
     * @param float $amount Mệnh giá thẻ
     * @return array Kết quả xử lý ['success' => bool, 'message' => string, 'transaction_id' => string]
     */
    public function processCardTopup($userId, $cardType, $cardNumber, $cardSerial, $amount)
    {
        try {
            debugLog("Processing card topup for user ID: $userId, Card Type: $cardType, Amount: $amount");

            // Bắt đầu transaction để đảm bảo tính toàn vẹn dữ liệu
            $this->conn->beginTransaction();

            // Kiểm tra thông tin thẻ
            if (empty($cardType) || empty($cardNumber) || empty($cardSerial) || empty($amount)) {
                debugLog("Card information is incomplete");
                return ['success' => false, 'message' => 'Thông tin thẻ không đầy đủ.'];
            }

            // Kiểm tra xem bảng card_transactions có tồn tại không
            $tableCheck = $this->conn->query("SHOW TABLES LIKE 'card_transactions'");
            if ($tableCheck->rowCount() == 0) {
                // Tạo bảng card_transactions nếu chưa tồn tại
                $this->conn->exec("CREATE TABLE IF NOT EXISTS card_transactions (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    card_number VARCHAR(50) NOT NULL,
                    card_serial VARCHAR(50) NOT NULL,
                    card_type VARCHAR(50) NOT NULL,
                    amount DECIMAL(10,2) NOT NULL,
                    xu_amount INT NOT NULL DEFAULT 0,
                    status VARCHAR(20) NOT NULL DEFAULT 'pending',
                    message TEXT,
                    transaction_id VARCHAR(100),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
            }

            // Kiểm tra xem bảng card_rates có tồn tại không
            $tableCheck = $this->conn->query("SHOW TABLES LIKE 'card_rates'");
            if ($tableCheck->rowCount() == 0) {
                // Tạo bảng card_rates nếu chưa tồn tại
                $this->conn->exec("CREATE TABLE IF NOT EXISTS card_rates (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    card_type VARCHAR(50) NOT NULL,
                    amount INT NOT NULL,
                    rate FLOAT NOT NULL DEFAULT 1.0,
                    xu_rate INT NOT NULL DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    UNIQUE KEY card_amount_unique (card_type, amount)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

                // Thêm tỷ lệ mặc định
                $cardTypes = ['VIETTEL', 'MOBIFONE', 'VINAPHONE', 'VIETNAMOBILE', 'ZING', 'GATE'];
                $amounts = [10000, 20000, 30000, 50000, 100000, 200000, 300000, 500000, 1000000];

                $insertSql = "INSERT INTO card_rates (card_type, amount, rate, xu_rate) VALUES (?, ?, ?, ?)";
                $stmt = $this->conn->prepare($insertSql);

                foreach ($cardTypes as $type) {
                    foreach ($amounts as $amt) {
                        $rate = 0.8; // Tỷ lệ mặc định 80%
                        $xuRate = floor($amt * $rate);
                        $stmt->execute([$type, $amt, $rate, $xuRate]);
                    }
                }
            }

            // Kiểm tra xem bảng card_api_config có tồn tại không
            $tableCheck = $this->conn->query("SHOW TABLES LIKE 'card_api_config'");
            if ($tableCheck->rowCount() == 0) {
                // Tạo bảng card_api_config nếu chưa tồn tại
                $this->conn->exec("CREATE TABLE IF NOT EXISTS card_api_config (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    api_name VARCHAR(50) NOT NULL,
                    api_url VARCHAR(255) NOT NULL,
                    merchant_id VARCHAR(100),
                    api_key VARCHAR(255),
                    secret_key VARCHAR(255),
                    callback_url VARCHAR(255),
                    is_active TINYINT(1) NOT NULL DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

                // Thêm cấu hình API mặc định
                $insertSql = "INSERT INTO card_api_config (api_name, api_url, merchant_id, api_key, secret_key, callback_url, is_active)
                              VALUES ('CARDVIP', 'https://api.cardvip.vn/api/createExchange', '', '', '', 'https://yourdomain.com/api/card_callback.php', 1)";
                $this->conn->exec($insertSql);
            }

            // Kiểm tra xem thẻ đã được sử dụng chưa
            $checkCard = $this->conn->prepare("SELECT id FROM card_transactions WHERE card_number = :cardNumber AND card_serial = :cardSerial");
            $checkCard->bindParam(':cardNumber', $cardNumber, PDO::PARAM_STR);
            $checkCard->bindParam(':cardSerial', $cardSerial, PDO::PARAM_STR);
            $checkCard->execute();

            if ($checkCard->rowCount() > 0) {
                debugLog("Card already used: Number=$cardNumber, Serial=$cardSerial");
                return ['success' => false, 'message' => 'Thẻ này đã được sử dụng.'];
            }

            // Lấy thông tin người dùng
            $stmt = $this->conn->prepare("SELECT username FROM team_user WHERE id = :userId");
            $stmt->bindParam(':userId', $userId, PDO::PARAM_INT);
            $stmt->execute();
            $user = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$user) {
                debugLog("User not found for ID: $userId");
                return ['success' => false, 'message' => 'Không tìm thấy thông tin người dùng.'];
            }

            // Lấy tỷ lệ quy đổi xu cho loại thẻ và mệnh giá
            $stmt = $this->conn->prepare("SELECT rate, xu_rate FROM card_rates WHERE card_type = :card_type AND amount = :amount");
            $stmt->bindParam(':card_type', $cardType, PDO::PARAM_STR);
            $stmt->bindParam(':amount', $amount, PDO::PARAM_INT);
            $stmt->execute();
            $rateInfo = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$rateInfo) {
                debugLog("Card rate not found for type: $cardType, amount: $amount");
                // Sử dụng tỷ lệ mặc định 80%
                $rate = 0.8;
                $xuAmount = floor($amount * $rate);
            } else {
                $rate = $rateInfo['rate'];
                $xuAmount = $rateInfo['xu_rate'];
            }

            debugLog("Card rate: $rate, Xu amount: $xuAmount");

            // Tạo transaction ID duy nhất
            $transactionId = uniqid('CARD_') . '_' . $userId;

            // Thêm giao dịch mới
            $stmt = $this->conn->prepare("INSERT INTO card_transactions
                (user_id, card_number, card_serial, card_type, amount, xu_amount, status, transaction_id)
                VALUES (:userId, :cardNumber, :cardSerial, :cardType, :amount, :xu_amount, 'pending', :transaction_id)");
            $stmt->bindParam(':userId', $userId, PDO::PARAM_INT);
            $stmt->bindParam(':cardNumber', $cardNumber, PDO::PARAM_STR);
            $stmt->bindParam(':cardSerial', $cardSerial, PDO::PARAM_STR);
            $stmt->bindParam(':cardType', $cardType, PDO::PARAM_STR);
            $stmt->bindParam(':amount', $amount, PDO::PARAM_INT);
            $stmt->bindParam(':xu_amount', $xuAmount, PDO::PARAM_INT);
            $stmt->bindParam(':transaction_id', $transactionId, PDO::PARAM_STR);
            $result = $stmt->execute();

            if (!$result) {
                debugLog("Failed to insert card transaction");
                return ['success' => false, 'message' => 'Lỗi khi lưu thông tin giao dịch.'];
            }

            // Lấy ID của giao dịch vừa thêm
            $transactionDbId = $this->conn->lastInsertId();
            debugLog("Card transaction saved with ID: $transactionDbId");

            // Lấy cấu hình API
            $stmt = $this->conn->prepare("SELECT * FROM card_api_config WHERE is_active = 1 LIMIT 1");
            $stmt->execute();
            $apiConfig = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$apiConfig) {
                // Nếu không có cấu hình API, chỉ lưu giao dịch và để admin xử lý thủ công
                $this->conn->commit();
                debugLog("No active API config found. Transaction will be processed manually.");
                return [
                    'success' => true,
                    'message' => 'Yêu cầu nạp thẻ đã được gửi. Admin sẽ xử lý trong thời gian sớm nhất.',
                    'transaction_id' => $transactionId
                ];
            }

            // Gọi API nạp thẻ
            $apiResult = $this->callCardApi($apiConfig, $cardType, $cardNumber, $cardSerial, $amount, $transactionId);

            if ($apiResult['success']) {
                // Cập nhật trạng thái giao dịch
                $stmt = $this->conn->prepare("UPDATE card_transactions SET message = :message WHERE id = :id");
                $stmt->bindParam(':message', $apiResult['message'], PDO::PARAM_STR);
                $stmt->bindParam(':id', $transactionDbId, PDO::PARAM_INT);
                $stmt->execute();

                // Commit transaction
                $this->conn->commit();

                debugLog("API call successful: " . json_encode($apiResult));
                return [
                    'success' => true,
                    'message' => 'Yêu cầu nạp thẻ đã được gửi. Hệ thống sẽ xử lý tự động và cập nhật kết quả sau vài phút.',
                    'transaction_id' => $transactionId
                ];
            } else {
                // Cập nhật trạng thái giao dịch thành thất bại
                $stmt = $this->conn->prepare("UPDATE card_transactions SET status = 'failed', message = :message WHERE id = :id");
                $stmt->bindParam(':message', $apiResult['message'], PDO::PARAM_STR);
                $stmt->bindParam(':id', $transactionDbId, PDO::PARAM_INT);
                $stmt->execute();

                // Commit transaction
                $this->conn->commit();

                debugLog("API call failed: " . $apiResult['message']);
                return [
                    'success' => false,
                    'message' => 'Lỗi khi gọi API nạp thẻ: ' . $apiResult['message'],
                    'transaction_id' => $transactionId
                ];
            }
        } catch (PDOException $e) {
            // Rollback transaction on error
            if ($this->conn->inTransaction()) {
                $this->conn->rollBack();
            }
            debugLog("Error processing card topup: " . $e->getMessage());
            return ['success' => false, 'message' => 'Lỗi hệ thống: ' . $e->getMessage()];
        } catch (Exception $e) {
            // Rollback transaction on error
            if ($this->conn->inTransaction()) {
                $this->conn->rollBack();
            }
            debugLog("General error in processCardTopup: " . $e->getMessage());
            return ['success' => false, 'message' => 'Lỗi hệ thống: ' . $e->getMessage()];
        }
    }

    /**
     * Gọi API nạp thẻ
     *
     * @param array $apiConfig Cấu hình API
     * @param string $cardType Loại thẻ
     * @param string $cardNumber Mã thẻ
     * @param string $cardSerial Số serial
     * @param float $amount Mệnh giá thẻ
     * @param string $transactionId Mã giao dịch
     * @return array Kết quả gọi API ['success' => bool, 'message' => string, 'data' => array]
     */
    private function callCardApi($apiConfig, $cardType, $cardNumber, $cardSerial, $amount, $transactionId)
    {
        try {
            debugLog("Calling card API for transaction: $transactionId");

            // Tạo dữ liệu gửi đi tùy theo nhà cung cấp API
            if ($apiConfig['api_name'] == 'THESIEURE') {
                // Chuyển đổi tên loại thẻ cho phù hợp với TheSieuRe.com
                $telcoMap = [
                    'VIETTEL' => 'VIETTEL',
                    'MOBIFONE' => 'MOBIFONE',
                    'VINAPHONE' => 'VINAPHONE',
                    'VIETNAMOBILE' => 'VNMOBILE',
                    'ZING' => 'ZING',
                    'GATE' => 'GATE'
                ];

                $telco = isset($telcoMap[$cardType]) ? $telcoMap[$cardType] : $cardType;

                // Tạo dữ liệu cho TheSieuRe.com API
                $params = [
                    'telco' => $telco,
                    'code' => $cardNumber,
                    'serial' => $cardSerial,
                    'amount' => $amount,
                    'request_id' => $transactionId,
                    'partner_id' => $apiConfig['merchant_id'],
                    'command' => 'charging'
                ];

                // Tạo chữ ký theo tài liệu mới
                // Thứ tự mã hóa chữ ký: md5(partner_key + partner_id + command + code + serial + telco + amount + request_id)
                $signData = $apiConfig['secret_key'] . $params['partner_id'] . $params['command'] .
                    $params['code'] . $params['serial'] . $params['telco'] . $params['amount'] . $params['request_id'];
                $params['sign'] = md5($signData);

                debugLog("TheSieuRe API request data: " . json_encode($params));

                // Xác định phương thức request (GET hoặc POST)
                $requestMethod = isset($apiConfig['request_method']) ? strtoupper($apiConfig['request_method']) : 'POST';

                if ($requestMethod == 'GET') {
                    // Sử dụng phương thức GET
                    $apiUrl = $apiConfig['api_url'] . '?' . http_build_query($params);
                    $ch = curl_init($apiUrl);
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                    debugLog("TheSieuRe API GET URL: $apiUrl");
                } else {
                    // Sử dụng phương thức POST
                    $ch = curl_init($apiConfig['api_url']);
                    curl_setopt($ch, CURLOPT_POST, 1);
                    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($params)); // Sử dụng JSON thay vì form data
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                    curl_setopt($ch, CURLOPT_HTTPHEADER, [
                        'Content-Type: application/json',
                        'Accept: application/json'
                    ]);
                }

                $response = curl_exec($ch);
                $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                $error = curl_error($ch);
                curl_close($ch);

                debugLog("TheSieuRe API response: $response, HTTP code: $httpCode, Error: $error");

                if ($error) {
                    return ['success' => false, 'message' => 'Lỗi kết nối API: ' . $error];
                }

                if ($httpCode != 200) {
                    return ['success' => false, 'message' => 'API trả về mã lỗi: ' . $httpCode];
                }

                $responseData = json_decode($response, true);

                if (!$responseData) {
                    return ['success' => false, 'message' => 'Không thể phân tích phản hồi từ API'];
                }

                // Xử lý kết quả từ API TheSieuRe
                if (isset($responseData['status']) && $responseData['status'] == 'success') {
                    return ['success' => true, 'message' => 'Yêu cầu nạp thẻ đã được gửi thành công', 'data' => $responseData];
                } else {
                    $errorMessage = isset($responseData['message']) ? $responseData['message'] : 'Lỗi không xác định từ API';
                    return ['success' => false, 'message' => $errorMessage, 'data' => $responseData];
                }
            }
            // Hỗ trợ cả CardVIP API cũ để tương thích ngược
            else if ($apiConfig['api_name'] == 'CARDVIP') {
                $postData = [
                    'merchant_id' => $apiConfig['merchant_id'],
                    'api_key' => $apiConfig['api_key'],
                    'card_type' => $cardType,
                    'card_code' => $cardNumber,
                    'card_serial' => $cardSerial,
                    'card_amount' => $amount,
                    'request_id' => $transactionId,
                    'callback_url' => $apiConfig['callback_url']
                ];

                // Tạo chữ ký
                $signData = $postData['merchant_id'] . $postData['api_key'] . $postData['card_type'] .
                    $postData['card_code'] . $postData['card_serial'] . $postData['card_amount'] .
                    $postData['request_id'] . $apiConfig['secret_key'];
                $postData['signature'] = md5($signData);

                debugLog("CardVIP API request data: " . json_encode($postData));

                // Gọi API
                $ch = curl_init($apiConfig['api_url']);
                curl_setopt($ch, CURLOPT_POST, 1);
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($postData));
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_HTTPHEADER, [
                    'Content-Type: application/json',
                    'Accept: application/json'
                ]);

                $response = curl_exec($ch);
                $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                $error = curl_error($ch);
                curl_close($ch);

                debugLog("CardVIP API response: $response, HTTP code: $httpCode, Error: $error");

                if ($error) {
                    return ['success' => false, 'message' => 'Lỗi kết nối API: ' . $error];
                }

                if ($httpCode != 200) {
                    return ['success' => false, 'message' => 'API trả về mã lỗi: ' . $httpCode];
                }

                $responseData = json_decode($response, true);

                if (!$responseData) {
                    return ['success' => false, 'message' => 'Không thể phân tích phản hồi từ API'];
                }

                // Xử lý kết quả từ API
                if (isset($responseData['status']) && $responseData['status'] == 'success') {
                    return ['success' => true, 'message' => 'Yêu cầu nạp thẻ đã được gửi thành công', 'data' => $responseData];
                } else {
                    $errorMessage = isset($responseData['message']) ? $responseData['message'] : 'Lỗi không xác định từ API';
                    return ['success' => false, 'message' => $errorMessage, 'data' => $responseData];
                }
            } else {
                // Xử lý cho các API khác
                return ['success' => false, 'message' => 'API không được hỗ trợ: ' . $apiConfig['api_name']];
            }
        } catch (Exception $e) {
            debugLog("Error calling card API: " . $e->getMessage());
            return ['success' => false, 'message' => 'Lỗi khi gọi API: ' . $e->getMessage()];
        }
    }

    /**
     * Xử lý callback từ API nạp thẻ
     *
     * @param array $data Dữ liệu callback từ API
     * @return bool Kết quả xử lý
     */
    public function processCardCallback($data)
    {
        try {
            debugLog("Processing card callback: " . json_encode($data));

            // Kiểm tra xem dữ liệu callback là từ TheSieuRe hay CardVIP
            $isTheSieuRe = isset($data['status_code']);

            if ($isTheSieuRe) {
                // Xử lý callback từ TheSieuRe.com
                if (!isset($data['request_id']) || !isset($data['status_code'])) {
                    debugLog("Invalid TheSieuRe callback data: missing required fields");
                    return false;
                }

                $transactionId = $data['request_id'];
                $statusCode = $data['status_code'];
                $amount = isset($data['value']) ? $data['value'] : 0;
                $message = isset($data['message']) ? $data['message'] : '';

                // Chuyển đổi mã trạng thái của TheSieuRe sang trạng thái của hệ thống
                if ($statusCode == 1) {
                    $status = 'success'; // Thẻ đúng
                } elseif ($statusCode == 2) {
                    $status = 'failed'; // Thẻ sai
                } elseif ($statusCode == 3) {
                    $status = 'failed'; // Thẻ đã được sử dụng
                } elseif ($statusCode == 4) {
                    $status = 'failed'; // Thẻ bị khóa
                } elseif ($statusCode == 99) {
                    $status = 'pending'; // Đang xử lý
                } else {
                    $status = 'failed'; // Các trường hợp khác
                }
            } else {
                // Xử lý callback từ CardVIP (tương thích ngược)
                if (!isset($data['request_id']) || !isset($data['status'])) {
                    debugLog("Invalid CardVIP callback data: missing required fields");
                    return false;
                }

                $transactionId = $data['request_id'];
                $status = $data['status'];
                $amount = isset($data['real_amount']) ? $data['real_amount'] : (isset($data['amount']) ? $data['amount'] : 0);
                $message = isset($data['message']) ? $data['message'] : '';
            }

            // Tìm giao dịch trong database
            $stmt = $this->conn->prepare("SELECT * FROM card_transactions WHERE transaction_id = :transaction_id LIMIT 1");
            $stmt->bindParam(':transaction_id', $transactionId, PDO::PARAM_STR);
            $stmt->execute();
            $transaction = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$transaction) {
                debugLog("Transaction not found: $transactionId");
                return false;
            }

            debugLog("Found transaction: " . json_encode($transaction));

            // Cập nhật trạng thái giao dịch
            $newStatus = 'pending';
            $xuAmount = $transaction['xu_amount'];

            if ($status == 'success' || $status == 'approved') {
                $newStatus = 'success';

                // Nếu mệnh giá thực tế khác với mệnh giá đã chọn, tính lại số xu
                if ($amount > 0 && $amount != $transaction['amount']) {
                    // Lấy tỷ lệ quy đổi xu cho mệnh giá thực tế
                    $stmt = $this->conn->prepare("SELECT rate, xu_rate FROM card_rates WHERE card_type = :card_type AND amount = :amount");
                    $stmt->bindParam(':card_type', $transaction['card_type'], PDO::PARAM_STR);
                    $stmt->bindParam(':amount', $amount, PDO::PARAM_INT);
                    $stmt->execute();
                    $rateInfo = $stmt->fetch(PDO::FETCH_ASSOC);

                    if ($rateInfo) {
                        $xuAmount = $rateInfo['xu_rate'];
                    } else {
                        // Nếu không tìm thấy tỷ lệ, tính theo tỷ lệ mặc định (80%)
                        $xuAmount = floor($amount * 0.8);
                    }
                }

                // Cộng xu vào tài khoản người dùng
                $userId = $transaction['user_id'];
                $this->addXuToUser($userId, $xuAmount, "Nạp thẻ $transaction[card_type] mệnh giá " . number_format($amount) . " VNĐ");

                debugLog("Added $xuAmount xu to user $userId");
            } elseif ($status == 'failed' || $status == 'rejected') {
                $newStatus = 'failed';
            }

            // Cập nhật giao dịch trong database
            $stmt = $this->conn->prepare("UPDATE card_transactions SET
                status = :status,
                message = :message,
                amount = CASE WHEN :amount > 0 THEN :amount ELSE amount END,
                xu_amount = :xu_amount
                WHERE transaction_id = :transaction_id");
            $stmt->bindParam(':status', $newStatus, PDO::PARAM_STR);
            $stmt->bindParam(':message', $message, PDO::PARAM_STR);
            $stmt->bindParam(':amount', $amount, PDO::PARAM_INT);
            $stmt->bindParam(':xu_amount', $xuAmount, PDO::PARAM_INT);
            $stmt->bindParam(':transaction_id', $transactionId, PDO::PARAM_STR);
            $result = $stmt->execute();

            debugLog("Updated transaction status to $newStatus, result: " . ($result ? 'success' : 'failed'));

            return $result;
        } catch (PDOException $e) {
            debugLog("Database error in processCardCallback: " . $e->getMessage());
            return false;
        } catch (Exception $e) {
            debugLog("General error in processCardCallback: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Thêm xu vào tài khoản người dùng
     *
     * @param int $userId ID của người dùng
     * @param int $xuAmount Số xu cần thêm
     * @param string $note Ghi chú
     * @return bool Kết quả thêm xu
     */
    private function addXuToUser($userId, $xuAmount, $note = '')
    {
        try {
            // Kiểm tra xem người dùng có trong bảng user_balance không
            $stmt = $this->conn->prepare("SELECT * FROM user_balance WHERE user_id = :user_id");
            $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
            $stmt->execute();
            $userBalance = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($userBalance) {
                // Cập nhật số dư hiện tại
                $stmt = $this->conn->prepare("UPDATE user_balance SET balance = balance + :xu_amount WHERE user_id = :user_id");
                $stmt->bindParam(':xu_amount', $xuAmount, PDO::PARAM_INT);
                $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
                $result = $stmt->execute();
            } else {
                // Tạo bản ghi mới
                $stmt = $this->conn->prepare("INSERT INTO user_balance (user_id, balance) VALUES (:user_id, :xu_amount)");
                $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
                $stmt->bindParam(':xu_amount', $xuAmount, PDO::PARAM_INT);
                $result = $stmt->execute();
            }

            // Ghi lại lịch sử giao dịch
            if ($result) {
                // Kiểm tra xem bảng balance_transactions có tồn tại không
                $tableCheck = $this->conn->query("SHOW TABLES LIKE 'balance_transactions'");
                if ($tableCheck->rowCount() == 0) {
                    // Tạo bảng balance_transactions nếu chưa tồn tại
                    $this->conn->exec("CREATE TABLE IF NOT EXISTS balance_transactions (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        user_id INT NOT NULL,
                        amount INT NOT NULL,
                        transaction_type VARCHAR(50) NOT NULL,
                        description TEXT,
                        status VARCHAR(20) NOT NULL DEFAULT 'completed',
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
                }

                $stmt = $this->conn->prepare("INSERT INTO balance_transactions
                    (user_id, amount, transaction_type, description, status)
                    VALUES (:user_id, :amount, 'card_topup', :description, 'completed')");
                $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
                $stmt->bindParam(':amount', $xuAmount, PDO::PARAM_INT);
                $stmt->bindParam(':description', $note, PDO::PARAM_STR);
                $stmt->execute();
            }

            return $result;
        } catch (PDOException $e) {
            debugLog("Error adding xu to user: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Lấy lịch sử giao dịch thẻ cào của người dùng
     *
     * @param int $userId ID của người dùng
     * @param int $limit Số lượng bản ghi tối đa
     * @return array Danh sách giao dịch
     */
    public function getCardTransactionHistory($userId, $limit = 10)
    {
        try {
            // Kiểm tra xem bảng card_transactions có tồn tại không
            $tableCheck = $this->conn->query("SHOW TABLES LIKE 'card_transactions'");
            if ($tableCheck->rowCount() == 0) {
                debugLog("Table card_transactions does not exist");
                return [];
            }

            $stmt = $this->conn->prepare("SELECT * FROM card_transactions
                WHERE user_id = :user_id
                ORDER BY created_at DESC
                LIMIT :limit");
            $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
            $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
            $stmt->execute();

            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            debugLog("Error getting card transaction history: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Kiểm tra trạng thái giao dịch thẻ cào từ TheSieuRe.com
     *
     * @param array $apiConfig Cấu hình API
     * @param string $transactionId Mã giao dịch
     * @param string $orderCode Mã đơn hàng (nếu có)
     * @return array Kết quả kiểm tra ['success' => bool, 'message' => string, 'data' => array]
     */
    public function checkCardStatus($apiConfig, $transactionId, $orderCode = '')
    {
        try {
            debugLog("Checking card status for transaction: $transactionId");

            // Tạo dữ liệu cho API
            $params = [
                'partner_id' => $apiConfig['merchant_id'],
                'command' => 'getstatus',
                'request_id' => $transactionId
            ];

            // Thêm order_code nếu có
            if (!empty($orderCode)) {
                $params['order_code'] = $orderCode;
            }

            // Tạo chữ ký: md5(partner_key + partner_id + command + request_id)
            $signData = $apiConfig['secret_key'] . $params['partner_id'] . $params['command'] . $params['request_id'];
            $params['sign'] = md5($signData);

            debugLog("TheSieuRe getstatus request data: " . json_encode($params));

            // Gọi API
            $ch = curl_init($apiConfig['api_url']);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($params));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Accept: application/json'
            ]);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            debugLog("TheSieuRe getstatus response: $response, HTTP code: $httpCode, Error: $error");

            if ($error) {
                return ['success' => false, 'message' => 'Lỗi kết nối API: ' . $error];
            }

            if ($httpCode != 200) {
                return ['success' => false, 'message' => 'API trả về mã lỗi: ' . $httpCode];
            }

            $responseData = json_decode($response, true);

            if (!$responseData) {
                return ['success' => false, 'message' => 'Không thể phân tích phản hồi từ API'];
            }

            // Xử lý kết quả
            if (isset($responseData['status']) && $responseData['status'] == 'success') {
                return ['success' => true, 'message' => 'Lấy trạng thái giao dịch thành công', 'data' => $responseData['data']];
            } else {
                $errorMessage = isset($responseData['message']) ? $responseData['message'] : 'Lỗi không xác định từ API';
                return ['success' => false, 'message' => $errorMessage];
            }
        } catch (Exception $e) {
            debugLog("Error checking card status: " . $e->getMessage());
            return ['success' => false, 'message' => 'Lỗi khi kiểm tra trạng thái thẻ: ' . $e->getMessage()];
        }
    }

    /**
     * Lấy danh sách sản phẩm từ TheSieuRe.com
     *
     * @param array $apiConfig Cấu hình API
     * @return array Kết quả lấy danh sách sản phẩm ['success' => bool, 'message' => string, 'data' => array]
     */
    public function getProductList($apiConfig)
    {
        try {
            debugLog("Getting product list from TheSieuRe.com");

            // Tạo dữ liệu cho API
            $params = [
                'partner_id' => $apiConfig['merchant_id'],
                'command' => 'productlist'
            ];

            // Tạo chữ ký: md5(partner_key + partner_id + command)
            $signData = $apiConfig['secret_key'] . $params['partner_id'] . $params['command'];
            $params['sign'] = md5($signData);

            debugLog("TheSieuRe productlist request data: " . json_encode($params));

            // Gọi API
            $ch = curl_init($apiConfig['api_url']);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($params));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Accept: application/json'
            ]);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            debugLog("TheSieuRe productlist response: $response, HTTP code: $httpCode, Error: $error");

            if ($error) {
                return ['success' => false, 'message' => 'Lỗi kết nối API: ' . $error];
            }

            if ($httpCode != 200) {
                return ['success' => false, 'message' => 'API trả về mã lỗi: ' . $httpCode];
            }

            $responseData = json_decode($response, true);

            if (!$responseData) {
                return ['success' => false, 'message' => 'Không thể phân tích phản hồi từ API'];
            }

            // Xử lý kết quả
            if (isset($responseData['status']) && $responseData['status'] == 'success') {
                return ['success' => true, 'message' => 'Lấy danh sách sản phẩm thành công', 'data' => $responseData['data']];
            } else {
                $errorMessage = isset($responseData['message']) ? $responseData['message'] : 'Lỗi không xác định từ API';
                return ['success' => false, 'message' => $errorMessage];
            }
        } catch (Exception $e) {
            debugLog("Error getting product list: " . $e->getMessage());
            return ['success' => false, 'message' => 'Lỗi khi lấy danh sách sản phẩm: ' . $e->getMessage()];
        }
    }

    /**
     * Kiểm tra số dư tài khoản TheSieuRe.com
     *
     * @param array $apiConfig Cấu hình API
     * @return array Kết quả kiểm tra số dư ['success' => bool, 'message' => string, 'data' => array]
     */
    public function getBalance($apiConfig)
    {
        try {
            debugLog("Getting balance from TheSieuRe.com");

            // Tạo dữ liệu cho API
            $params = [
                'partner_id' => $apiConfig['merchant_id'],
                'command' => 'getbalance'
            ];

            // Tạo chữ ký: md5(partner_key + partner_id + command)
            $signData = $apiConfig['secret_key'] . $params['partner_id'] . $params['command'];
            $params['sign'] = md5($signData);

            debugLog("TheSieuRe getbalance request data: " . json_encode($params));

            // Gọi API
            $ch = curl_init($apiConfig['api_url']);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($params));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Accept: application/json'
            ]);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            debugLog("TheSieuRe getbalance response: $response, HTTP code: $httpCode, Error: $error");

            if ($error) {
                return ['success' => false, 'message' => 'Lỗi kết nối API: ' . $error];
            }

            if ($httpCode != 200) {
                return ['success' => false, 'message' => 'API trả về mã lỗi: ' . $httpCode];
            }

            $responseData = json_decode($response, true);

            if (!$responseData) {
                return ['success' => false, 'message' => 'Không thể phân tích phản hồi từ API'];
            }

            // Xử lý kết quả
            if (isset($responseData['status']) && $responseData['status'] == 'success') {
                return ['success' => true, 'message' => 'Lấy số dư tài khoản thành công', 'data' => $responseData['data']];
            } else {
                $errorMessage = isset($responseData['message']) ? $responseData['message'] : 'Lỗi không xác định từ API';
                return ['success' => false, 'message' => $errorMessage];
            }
        } catch (Exception $e) {
            debugLog("Error getting balance: " . $e->getMessage());
            return ['success' => false, 'message' => 'Lỗi khi lấy số dư tài khoản: ' . $e->getMessage()];
        }
    }

    /**
     * Calculate xu amount based on VND amount
     *
     * @param float $amount Amount in VND
     * @return int Amount in xu
     */
    private function calculateXuAmount($amount)
    {
        try {
            debugLog("Calculating xu for amount: $amount");
            echo "<script>console.log('Calculating xu for amount: $amount');</script>";

            // Kiểm tra bảng xu_rates có tồn tại không
            $tableCheck = $this->conn->query("SHOW TABLES LIKE 'xu_rates'");
            $xuRatesExists = $tableCheck->rowCount() > 0 ? 'Yes' : 'No';
            debugLog("xu_rates table exists: " . $xuRatesExists);
            echo "<script>console.log('xu_rates table exists: $xuRatesExists');</script>";

            if ($tableCheck->rowCount() == 0) {
                // Tạo bảng xu_rates nếu chưa tồn tại
                debugLog("Creating xu_rates table");
                echo "<script>console.log('Creating xu_rates table');</script>";
                $this->conn->exec("CREATE TABLE IF NOT EXISTS xu_rates (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    amount DECIMAL(10,2) NOT NULL,
                    xu INT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )");

                // Thêm một số giá trị mặc định
                $this->conn->exec("INSERT INTO xu_rates (amount, xu) VALUES
                    (10000, 10),
                    (20000, 20),
                    (50000, 55),
                    (100000, 120),
                    (200000, 250),
                    (500000, 650)
                ");
                debugLog("Inserted default xu rates");
                echo "<script>console.log('Inserted default xu rates');</script>";
            }

            // Lấy tỷ lệ quy đổi xu từ bảng xu_rates
            $stmt = $this->conn->prepare("SELECT * FROM xu_rates WHERE amount = :amount");
            $stmt->bindParam(':amount', $amount, PDO::PARAM_STR);
            $stmt->execute();
            $rate = $stmt->fetch(PDO::FETCH_ASSOC);

            $rateResult = $rate ? json_encode($rate) : 'null';
            debugLog("Rate query result: " . $rateResult);
            echo "<script>console.log('Rate query result: ' + JSON.stringify($rateResult));</script>";

            if ($rate && isset($rate['xu'])) {
                $foundRate = "Found rate: " . $rate['xu'] . " xu for " . $amount . " VND";
                debugLog($foundRate);
                echo "<script>console.log('$foundRate');</script>";
                return (int)$rate['xu'];
            }

            // Nếu không tìm thấy tỷ lệ chính xác, lấy tỷ lệ từ getXuRates()
            $xuRates = $this->getXuRates();
            $xuRatesJson = json_encode($xuRates);
            debugLog("Available rates from getXuRates: " . $xuRatesJson);
            echo "<script>console.log('Available rates from getXuRates: ' + JSON.stringify($xuRatesJson));</script>";

            if (isset($xuRates[$amount]) && isset($xuRates[$amount]['xu'])) {
                $rateFromXuRates = "Using rate from getXuRates: " . $xuRates[$amount]['xu'] . " xu";
                debugLog($rateFromXuRates);
                echo "<script>console.log('$rateFromXuRates');</script>";
                return (int)$xuRates[$amount]['xu'];
            }

            debugLog("No matching rate found for amount: $amount");
            echo "<script>console.log('No matching rate found for amount: $amount');</script>";
            // Sử dụng tỷ lệ mặc định
            $defaultXu = (int)($amount / 1000); // Tỷ lệ mặc định: 1000 VND = 1 xu
            $defaultRate = "Using default conversion rate: $defaultXu xu for $amount VND";
            debugLog($defaultRate);
            echo "<script>console.log('$defaultRate');</script>";
            return $defaultXu;
        } catch (PDOException $e) {
            debugLog("Error calculating xu amount: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Lấy lịch sử giao dịch của người dùng
     *
     * @param int $userId ID của người dùng
     * @return array Danh sách các giao dịch
     */
    public function getTransactionHistory($userId)
    {
        try {
            // Kiểm tra xem bảng card_transactions có tồn tại không
            $tableCheck = $this->conn->query("SHOW TABLES LIKE 'card_transactions'");
            if ($tableCheck->rowCount() == 0) {
                debugLog("Table card_transactions does not exist");
                return [];
            }

            // Lấy danh sách giao dịch
            $stmt = $this->conn->prepare("SELECT * FROM card_transactions WHERE user_id = :userId ORDER BY transaction_date DESC");
            $stmt->bindParam(':userId', $userId, PDO::PARAM_INT);
            $stmt->execute();
            $transactions = $stmt->fetchAll(PDO::FETCH_ASSOC);

            return $transactions;
        } catch (PDOException $e) {
            debugLog("Error getting transaction history: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Lấy số dư tài khoản của người dùng
     *
     * @param int $userId ID của người dùng
     * @return float Số dư tài khoản
     */
    public function getUserBalance($userId)
    {
        try {
            // Kiểm tra xem bảng user_balance có tồn tại không
            $tableCheck = $this->conn->query("SHOW TABLES LIKE 'user_balance'");
            if ($tableCheck->rowCount() == 0) {
                // Tạo bảng user_balance nếu chưa tồn tại
                $this->conn->exec("CREATE TABLE IF NOT EXISTS user_balance (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    balance DECIMAL(10,2) DEFAULT 0.00,
                    last_updated DATETIME DEFAULT CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

                debugLog("Created user_balance table");

                // Thêm bản ghi mặc định cho người dùng với số dư ban đầu là 20 xu
                $initialBalance = 20; // Đặt số dư ban đầu là 20 xu để có thể kích hoạt ngay
                $stmt = $this->conn->prepare("INSERT INTO user_balance (user_id, balance) VALUES (:userId, :balance)");
                $stmt->bindParam(':userId', $userId, PDO::PARAM_INT);
                $stmt->bindParam(':balance', $initialBalance, PDO::PARAM_STR);
                $stmt->execute();

                debugLog("Created initial balance of $initialBalance for user ID: $userId");
                return $initialBalance;
            }

            // Lấy số dư tài khoản
            $stmt = $this->conn->prepare("SELECT balance FROM user_balance WHERE user_id = :userId");
            $stmt->bindParam(':userId', $userId, PDO::PARAM_INT);
            $stmt->execute();
            $balance = $stmt->fetchColumn();

            if ($balance === false) {
                // Nếu không tìm thấy bản ghi, tạo mới với số dư ban đầu là 20 xu
                $initialBalance = 20; // Đặt số dư ban đầu là 20 xu để có thể kích hoạt ngay
                $stmt = $this->conn->prepare("INSERT INTO user_balance (user_id, balance) VALUES (:userId, :balance)");
                $stmt->bindParam(':userId', $userId, PDO::PARAM_INT);
                $stmt->bindParam(':balance', $initialBalance, PDO::PARAM_STR);
                $stmt->execute();

                debugLog("Created new balance record with $initialBalance for user ID: $userId");
                return $initialBalance;
            }

            return (float)$balance;
        } catch (PDOException $e) {
            debugLog("Error getting user balance: " . $e->getMessage());
            // Trả về giá trị mặc định 20 xu để có thể kích hoạt ngay
            return 20;
        }
    }

    /**
     * Kích hoạt tài khoản người dùng
     *
     * @param int $userId ID của người dùng
     * @return bool True nếu thành công, ngược lại là false
     */
    public function activateAccount($userId)
    {
        try {
            // Lấy thông tin người dùng
            $stmt = $this->conn->prepare("SELECT username FROM team_user WHERE id = :userId");
            $stmt->bindParam(':userId', $userId, PDO::PARAM_INT);
            $stmt->execute();
            $user = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$user) {
                debugLog("User not found with ID: $userId");
                return false;
            }

            // Kiểm tra xem đã có bản ghi trong bảng 5h_active chưa
            $stmt = $this->conn1->prepare("SELECT * FROM 5h_active WHERE userID = :userId OR username = :username");
            $stmt->bindParam(':userId', $userId, PDO::PARAM_INT);
            $stmt->bindParam(':username', $user['username'], PDO::PARAM_STR);
            $stmt->execute();
            $activation = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($activation) {
                // Cập nhật bản ghi hiện có
                $stmt = $this->conn1->prepare("UPDATE 5h_active SET time_end = -1 WHERE userID = :userId OR username = :username");
                $stmt->bindParam(':userId', $userId, PDO::PARAM_INT);
                $stmt->bindParam(':username', $user['username'], PDO::PARAM_STR);
                $result = $stmt->execute();
            } else {
                // Thêm bản ghi mới
                $stmt = $this->conn1->prepare("INSERT INTO 5h_active (userID, username, time_end) VALUES (:userId, :username, -1)");
                $stmt->bindParam(':userId', $userId, PDO::PARAM_INT);
                $stmt->bindParam(':username', $user['username'], PDO::PARAM_STR);
                $result = $stmt->execute();
            }

            if ($result) {
                debugLog("Account activated successfully for user ID: $userId");
                return true;
            } else {
                debugLog("Failed to activate account for user ID: $userId");
                return false;
            }
        } catch (PDOException $e) {
            debugLog("Error activating account: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Kích hoạt tài khoản người dùng với chi phí 20 xu
     *
     * @param int $userId ID của người dùng
     * @return array Kết quả kích hoạt ['success' => bool, 'message' => string]
     */
    public function activateAccountWithXu($userId)
    {
        try {
            // Bắt đầu transaction
            $this->conn->beginTransaction();

            // Lấy thông tin người dùng
            $stmt = $this->conn->prepare("SELECT username FROM team_user WHERE id = :userId");
            $stmt->bindParam(':userId', $userId, PDO::PARAM_INT);
            $stmt->execute();
            $user = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$user) {
                debugLog("User not found with ID: $userId");
                return ['success' => false, 'message' => 'Không tìm thấy thông tin người dùng.'];
            }

            // Kiểm tra trạng thái kích hoạt hiện tại
            $activation = $this->getActivationStatus($userId, $user['username']);
            if ($activation['status'] === 'Đã kích hoạt') {
                return ['success' => false, 'message' => 'Tài khoản đã được kích hoạt.'];
            }

            // Kiểm tra số dư xu
            $balance = $this->getUserBalance($userId);
            $xuCost = 20; // Chi phí kích hoạt: 20 xu

            if ($balance < $xuCost) {
                return ['success' => false, 'message' => 'Số dư không đủ. Bạn cần ít nhất 20 xu để kích hoạt tài khoản.'];
            }

            // Kiểm tra bảng user_balance có tồn tại không
            $tableCheck = $this->conn->query("SHOW TABLES LIKE 'user_balance'");
            if ($tableCheck->rowCount() == 0) {
                debugLog("Creating user_balance table");
                $this->conn->exec("CREATE TABLE IF NOT EXISTS user_balance (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    balance DECIMAL(10,2) DEFAULT 0.00,
                    last_updated DATETIME DEFAULT CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
                debugLog("user_balance table created successfully");

                // Thêm bản ghi mặc định cho người dùng
                $stmt = $this->conn->prepare("INSERT INTO user_balance (user_id, balance) VALUES (:userId, :balance)");
                $stmt->bindParam(':userId', $userId, PDO::PARAM_INT);
                $initialBalance = $xuCost; // Đặt số dư ban đầu bằng chi phí kích hoạt để có thể kích hoạt ngay
                $stmt->bindParam(':balance', $initialBalance, PDO::PARAM_STR);
                $stmt->execute();

                // Cập nhật biến balance
                $balance = $initialBalance;
                debugLog("Created initial balance of $initialBalance for user ID: $userId");
            }

            // Kiểm tra xem người dùng đã có bản ghi trong bảng user_balance chưa
            $stmt = $this->conn->prepare("SELECT id FROM user_balance WHERE user_id = :userId");
            $stmt->bindParam(':userId', $userId, PDO::PARAM_INT);
            $stmt->execute();
            $balanceRecord = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$balanceRecord) {
                // Thêm bản ghi mới nếu chưa có
                $stmt = $this->conn->prepare("INSERT INTO user_balance (user_id, balance) VALUES (:userId, :balance)");
                $stmt->bindParam(':userId', $userId, PDO::PARAM_INT);
                $initialBalance = $xuCost; // Đặt số dư ban đầu bằng chi phí kích hoạt để có thể kích hoạt ngay
                $stmt->bindParam(':balance', $initialBalance, PDO::PARAM_STR);
                $stmt->execute();

                // Cập nhật biến balance
                $balance = $initialBalance;
                debugLog("Created initial balance record with $initialBalance for user ID: $userId");
            }

            // Trừ xu từ tài khoản
            $newBalance = $balance - $xuCost;
            $stmt = $this->conn->prepare("UPDATE user_balance SET balance = :newBalance WHERE user_id = :userId");
            $stmt->bindParam(':newBalance', $newBalance, PDO::PARAM_STR);
            $stmt->bindParam(':userId', $userId, PDO::PARAM_INT);
            $updateResult = $stmt->execute();

            if (!$updateResult) {
                $errorInfo = $stmt->errorInfo();
                debugLog("Failed to update balance for user ID: $userId. Error: " . json_encode($errorInfo));
                $this->conn->rollBack();
                return ['success' => false, 'message' => 'Không thể cập nhật số dư tài khoản.'];
            }

            // Kiểm tra và tạo bảng transaction_log nếu chưa tồn tại
            $tableCheck = $this->conn->query("SHOW TABLES LIKE 'transaction_log'");
            if ($tableCheck->rowCount() == 0) {
                debugLog("Creating transaction_log table");
                $this->conn->exec("CREATE TABLE IF NOT EXISTS transaction_log (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    amount DECIMAL(10,2) NOT NULL,
                    type VARCHAR(50) NOT NULL,
                    description TEXT,
                    transaction_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )");
                debugLog("transaction_log table created successfully");
            }

            // Ghi log giao dịch
            try {
                $stmt = $this->conn->prepare("INSERT INTO transaction_log (user_id, amount, type, description, transaction_date)
                                             VALUES (:userId, :amount, 'activation', :description, NOW())");
                $stmt->bindParam(':userId', $userId, PDO::PARAM_INT);
                $stmt->bindParam(':amount', $xuCost, PDO::PARAM_INT);
                $description = "Kích hoạt tài khoản với chi phí 20 xu";
                $stmt->bindParam(':description', $description, PDO::PARAM_STR);
                $logResult = $stmt->execute();

                if (!$logResult) {
                    $errorInfo = $stmt->errorInfo();
                    debugLog("Failed to log transaction for user ID: $userId. Error: " . json_encode($errorInfo));
                    $this->conn->rollBack();
                    return ['success' => false, 'message' => 'Không thể ghi log giao dịch.'];
                }
            } catch (PDOException $e) {
                debugLog("Exception when logging transaction: " . $e->getMessage());
                $this->conn->rollBack();
                return ['success' => false, 'message' => 'Lỗi khi ghi log giao dịch: ' . $e->getMessage()];
            }

            // Kích hoạt tài khoản
            $stmt = $this->conn1->prepare("SELECT * FROM 5h_active WHERE userID = :userId OR username = :username");
            $stmt->bindParam(':userId', $userId, PDO::PARAM_INT);
            $stmt->bindParam(':username', $user['username'], PDO::PARAM_STR);
            $stmt->execute();
            $activationRecord = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($activationRecord) {
                // Cập nhật bản ghi hiện có
                $stmt = $this->conn1->prepare("UPDATE 5h_active SET time_end = -1 WHERE userID = :userId OR username = :username");
                $stmt->bindParam(':userId', $userId, PDO::PARAM_INT);
                $stmt->bindParam(':username', $user['username'], PDO::PARAM_STR);
                $activateResult = $stmt->execute();
            } else {
                // Thêm bản ghi mới
                $stmt = $this->conn1->prepare("INSERT INTO 5h_active (userID, username, time_end) VALUES (:userId, :username, -1)");
                $stmt->bindParam(':userId', $userId, PDO::PARAM_INT);
                $stmt->bindParam(':username', $user['username'], PDO::PARAM_STR);
                $activateResult = $stmt->execute();
            }

            if (!$activateResult) {
                $this->conn->rollBack();
                debugLog("Failed to activate account for user ID: $userId");
                return ['success' => false, 'message' => 'Không thể kích hoạt tài khoản.'];
            }

            // Commit transaction
            $this->conn->commit();
            debugLog("Account activated successfully with xu for user ID: $userId");
            return ['success' => true, 'message' => 'Kích hoạt tài khoản thành công. Đã trừ 20 xu từ tài khoản của bạn.'];
        } catch (PDOException $e) {
            // Rollback transaction on error
            if ($this->conn->inTransaction()) {
                $this->conn->rollBack();
            }
            debugLog("Error activating account with xu: " . $e->getMessage());
            return ['success' => false, 'message' => 'Lỗi hệ thống: ' . $e->getMessage()];
        }
    }

    /**
     * Lấy danh sách tài khoản ngân hàng đang hoạt động
     *
     * @return array Danh sách tài khoản ngân hàng
     */
    private function getActiveBankAccounts()
    {
        try {
            $stmt = $this->conn->prepare("SELECT * FROM bank_accounts WHERE is_active = 1 ORDER BY bank_name");
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            debugLog("Error getting active bank accounts: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Lấy danh sách tài khoản ngân hàng
     *
     * @param int $userId ID của người dùng (không sử dụng trong phiên bản hiện tại)
     * @return array Danh sách tài khoản ngân hàng
     */
    public function getBankAccounts($userId = null)
    {
        try {
            // Trong phiên bản hiện tại, chúng ta trả về tất cả tài khoản ngân hàng đang hoạt động
            // Tham số $userId được giữ lại để tương thích với gọi hàm hiện tại
            return $this->getActiveBankAccounts();
        } catch (PDOException $e) {
            debugLog("Error getting bank accounts: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Lấy thông tin tài khoản ngân hàng theo ID
     *
     * @param int $bankAccountId ID của tài khoản ngân hàng
     * @param int $userId ID của người dùng (để kiểm tra quyền truy cập trong tương lai)
     * @return array|false Thông tin tài khoản ngân hàng hoặc false nếu không tìm thấy
     */
    public function getBankAccountById($bankAccountId, $userId = null)
    {
        try {
            $stmt = $this->conn->prepare("SELECT * FROM bank_accounts WHERE id = :bankAccountId AND is_active = 1");
            $stmt->bindParam(':bankAccountId', $bankAccountId, PDO::PARAM_INT);
            $stmt->execute();
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            debugLog("Error getting bank account by ID: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Xử lý nạp tiền qua ATM
     *
     * @param int $userId ID của người dùng
     * @param int $bankAccountId ID của tài khoản ngân hàng
     * @param float $amount Số tiền nạp
     * @param string $transactionCode Mã giao dịch (nếu có)
     * @param string $userAccount Số tài khoản của người dùng
     * @return bool True nếu thành công, ngược lại là false
     */
    public function processBankTopup($userId, $bankAccountId, $amount, $transactionCode = '', $userAccount = '')
    {
        try {
            $logMessage = "Starting bank topup process for user: $userId, bank: $bankAccountId, amount: $amount, transaction_code: $transactionCode, user_account: $userAccount";
            debugLog($logMessage);
            echo "<script>console.log('$logMessage');</script>";

            $sessionData = json_encode($_SESSION);
            debugLog("Session data: " . $sessionData);
            echo "<script>console.log('Session data: ' + JSON.stringify($sessionData));</script>";

            // Validate inputs
            if (!is_numeric($userId) || $userId <= 0) {
                $errorMsg = "Invalid user ID: $userId";
                debugLog($errorMsg);
                echo "<script>console.error('$errorMsg');</script>";
                return false;
            }

            if (!is_numeric($bankAccountId) || $bankAccountId <= 0) {
                $errorMsg = "Invalid bank account ID: $bankAccountId";
                debugLog($errorMsg);
                echo "<script>console.error('$errorMsg');</script>";
                return false;
            }

            if (!is_numeric($amount) || $amount <= 0) {
                $errorMsg = "Invalid amount: $amount";
                debugLog($errorMsg);
                echo "<script>console.error('$errorMsg');</script>";
                return false;
            }

            // Kiểm tra bảng bank_transactions có tồn tại không
            $tableCheck = $this->conn->query("SHOW TABLES LIKE 'bank_transactions'");
            $tableExists = $tableCheck->rowCount() > 0 ? 'Yes' : 'No';
            debugLog("bank_transactions table exists: " . $tableExists);
            echo "<script>console.log('bank_transactions table exists: $tableExists');</script>";

            // Kiểm tra xem cột user_account đã tồn tại trong bảng bank_transactions chưa
            if ($tableExists == 'Yes') {
                $columnCheck = $this->conn->query("SHOW COLUMNS FROM bank_transactions LIKE 'user_account'");
                $columnExists = $columnCheck->rowCount() > 0 ? 'Yes' : 'No';
                debugLog("user_account column exists: " . $columnExists);
                echo "<script>console.log('user_account column exists: $columnExists');</script>";

                if ($columnExists == 'No') {
                    debugLog("Adding user_account column to bank_transactions table");
                    echo "<script>console.log('Adding user_account column to bank_transactions table');</script>";
                    $this->conn->exec("ALTER TABLE bank_transactions ADD COLUMN user_account VARCHAR(50) AFTER transaction_code");
                    debugLog("user_account column added successfully");
                    echo "<script>console.log('user_account column added successfully');</script>";
                }
            }

            if ($tableCheck->rowCount() == 0) {
                debugLog("Creating bank_transactions table");
                echo "<script>console.log('Creating bank_transactions table');</script>";
                // Tạo bảng nếu chưa tồn tại
                $this->conn->exec("CREATE TABLE IF NOT EXISTS bank_transactions (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    bank_account_id INT NOT NULL,
                    amount DECIMAL(10,2) NOT NULL,
                    xu_amount INT NOT NULL,
                    transaction_code VARCHAR(255),
                    user_account VARCHAR(50),
                    status ENUM('pending', 'approved', 'rejected') NOT NULL DEFAULT 'pending',
                    notes TEXT,
                    transaction_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                )");
                echo "<script>console.log('bank_transactions table created');</script>";
            }

            // Bắt đầu transaction
            $this->conn->beginTransaction();

            // Kiểm tra bảng team_user có tồn tại không
            $tableCheck = $this->conn->query("SHOW TABLES LIKE 'team_user'");
            $teamUserExists = $tableCheck->rowCount() > 0 ? 'Yes' : 'No';
            debugLog("team_user table exists: " . $teamUserExists);
            echo "<script>console.log('team_user table exists: $teamUserExists');</script>";

            if ($tableCheck->rowCount() == 0) {
                $errorMsg = "team_user table does not exist. Cannot proceed.";
                debugLog($errorMsg);
                echo "<script>console.error('$errorMsg');</script>";
                $this->conn->rollBack();
                return false;
            }

            // Kiểm tra người dùng tồn tại trong bảng team_user
            $stmt = $this->conn->prepare("SELECT id, username FROM team_user WHERE id = :userId");
            $stmt->bindParam(':userId', $userId, PDO::PARAM_INT);
            $stmt->execute();
            $user = $stmt->fetch(PDO::FETCH_ASSOC);

            $userResult = $user ? json_encode($user) : 'User not found';
            debugLog("User query result: " . $userResult);
            echo "<script>console.log('User query result: ' + JSON.stringify($userResult));</script>";

            if (!$user) {
                $errorMsg = "User not found for ID: $userId in team_user table";
                debugLog($errorMsg);
                echo "<script>console.error('$errorMsg');</script>";
                $this->conn->rollBack();
                return false;
            }

            // Kiểm tra ID người dùng có khớp với ID trong session không
            if ($user['id'] != $userId) {
                $errorMsg = "User ID mismatch: Session ID: $userId, DB ID: {$user['id']}";
                debugLog($errorMsg);
                echo "<script>console.error('$errorMsg');</script>";
                $this->conn->rollBack();
                return false;
            }

            // Kiểm tra bảng bank_accounts có tồn tại không
            $tableCheck = $this->conn->query("SHOW TABLES LIKE 'bank_accounts'");
            $bankAccountsExists = $tableCheck->rowCount() > 0 ? 'Yes' : 'No';
            debugLog("bank_accounts table exists: " . $bankAccountsExists);
            echo "<script>console.log('bank_accounts table exists: $bankAccountsExists');</script>";

            if ($tableCheck->rowCount() == 0) {
                debugLog("Creating bank_accounts table");
                echo "<script>console.log('Creating bank_accounts table');</script>";
                // Tạo bảng bank_accounts nếu chưa tồn tại
                $this->conn->exec("CREATE TABLE IF NOT EXISTS bank_accounts (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    bank_name VARCHAR(255) NOT NULL,
                    account_name VARCHAR(255) NOT NULL,
                    account_number VARCHAR(50) NOT NULL,
                    is_active TINYINT(1) NOT NULL DEFAULT 1,
                    qr_code_url VARCHAR(255),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )");

                // Thêm một số tài khoản ngân hàng mặc định
                $this->conn->exec("INSERT INTO bank_accounts (bank_name, account_name, account_number, is_active) VALUES
                    ('Vietcombank', 'NGUYEN VAN A', '**********', 1),
                    ('Techcombank', 'NGUYEN VAN A', '**********', 1),
                    ('MBBank', 'NGUYEN VAN A', '**********', 1)
                ");
                debugLog("Inserted default bank accounts");
                echo "<script>console.log('Inserted default bank accounts');</script>";
            }

            // Kiểm tra tài khoản ngân hàng
            $stmt = $this->conn->prepare("SELECT * FROM bank_accounts WHERE id = :bankAccountId AND is_active = 1");
            $stmt->bindParam(':bankAccountId', $bankAccountId, PDO::PARAM_INT);
            $stmt->execute();
            $bankAccount = $stmt->fetch(PDO::FETCH_ASSOC);

            $bankAccountResult = $bankAccount ? json_encode($bankAccount) : 'Bank account not found';
            debugLog("Bank account query result: " . $bankAccountResult);
            echo "<script>console.log('Bank account query result: ' + JSON.stringify($bankAccountResult));</script>";

            if (!$bankAccount) {
                $errorMsg = "Bank account not found or inactive: $bankAccountId";
                debugLog($errorMsg);
                echo "<script>console.error('$errorMsg');</script>";
                $this->conn->rollBack();
                return false;
            }

            // Tính toán số xu dựa trên số tiền nạp
            $xuAmount = $this->calculateXuAmount($amount);
            debugLog("Calculated xu amount: $xuAmount for amount: $amount");
            echo "<script>console.log('Calculated xu amount: $xuAmount for amount: $amount');</script>";

            if ($xuAmount <= 0) {
                debugLog("Invalid xu amount calculated: $xuAmount. Using direct conversion.");
                echo "<script>console.log('Invalid xu amount calculated: $xuAmount. Using direct conversion.');</script>";
                // Sử dụng tỷ lệ mặc định nếu không tìm thấy trong bảng
                $xuAmount = (int)($amount / 1000); // Tỷ lệ mặc định: 1000 VND = 1 xu
                debugLog("Using default rate: $xuAmount xu for $amount VND");
                echo "<script>console.log('Using default rate: $xuAmount xu for $amount VND');</script>";
            }

            // In ra thông tin trước khi thêm vào database để debug
            $insertInfo = "About to insert: user_id={$user['id']}, bank_account_id=$bankAccountId, amount=$amount, xu_amount=$xuAmount";
            debugLog($insertInfo);
            echo "<script>console.log('$insertInfo');</script>";

            try {
                // Thêm giao dịch mới
                $insertSQL = "INSERT INTO bank_transactions
                    (user_id, bank_account_id, amount, transaction_code, user_account, status, notes, xu_amount)
                    VALUES (:userId, :bankAccountId, :amount, :transactionCode, :userAccount, 'pending', :notes, :xuAmount)";

                debugLog("SQL query: $insertSQL");
                echo "<script>console.log('SQL query: ' + `$insertSQL`);</script>";
                $stmt = $this->conn->prepare($insertSQL);

                $stmt->bindParam(':userId', $user['id'], PDO::PARAM_INT);
                $stmt->bindParam(':bankAccountId', $bankAccountId, PDO::PARAM_INT);
                $stmt->bindParam(':amount', $amount, PDO::PARAM_STR);
                $stmt->bindParam(':transactionCode', $transactionCode, PDO::PARAM_STR);
                $stmt->bindParam(':userAccount', $userAccount, PDO::PARAM_STR);
                $stmt->bindParam(':xuAmount', $xuAmount, PDO::PARAM_INT);

                $notes = "Yêu cầu nạp qua ATM từ người dùng: " . $user['username'] . " vào lúc: " . date('Y-m-d H:i:s');
                $stmt->bindParam(':notes', $notes, PDO::PARAM_STR);

                $boundParams = "Bound parameters: userId={$user['id']}, bankAccountId=$bankAccountId, amount=$amount, xuAmount=$xuAmount, userAccount=$userAccount";
                debugLog($boundParams);
                echo "<script>console.log('$boundParams');</script>";

                $result = $stmt->execute();
                $executeResult = $result ? 'Success' : 'Failed';
                debugLog("Execute result: " . $executeResult);
                echo "<script>console.log('Execute result: $executeResult');</script>";

                if (!$result) {
                    $errorInfo = $stmt->errorInfo();
                    $errorMsg = "Failed to insert bank transaction. Error info: " . json_encode($errorInfo);
                    debugLog($errorMsg);
                    echo "<script>console.error('$errorMsg');</script>";
                    $this->conn->rollBack();
                    return false;
                }
            } catch (PDOException $e) {
                $errorMsg = "PDOException during insert: " . $e->getMessage();
                debugLog($errorMsg);
                echo "<script>console.error('$errorMsg');</script>";
                $this->conn->rollBack();
                return false;
            }

            // Commit transaction
            $this->conn->commit();
            echo "<script>console.log('Transaction committed successfully');</script>";
            return true;
        } catch (PDOException $e) {
            // Rollback transaction on error
            if ($this->conn->inTransaction()) {
                $this->conn->rollBack();
                echo "<script>console.log('Transaction rolled back due to PDOException');</script>";
            }
            $errorMsg = "PDOException in processBankTopup: " . $e->getMessage();
            debugLog($errorMsg . "\nTrace: " . $e->getTraceAsString());
            echo "<script>console.error('$errorMsg');</script>";
            return false;
        } catch (Exception $e) {
            // Rollback transaction on error
            if ($this->conn->inTransaction()) {
                $this->conn->rollBack();
                echo "<script>console.log('Transaction rolled back due to Exception');</script>";
            }
            $errorMsg = "Exception in processBankTopup: " . $e->getMessage();
            debugLog($errorMsg);
            echo "<script>console.error('$errorMsg');</script>";
            return false;
        }
    }

    /**
     * Lấy lịch sử giao dịch ngân hàng của người dùng
     *
     * @param int $userId ID của người dùng
     * @return array Danh sách giao dịch
     */
    public function getBankTransactionHistory($userId)
    {
        try {
            $stmt = $this->conn->prepare("
                SELECT bt.*, ba.bank_name
                FROM bank_transactions bt
                JOIN bank_accounts ba ON bt.bank_account_id = ba.id
                WHERE bt.user_id = :userId
                ORDER BY bt.transaction_date DESC
            ");
            $stmt->bindParam(':userId', $userId, PDO::PARAM_INT);
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            debugLog("Error getting bank transaction history: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Đổi mật khẩu cho người dùng
     *
     * @param int $userId ID của người dùng
     * @param string $currentPassword Mật khẩu hiện tại
     * @param string $newPassword Mật khẩu mới
     * @return array Kết quả đổi mật khẩu ['success' => bool, 'message' => string]
     */
    public function changePassword($userId, $currentPassword, $newPassword)
    {
        try {
            debugLog("Attempting to change password for user ID: $userId");

            // Kiểm tra người dùng tồn tại
            $stmt = $this->conn->prepare("SELECT id, username, password FROM team_user WHERE id = :userId");
            $stmt->bindParam(':userId', $userId, PDO::PARAM_INT);
            $stmt->execute();
            $user = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$user) {
                debugLog("User not found with ID: $userId");
                return ['success' => false, 'message' => 'Không tìm thấy thông tin người dùng.'];
            }

            // Kiểm tra mật khẩu hiện tại
            $hashedCurrentPassword = $this->sqlPassword($currentPassword);

            if ($user['password'] !== $hashedCurrentPassword) {
                debugLog("Current password does not match for user ID: $userId");
                return ['success' => false, 'message' => 'Mật khẩu hiện tại không đúng.'];
            }

            // Mã hóa mật khẩu mới
            $hashedNewPassword = $this->sqlPassword($newPassword);

            // Cập nhật mật khẩu mới
            $stmt = $this->conn->prepare("UPDATE team_user SET password = :password WHERE id = :userId");
            $stmt->bindParam(':password', $hashedNewPassword, PDO::PARAM_STR);
            $stmt->bindParam(':userId', $userId, PDO::PARAM_INT);
            $result = $stmt->execute();

            if ($result) {
                debugLog("Password changed successfully for user ID: $userId");
                return ['success' => true, 'message' => 'Đổi mật khẩu thành công!'];
            } else {
                debugLog("Failed to change password for user ID: $userId");
                return ['success' => false, 'message' => 'Đổi mật khẩu thất bại.'];
            }
        } catch (PDOException $e) {
            debugLog("Error changing password: " . $e->getMessage());
            return ['success' => false, 'message' => 'Lỗi hệ thống: ' . $e->getMessage()];
        }
    }

    /**
     * Hàm mã hóa mật khẩu theo kiểu MySQL PASSWORD()
     *
     * @param string $input Mật khẩu cần mã hóa
     * @return string Mật khẩu đã mã hóa
     */
    private function sqlPassword($input)
    {
        debugLog("Hashing password with MySQL PASSWORD() style");
        // Sử dụng MySQL PASSWORD() style để mã hóa mật khẩu
        $pass = strtoupper(sha1(sha1($input, true)));
        return '*' . $pass;
    }

    /**
     * Lấy bảng giá quy đổi xu
     *
     * @return array Bảng giá quy đổi xu
     */
    public function getXuRates()
    {
        try {
            debugLog("Getting xu rates");
            echo "<script>console.log('Getting xu rates');</script>";

            // Kiểm tra xem bảng xu_rates có tồn tại không
            $tableCheck = $this->conn->query("SHOW TABLES LIKE 'xu_rates'");
            $xuRatesExists = $tableCheck->rowCount() > 0 ? 'Yes' : 'No';
            debugLog("xu_rates table exists in getXuRates: " . $xuRatesExists);
            echo "<script>console.log('xu_rates table exists in getXuRates: $xuRatesExists');</script>";

            if ($tableCheck->rowCount() == 0) {
                debugLog("Creating xu_rates table in getXuRates");
                echo "<script>console.log('Creating xu_rates table in getXuRates');</script>";
                // Tạo bảng xu_rates nếu chưa tồn tại
                $this->conn->exec("CREATE TABLE IF NOT EXISTS xu_rates (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    amount DECIMAL(10,2) NOT NULL,
                    xu INT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )");

                // Thêm một số giá trị mặc định
                $this->conn->exec("INSERT INTO xu_rates (amount, xu) VALUES
                    (10000, 10),
                    (20000, 20),
                    (50000, 55),
                    (100000, 120),
                    (200000, 250),
                    (500000, 650)
                ");
                debugLog("Inserted default xu rates in getXuRates");
                echo "<script>console.log('Inserted default xu rates in getXuRates');</script>";
            }

            // Lấy danh sách tỷ lệ quy đổi xu
            $stmt = $this->conn->query("SELECT amount, xu FROM xu_rates ORDER BY amount ASC");
            $rates = $stmt->fetchAll(PDO::FETCH_ASSOC);
            debugLog("Fetched " . count($rates) . " xu rates from database");
            echo "<script>console.log('Fetched " . count($rates) . " xu rates from database');</script>";

            // Chuyển đổi thành mảng với key là amount
            $result = [];
            foreach ($rates as $rate) {
                $result[$rate['amount']] = [
                    'xu' => $rate['xu']
                ];
            }

            $resultJson = json_encode($result);
            debugLog("Xu rates result: " . $resultJson);
            echo "<script>console.log('Xu rates result: ' + JSON.stringify($resultJson));</script>";

            return $result;
        } catch (PDOException $e) {
            $errorMsg = "Error getting xu rates: " . $e->getMessage();
            debugLog($errorMsg);
            echo "<script>console.error('$errorMsg');</script>";
            // Trả về giá trị mặc định nếu có lỗi
            $defaultRates = [
                '10000' => ['xu' => 10],
                '20000' => ['xu' => 20],
                '50000' => ['xu' => 55],
                '100000' => ['xu' => 120],
                '200000' => ['xu' => 250],
                '500000' => ['xu' => 650]
            ];
            debugLog("Using default xu rates: " . json_encode($defaultRates));
            echo "<script>console.log('Using default xu rates: ' + JSON.stringify(" . json_encode($defaultRates) . "));</script>";
            return $defaultRates;
        }
    }
}

// Xử lý AJAX request để kích hoạt tài khoản
if (
    isset($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD'] === 'POST'
    && isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest'
) {
    // Lấy dữ liệu từ request
    $data = json_decode(file_get_contents('php://input'), true);

    if (isset($data['userId']) && is_numeric($data['userId'])) {
        // Kiểm tra quyền admin
        if (!isset($_SESSION['is_admin']) || !$_SESSION['is_admin']) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Bạn không có quyền thực hiện hành động này.']);
            exit();
        }

        // Kết nối đến cơ sở dữ liệu
        require_once __DIR__ . '/database.php';

        try {
            $conn = connectPDO($config1);
            $conn1 = connectPDO($config2);

            $accountManager = new AccountManager($conn, $conn1);
            $result = $accountManager->activateAccount($data['userId']);

            header('Content-Type: application/json');
            if ($result) {
                echo json_encode(['success' => true, 'message' => 'Kích hoạt tài khoản thành công.']);
            } else {
                echo json_encode(['success' => false, 'message' => 'Kích hoạt tài khoản thất bại.']);
            }
        } catch (PDOException $e) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Lỗi kết nối cơ sở dữ liệu: ' . $e->getMessage()]);
        }

        exit();
    }
}
