# KPAH - <PERSON><PERSON> thống quản lý tài khoản và nạp thẻ

Hệ thống quản lý tài khoản và nạp thẻ cho game KPAH, bao gồm các chức năng:
- Đăng ký và đăng nhập tài khoản
- Quản lý thông tin tài khoản
- Nạp thẻ và quản lý giao dịch
- Kích hoạt tài khoản
- Quên mật khẩu và đặt lại mật khẩu qua email
- Bảng xếp hạng số dư người dùng
- Bảng xếp hạng cao thủ (cấp độ và kinh nghiệm)
- URL thân thiện (không có đuôi file) và trang 404 tùy chỉnh
- Quản trị viên: quản lý người dùng, nạp thẻ thủ công, kích hoạt tài khoản

## Y<PERSON>u cầu hệ thống

- PHP 7.4 trở lên
- MySQL 5.7 trở lên
- Web server (Apache, Nginx)
- XAMPP (khuyến nghị cho môi trường phát triển)

## Cài đặt

### Cài đặt tự động (Khuyên dùng)

1. Clone hoặc tải xuống mã nguồn và đặt vào thư mục web server (ví dụ: `htdocs` trong XAMPP)
2. Truy cập thư mục `installer` qua trình duyệt (ví dụ: `http://localhost/installer`)
3. Làm theo các bước trên màn hình để hoàn tất quá trình cài đặt:
   - Kiểm tra yêu cầu hệ thống
   - Cấu hình cơ sở dữ liệu
   - Cấu hình trang web và tài khoản quản trị viên
   - Hoàn tất cài đặt
4. Sau khi cài đặt hoàn tất, xóa thư mục `installer` để đảm bảo an toàn

### Cài đặt thủ công

1. Clone hoặc tải xuống mã nguồn và đặt vào thư mục web server (ví dụ: `htdocs` trong XAMPP)
2. Tạo cơ sở dữ liệu bằng cách import file `setup_database.sql`:
   - Mở phpMyAdmin (http://localhost/phpmyadmin)
   - Chọn tab "Import"
   - Chọn file `setup_database.sql`
   - Nhấn "Go" để thực hiện import
3. Cấu hình kết nối cơ sở dữ liệu trong file `Controllers/database.php` (nếu cần)
4. Truy cập trang web qua trình duyệt: http://localhost/

## Tài khoản mặc định

- **Admin**:
  - Tên đăng nhập: `admin`
  - Mật khẩu: `Admin123`

- **Người dùng test**:
  - Tên đăng nhập: `testuser`
  - Mật khẩu: `Admin123`

## Cấu trúc thư mục

- `Controllers/`: Chứa các file xử lý logic
- `Views/`: Chứa các file giao diện người dùng
- `Panel/`: Chứa các file giao diện quản trị viên
- `assets/`: Chứa các file tài nguyên (CSS, JS, hình ảnh)
- `handle/`: Chứa các file xử lý form
- `password_reset/`: Chứa các file xử lý quên mật khẩu và đặt lại mật khẩu
- `installer/`: Công cụ cài đặt tự động
- `vendor/`: Chứa các thư viện bên ngoài (PHPMailer)

## Chức năng chính

### Người dùng

1. **Đăng ký tài khoản**
   - Đăng ký tài khoản mới với tên đăng nhập, mật khẩu, email

2. **Đăng nhập**
   - Đăng nhập vào hệ thống với tên đăng nhập và mật khẩu

3. **Quản lý tài khoản**
   - Xem thông tin tài khoản
   - Cập nhật email
   - Xem trạng thái kích hoạt
   - Xem số dư tài khoản

4. **Nạp thẻ**
   - Gửi yêu cầu nạp thẻ
   - Xem lịch sử giao dịch

5. **Quên mật khẩu**
   - Yêu cầu đặt lại mật khẩu qua email
   - Nhận email chứa liên kết đặt lại mật khẩu
   - Đặt mật khẩu mới

6. **Bảng xếp hạng số dư**
   - Xem bảng xếp hạng số dư người dùng
   - Xem thứ hạng của bản thân
   - Xem tổng số dư toàn hệ thống

7. **Bảng xếp hạng cao thủ**
   - Xem bảng xếp hạng theo cấp độ hoặc kinh nghiệm
   - Xem thứ hạng của nhân vật
   - Hiển thị cấp độ và kinh nghiệm của người chơi

8. **URL thân thiện**
   - URL không có đuôi file (.php)
   - Trang 404 tùy chỉnh khi truy cập URL không tồn tại
   - Cải thiện trải nghiệm người dùng và SEO

### Quản trị viên

1. **Quản lý người dùng**
   - Xem danh sách người dùng
   - Khóa/mở khóa tài khoản
   - Cấp/thu hồi quyền quản trị viên

2. **Quản lý nạp thẻ**
   - Xem danh sách giao dịch
   - Duyệt/từ chối yêu cầu nạp thẻ
   - Xem chi tiết giao dịch

3. **Nạp thẻ thủ công**
   - Thêm giao dịch nạp thẻ thủ công cho người dùng

4. **Kích hoạt tài khoản**
   - Kích hoạt vĩnh viễn tài khoản người dùng
   - Kích hoạt tạm thời (24 giờ) tài khoản người dùng
   - Hủy kích hoạt tài khoản

## Cơ sở dữ liệu

### Database `account`

1. **Bảng `team_user`**
   - Lưu thông tin người dùng: ID, tên đăng nhập, mật khẩu, email, số điện thoại, trạng thái khóa, quyền admin

2. **Bảng `user_balance`**
   - Lưu số dư tài khoản của người dùng

3. **Bảng `card_transactions`**
   - Lưu thông tin giao dịch nạp thẻ: loại thẻ, mã thẻ, mệnh giá, trạng thái, số xu

### Database `game_db`

1. **Bảng `5h_active`**
   - Lưu trạng thái kích hoạt tài khoản

2. **Bảng `tob_char`**
   - Lưu thông tin nhân vật trong game

## Liên hệ

Nếu có bất kỳ câu hỏi hoặc góp ý nào, vui lòng liên hệ qua email: <EMAIL>
