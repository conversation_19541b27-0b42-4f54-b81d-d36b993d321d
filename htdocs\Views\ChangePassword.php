<?php
// Start the session at the beginning of the file
session_start();

// Check if user is logged in, redirect to login page if not
if (!isset($_SESSION['team_user']) || empty($_SESSION['team_user'])) {
    header("Location: ../index.php?error=" . urlencode('Vui lòng đăng nhập để truy cập trang này.'));
    exit();
}

// Include database configuration
require_once '../Controllers/database.php';

// Create AccountManager class if it doesn't exist
if (!class_exists('AccountManager')) {
    require_once '../Controllers/Account.php';
}

// Connect to databases
try {
    // Connect to account database
    $conn = connectPDO($config1);

    // Connect to game_db database
    $conn1 = connectPDO($config2);

    // Create necessary tables if they don't exist
    createTables($conn, $conn1);
} catch (PDOException $e) {
    die("Lỗi kết nối cơ sở dữ liệu: " . $e->getMessage());
}

// Thêm hàm ghi log nếu chưa có
if (!function_exists('debugLog')) {
    function debugLog($message)
    {
        // Sử dụng error_log thay vì ghi vào file riêng
        error_log("[CHANGE_PASSWORD] " . $message);
    }
}

// Ghi log thông tin session
debugLog("Session data: " . json_encode($_SESSION));

// Get user information
if (isset($_SESSION['id'])) {
    $userId = $_SESSION['id'];
    debugLog("User ID from session: $userId");
} else {
    // Nếu không có id trong session, thử lấy từ database dựa vào username
    if (isset($_SESSION['team_user'])) {
        $stmt = $conn->prepare("SELECT id FROM team_user WHERE username = :username");
        $stmt->bindParam(':username', $_SESSION['team_user'], PDO::PARAM_STR);
        $stmt->execute();
        $userResult = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($userResult) {
            $userId = $userResult['id'];
            // Lưu user_id vào session để sử dụng sau này
            $_SESSION['id'] = $userId;
            debugLog("User ID retrieved from database: $userId");
        } else {
            debugLog("User not found in database for username: {$_SESSION['team_user']}");
            header("Location: ../index.php?error=" . urlencode('Không tìm thấy thông tin người dùng. Vui lòng đăng nhập lại.'));
            exit();
        }
    } else {
        debugLog("No user information in session");
        header("Location: ../index.php?error=" . urlencode('Vui lòng đăng nhập để truy cập trang này.'));
        exit();
    }
}

$username = $_SESSION['team_user'];
$isAdmin = isset($_SESSION['is_admin']) && $_SESSION['is_admin'];

debugLog("User information: ID=$userId, Username=$username, IsAdmin=$isAdmin");

// Initialize AccountManager
$accountManager = new AccountManager($conn, $conn1);
$userDetails = $accountManager->getUserDetails($userId);

// Xử lý đổi mật khẩu
$passwordMessage = '';
$passwordSuccess = false;

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['submit_password'])) {
    $currentPassword = $_POST['current_password'] ?? '';
    $newPassword = $_POST['new_password'] ?? '';
    $confirmPassword = $_POST['confirm_password'] ?? '';

    debugLog("Password change request received for user ID: $userId");

    if (empty($currentPassword) || empty($newPassword) || empty($confirmPassword)) {
        $passwordMessage = 'Vui lòng điền đầy đủ thông tin mật khẩu.';
        debugLog("Password change validation failed: Empty fields");
    } elseif ($newPassword !== $confirmPassword) {
        $passwordMessage = 'Mật khẩu mới và mật khẩu xác nhận không khớp.';
        debugLog("Password change validation failed: Passwords don't match");
    } elseif (strlen($newPassword) < 6) {
        $passwordMessage = 'Mật khẩu mới phải có ít nhất 6 ký tự.';
        debugLog("Password change validation failed: Password too short");
    } else {
        // Gọi hàm đổi mật khẩu
        $result = $accountManager->changePassword($userId, $currentPassword, $newPassword);
        $passwordSuccess = $result['success'];
        $passwordMessage = $result['message'];
        debugLog("Password change result: " . ($passwordSuccess ? "Success" : "Failed") . " - " . $passwordMessage);
    }
}
?>

<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Đổi mật khẩu - KPAH</title>
    <link href="../assets/homepage/css/bootstrap.min.css" rel="stylesheet" type="text/css">
    <link rel="stylesheet" href="../assets/homepage/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <script src="../assets/homepage/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/homepage/js/jquery-1.9.1.min.js"></script>
    <style>
        body {
            background-image: url('../assets/homepage/images/bg-page1.jpg');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            color: #f9d686;
            font-family: Arial, sans-serif;
        }

        .account-container {
            background-color: rgba(0, 0, 0, 0.7);
            border-radius: 10px;
            padding: 20px;
            margin-top: 30px;
            border: 1px solid #f9d686;
            box-shadow: 0 0 15px rgba(249, 214, 134, 0.3);
        }

        .account-header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 1px solid #f9d686;
            padding-bottom: 15px;
        }

        .account-header h1 {
            color: #f9d686;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .account-section {
            margin-bottom: 30px;
            padding: 15px;
            background-color: rgba(0, 0, 0, 0.5);
            border-radius: 8px;
            border: 1px solid #f9d686;
        }

        .account-section h2 {
            color: #f9d686;
            font-size: 1.5rem;
            margin-bottom: 15px;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
        }

        .btn-custom {
            background-color: #f9d686;
            color: #000;
            border: none;
            font-weight: bold;
        }

        .btn-custom:hover {
            background-color: #e5c677;
            color: #000;
        }

        .form-control {
            background-color: rgba(255, 255, 255, 0.9);
            border: 1px solid #f9d686;
        }

        .form-label {
            color: #f9d686;
            font-weight: bold;
        }

        .back-button {
            margin-top: 20px;
            margin-bottom: 30px;
        }

        /* Responsive styles */
        @media (max-width: 768px) {
            .account-container {
                margin-top: 15px;
                padding: 15px;
            }

            .account-section {
                padding: 10px;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="account-container">
                    <div class="account-header">
                        <h1>Đổi mật khẩu</h1>
                        <p>Xin chào, <strong><?php echo htmlspecialchars($username); ?></strong>!</p>
                    </div>

                    <?php if (!empty($passwordMessage)): ?>
                        <div class="alert <?php echo $passwordSuccess ? 'alert-success' : 'alert-danger'; ?> alert-dismissible fade show"
                            role="alert">
                            <?php echo $passwordMessage; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <div class="account-section">
                        <h2>Thay đổi mật khẩu</h2>
                        <form method="post" action="">
                            <div class="mb-3">
                                <label for="current_password" class="form-label">Mật khẩu hiện tại</label>
                                <input type="password" class="form-control" id="current_password" name="current_password" required>
                            </div>
                            <div class="mb-3">
                                <label for="new_password" class="form-label">Mật khẩu mới</label>
                                <input type="password" class="form-control" id="new_password" name="new_password" required>
                                <div class="form-text text-light">Mật khẩu phải có ít nhất 6 ký tự.</div>
                            </div>
                            <div class="mb-3">
                                <label for="confirm_password" class="form-label">Xác nhận mật khẩu mới</label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                            </div>
                            <div class="d-grid gap-2">
                                <button type="submit" name="submit_password" class="btn btn-custom">
                                    <i class="fas fa-key"></i> Đổi mật khẩu
                                </button>
                            </div>
                        </form>
                    </div>

                    <div class="text-center back-button">
                        <a href="../Views/Account.php" class="btn btn-custom me-2">
                            <i class="fas fa-user"></i> Quay lại tài khoản
                        </a>
                        <a href="../index.php" class="btn btn-custom">
                            <i class="fas fa-home"></i> Về trang chủ
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            // Auto-dismiss alerts after 5 seconds
            setTimeout(function() {
                $('.alert').alert('close');
            }, 5000);
        });
    </script>
</body>

</html>