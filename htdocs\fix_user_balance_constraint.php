<?php
// Bật hiển thị lỗi để debug
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Thông tin kết nối cơ sở dữ liệu
require_once 'Controllers/database.php';

echo "<h1>Sửa lỗi ràng buộc khóa ngoại trong bảng user_balance</h1>";

try {
    // Kết nối đến cơ sở dữ liệu
    $conn = connectPDO($config1);

    echo "<p>Kết nối thành công đến cơ sở dữ liệu</p>";

    // Kiểm tra bảng user_balance
    echo "<h2>Cấu trúc bảng user_balance hiện tại</h2>";
    $result = $conn->query("SHOW CREATE TABLE user_balance");
    $row = $result->fetch(PDO::FETCH_ASSOC);
    echo "<pre>" . $row['Create Table'] . "</pre>";

    // Kiểm tra xem có ràng buộc khóa ngoại không
    if (strpos($row['Create Table'], 'FOREIGN KEY') !== false) {
        echo "<p>Đang xóa ràng buộc khóa ngoại...</p>";

        // Lấy tên của ràng buộc khóa ngoại
        preg_match('/CONSTRAINT `([^`]+)` FOREIGN KEY/', $row['Create Table'], $matches);

        if (isset($matches[1])) {
            $constraintName = $matches[1];
            echo "<p>Tên ràng buộc khóa ngoại: $constraintName</p>";

            // Xóa ràng buộc khóa ngoại
            $conn->exec("ALTER TABLE user_balance DROP FOREIGN KEY `$constraintName`");
            echo "<p style='color:green'>Đã xóa ràng buộc khóa ngoại thành công.</p>";
        } else {
            echo "<p style='color:orange'>Không tìm thấy tên ràng buộc khóa ngoại.</p>";
        }
    } else {
        echo "<p style='color:orange'>Không tìm thấy ràng buộc khóa ngoại trong bảng user_balance.</p>";
    }

    // Kiểm tra lại cấu trúc bảng sau khi sửa
    $result = $conn->query("SHOW CREATE TABLE user_balance");
    $row = $result->fetch(PDO::FETCH_ASSOC);

    echo "<h2>Cấu trúc bảng user_balance sau khi sửa:</h2>";
    echo "<pre>" . $row['Create Table'] . "</pre>";

    // Kiểm tra xem có ràng buộc INDEX không
    if (strpos($row['Create Table'], 'KEY `user_id`') !== false) {
        echo "<p>Đang xóa chỉ mục user_id...</p>";

        // Xóa chỉ mục
        $conn->exec("ALTER TABLE user_balance DROP INDEX `user_id`");
        echo "<p style='color:green'>Đã xóa chỉ mục user_id thành công.</p>";
    } else {
        echo "<p style='color:orange'>Không tìm thấy chỉ mục user_id trong bảng user_balance.</p>";
    }

    // Kiểm tra lại cấu trúc bảng sau khi sửa
    $result = $conn->query("SHOW CREATE TABLE user_balance");
    $row = $result->fetch(PDO::FETCH_ASSOC);

    echo "<h2>Cấu trúc bảng user_balance sau khi sửa hoàn tất:</h2>";
    echo "<pre>" . $row['Create Table'] . "</pre>";

    echo "<p style='color:green'>Đã sửa xong lỗi ràng buộc khóa ngoại. Bây giờ bạn có thể duyệt giao dịch ATM mà không gặp lỗi.</p>";

    // Kiểm tra xem bảng user_balance có tồn tại không
    $tableCheck = $conn->query("SHOW TABLES LIKE 'user_balance'");
    $tableExists = $tableCheck->rowCount() > 0;

    if (!$tableExists) {
        // Tạo bảng user_balance nếu chưa tồn tại
        $conn->exec("CREATE TABLE user_balance (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            balance INT NOT NULL DEFAULT 0,
            last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )");

        echo "<p style='color:green'>Đã tạo bảng user_balance thành công.</p>";
    }

    // Kiểm tra xem có giao dịch nào đang chờ duyệt không
    $result = $conn->query("SELECT bt.*, tu.username FROM bank_transactions bt JOIN team_user tu ON bt.user_id = tu.id WHERE bt.status = 'pending'");
    $pendingTransactions = $result->fetchAll(PDO::FETCH_ASSOC);

    echo "<h2>Các giao dịch đang chờ duyệt:</h2>";

    if (count($pendingTransactions) > 0) {
        echo "<table border='1'>";
        echo "<tr><th>ID</th><th>Người dùng</th><th>Số tiền</th><th>Xu</th><th>Mã giao dịch</th><th>Ngày giao dịch</th></tr>";

        foreach ($pendingTransactions as $transaction) {
            echo "<tr>";
            echo "<td>" . $transaction['id'] . "</td>";
            echo "<td>" . htmlspecialchars($transaction['username']) . "</td>";
            echo "<td>" . number_format($transaction['amount'], 0, ',', '.') . " VNĐ</td>";
            echo "<td>" . number_format($transaction['xu_amount'], 0, ',', '.') . "</td>";
            echo "<td>" . htmlspecialchars($transaction['transaction_code']) . "</td>";
            echo "<td>" . date('d/m/Y H:i', strtotime($transaction['transaction_date'])) . "</td>";
            echo "</tr>";
        }

        echo "</table>";
    } else {
        echo "<p>Không có giao dịch nào đang chờ duyệt.</p>";
    }
} catch (PDOException $e) {
    echo "<h2 style='color:red'>Lỗi cơ sở dữ liệu</h2>";
    echo "<p>Lỗi: " . $e->getMessage() . "</p>";
}
