<?php
/**
 * Handle password reset
 */

// Start session
session_start();

// Include required files
require_once __DIR__ . '/config.php';
require_once __DIR__ . '/database.php';
require_once __DIR__ . '/utils.php';

// Get token from URL
$token = isset($_GET['token']) ? sanitizeInput($_GET['token']) : '';
$validToken = false;
$tokenExpired = false;
$userId = null;
$username = null;

// Check if token is provided
if (!empty($token)) {
    try {
        // Connect to database
        $conn = connectDB();
        
        // Verify token
        $tokenData = verifyResetToken($conn, $token);
        
        if ($tokenData) {
            $validToken = true;
            $userId = $tokenData['user_id'];
            
            // Get user information
            $stmt = $conn->prepare("SELECT username FROM team_user WHERE id = ?");
            $stmt->bind_param("i", $userId);
            $stmt->execute();
            $result = $stmt->get_result();
            $user = $result->fetch_assoc();
            $username = $user ? $user['username'] : 'Unknown';
            $stmt->close();
        } else {
            // Check if token exists but is expired
            $stmt = $conn->prepare("SELECT * FROM password_reset_tokens WHERE token = ? AND expires_at <= NOW()");
            $stmt->bind_param("s", $token);
            $stmt->execute();
            $result = $stmt->get_result();
            if ($result->fetch_assoc()) {
                $tokenExpired = true;
            }
            $stmt->close();
        }
    } catch (Exception $e) {
        debugLog("Error checking token: " . $e->getMessage());
    }
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $validToken) {
    $newPassword = isset($_POST['new_password']) ? $_POST['new_password'] : '';
    $confirmPassword = isset($_POST['confirm_password']) ? $_POST['confirm_password'] : '';
    
    // Validate passwords
    $passwordValidation = validatePassword($newPassword, $app_config['min_password_length']);
    
    if (!$passwordValidation['valid']) {
        $error = $passwordValidation['message'];
    } elseif (!passwordsMatch($newPassword, $confirmPassword)) {
        $error = 'Mật khẩu xác nhận không khớp.';
    } else {
        try {
            // Connect to database
            $conn = connectDB();
            
            // Hash password
            $hashedPassword = hashPassword($newPassword);
            
            // Update user password
            if (updateUserPassword($conn, $userId, $hashedPassword)) {
                // Mark token as used
                markTokenAsUsed($conn, $tokenData['id']);
                
                // Set success message
                $success = 'Mật khẩu của bạn đã được đặt lại thành công. Bạn có thể đăng nhập bằng mật khẩu mới.';
            } else {
                $error = 'Không thể cập nhật mật khẩu. Vui lòng thử lại sau.';
            }
        } catch (Exception $e) {
            debugLog("Error resetting password: " . $e->getMessage());
            $error = 'Có lỗi xảy ra trong quá trình xử lý. Vui lòng thử lại sau.';
        }
    }
}

// Include template
include __DIR__ . '/templates/reset_form.php';
