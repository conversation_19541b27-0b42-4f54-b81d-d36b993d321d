<?php
// Include database configuration
require_once 'Controllers/database.php';

// Connect to database
try {
    $conn = connectPDO($config1);
    echo "Connected to database successfully.<br>";
    
    // 1. Cậ<PERSON> nhật bảng card_transactions
    echo "<h3>Updating card_transactions table</h3>";
    
    // Kiểm tra xem cột transaction_id đã tồn tại chưa
    $columnCheck = $conn->query("SHOW COLUMNS FROM card_transactions LIKE 'transaction_id'");
    if ($columnCheck->rowCount() == 0) {
        $conn->exec("ALTER TABLE card_transactions ADD COLUMN transaction_id VARCHAR(100) AFTER id");
        echo "Added column transaction_id to card_transactions.<br>";
    } else {
        echo "Column transaction_id already exists in card_transactions.<br>";
    }
    
    // Kiểm tra xem cột card_serial đã tồn tại chưa
    $columnCheck = $conn->query("SHOW COLUMNS FROM card_transactions LIKE 'card_serial'");
    if ($columnCheck->rowCount() == 0) {
        $conn->exec("ALTER TABLE card_transactions ADD COLUMN card_serial VARCHAR(50) AFTER card_number");
        echo "Added column card_serial to card_transactions.<br>";
    } else {
        echo "Column card_serial already exists in card_transactions.<br>";
    }
    
    // Kiểm tra xem cột message đã tồn tại chưa
    $columnCheck = $conn->query("SHOW COLUMNS FROM card_transactions LIKE 'message'");
    if ($columnCheck->rowCount() == 0) {
        $conn->exec("ALTER TABLE card_transactions ADD COLUMN message TEXT AFTER notes");
        echo "Added column message to card_transactions.<br>";
    } else {
        echo "Column message already exists in card_transactions.<br>";
    }
    
    // Kiểm tra xem cột created_at đã tồn tại chưa
    $columnCheck = $conn->query("SHOW COLUMNS FROM card_transactions LIKE 'created_at'");
    if ($columnCheck->rowCount() == 0) {
        $conn->exec("ALTER TABLE card_transactions ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP AFTER message");
        echo "Added column created_at to card_transactions.<br>";
    } else {
        echo "Column created_at already exists in card_transactions.<br>";
    }
    
    // Cập nhật kiểu dữ liệu của cột status
    $conn->exec("ALTER TABLE card_transactions MODIFY COLUMN status ENUM('pending', 'success', 'approved', 'rejected', 'failed') DEFAULT 'pending'");
    echo "Updated status column in card_transactions.<br>";
    
    // 2. Tạo bảng balance_transactions nếu chưa tồn tại
    echo "<h3>Creating balance_transactions table</h3>";
    
    $tableCheck = $conn->query("SHOW TABLES LIKE 'balance_transactions'");
    if ($tableCheck->rowCount() == 0) {
        $sql = "CREATE TABLE balance_transactions (
            id INT(11) AUTO_INCREMENT PRIMARY KEY,
            user_id INT(11) NOT NULL,
            amount INT(11) NOT NULL,
            transaction_type ENUM('deposit', 'withdraw', 'transfer', 'purchase', 'refund', 'other') NOT NULL,
            description TEXT,
            status ENUM('pending', 'completed', 'failed') DEFAULT 'completed',
            reference_id VARCHAR(100),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX (user_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $conn->exec($sql);
        echo "Created balance_transactions table.<br>";
    } else {
        echo "Table balance_transactions already exists.<br>";
    }
    
    // 3. Cập nhật bảng user_balance
    echo "<h3>Updating user_balance table</h3>";
    
    // Kiểm tra xem cột updated_at đã tồn tại chưa
    $columnCheck = $conn->query("SHOW COLUMNS FROM user_balance LIKE 'updated_at'");
    if ($columnCheck->rowCount() == 0) {
        $conn->exec("ALTER TABLE user_balance ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER last_updated");
        echo "Added column updated_at to user_balance.<br>";
    } else {
        echo "Column updated_at already exists in user_balance.<br>";
    }
    
    // Đổi tên cột last_updated thành created_at nếu cần
    $columnCheck = $conn->query("SHOW COLUMNS FROM user_balance LIKE 'last_updated'");
    if ($columnCheck->rowCount() > 0) {
        $conn->exec("ALTER TABLE user_balance CHANGE COLUMN last_updated created_at DATETIME DEFAULT CURRENT_TIMESTAMP");
        echo "Renamed column last_updated to created_at in user_balance.<br>";
    }
    
    // Đảm bảo cột balance có kiểu dữ liệu phù hợp
    $conn->exec("ALTER TABLE user_balance MODIFY COLUMN balance INT(11) DEFAULT 0");
    echo "Updated balance column in user_balance to INT(11).<br>";
    
    // 4. Kiểm tra và cập nhật các bảng khác nếu cần
    
    echo "<h3>All database updates completed successfully!</h3>";
    
    // Hiển thị cấu trúc bảng sau khi cập nhật
    echo "<h3>Updated card_transactions structure:</h3>";
    $columns = $conn->query("SHOW COLUMNS FROM card_transactions");
    $columnList = $columns->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5' cellspacing='0'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    foreach ($columnList as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . ($column['Default'] === null ? 'NULL' : $column['Default']) . "</td>";
        echo "<td>" . $column['Extra'] . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage();
}
?>
