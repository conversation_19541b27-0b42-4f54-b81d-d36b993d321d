<?php
// Start the session at the beginning of the file
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['team_user']) || empty($_SESSION['team_user']) || !isset($_SESSION['is_admin']) || !$_SESSION['is_admin']) {
    header("Location: ../index.php?error=" . urlencode('Bạn không có quyền truy cập trang này.'));
    exit();
}

// Include database configuration
require_once '../Controllers/database.php';

// Connect to databases
try {
    // Connect to account database
    $conn = connectPDO($config1);

    // Connect to game_db database
    $conn1 = connectPDO($config2);
} catch (PDOException $e) {
    die("Lỗi kết nối cơ sở dữ liệu: " . $e->getMessage());
}

// Get user information
$userId = $_SESSION['id'];
$username = $_SESSION['team_user'];

// Initialize variables
$message = '';
$messageType = '';
$searchTerm = isset($_GET['search']) ? trim($_GET['search']) : '';
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$perPage = 10;
$offset = ($page - 1) * $perPage;

// Handle activation actions
if (isset($_GET['action'])) {
    $action = $_GET['action'];
    $targetUserId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

    // Activate user account
    if ($action === 'activate' && $targetUserId > 0) {
        try {
            // Get user details
            $stmt = $conn->prepare("SELECT * FROM team_user WHERE id = :id");
            $stmt->bindParam(':id', $targetUserId, PDO::PARAM_INT);
            $stmt->execute();
            $user = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$user) {
                throw new Exception('Không tìm thấy người dùng.');
            }

            // Check if user already has an activation record
            $stmt = $conn1->prepare("SELECT * FROM 5h_active WHERE userID = :userId OR username = :username");
            $stmt->bindParam(':userId', $targetUserId, PDO::PARAM_INT);
            $stmt->bindParam(':username', $user['username'], PDO::PARAM_STR);
            $stmt->execute();
            $activation = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($activation) {
                // Update existing activation record
                $stmt = $conn1->prepare("UPDATE 5h_active SET time_end = -1 WHERE userID = :userId OR username = :username");
                $stmt->bindParam(':userId', $targetUserId, PDO::PARAM_INT);
                $stmt->bindParam(':username', $user['username'], PDO::PARAM_STR);
                $stmt->execute();
            } else {
                // Create new activation record
                $stmt = $conn1->prepare("INSERT INTO 5h_active (userID, username, time_end) VALUES (:userId, :username, -1)");
                $stmt->bindParam(':userId', $targetUserId, PDO::PARAM_INT);
                $stmt->bindParam(':username', $user['username'], PDO::PARAM_STR);
                $stmt->execute();
            }

            $message = 'Đã kích hoạt tài khoản người dùng thành công.';
            $messageType = 'success';
        } catch (Exception $e) {
            $message = 'Lỗi khi kích hoạt tài khoản: ' . $e->getMessage();
            $messageType = 'danger';
        }
    }

    // Deactivate user account
    if ($action === 'deactivate' && $targetUserId > 0) {
        try {
            // Get user details
            $stmt = $conn->prepare("SELECT * FROM team_user WHERE id = :id");
            $stmt->bindParam(':id', $targetUserId, PDO::PARAM_INT);
            $stmt->execute();
            $user = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$user) {
                throw new Exception('Không tìm thấy người dùng.');
            }

            // Check if user has an activation record
            $stmt = $conn1->prepare("SELECT * FROM 5h_active WHERE userID = :userId OR username = :username");
            $stmt->bindParam(':userId', $targetUserId, PDO::PARAM_INT);
            $stmt->bindParam(':username', $user['username'], PDO::PARAM_STR);
            $stmt->execute();
            $activation = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($activation) {
                // Update activation record to deactivate
                $stmt = $conn1->prepare("UPDATE 5h_active SET time_end = 0 WHERE userID = :userId OR username = :username");
                $stmt->bindParam(':userId', $targetUserId, PDO::PARAM_INT);
                $stmt->bindParam(':username', $user['username'], PDO::PARAM_STR);
                $stmt->execute();

                $message = 'Đã hủy kích hoạt tài khoản người dùng thành công.';
                $messageType = 'success';
            } else {
                $message = 'Người dùng chưa được kích hoạt.';
                $messageType = 'warning';
            }
        } catch (Exception $e) {
            $message = 'Lỗi khi hủy kích hoạt tài khoản: ' . $e->getMessage();
            $messageType = 'danger';
        }
    }

    // Temporary activation
    if ($action === 'temp_activate' && $targetUserId > 0) {
        try {
            // Get user details
            $stmt = $conn->prepare("SELECT * FROM team_user WHERE id = :id");
            $stmt->bindParam(':id', $targetUserId, PDO::PARAM_INT);
            $stmt->execute();
            $user = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$user) {
                throw new Exception('Không tìm thấy người dùng.');
            }

            // Calculate end time (current time + 24 hours)
            $endTime = time() + (24 * 60 * 60);

            // Check if user already has an activation record
            $stmt = $conn1->prepare("SELECT * FROM 5h_active WHERE userID = :userId OR username = :username");
            $stmt->bindParam(':userId', $targetUserId, PDO::PARAM_INT);
            $stmt->bindParam(':username', $user['username'], PDO::PARAM_STR);
            $stmt->execute();
            $activation = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($activation) {
                // Update existing activation record
                $stmt = $conn1->prepare("UPDATE 5h_active SET time_end = :endTime WHERE userID = :userId OR username = :username");
                $stmt->bindParam(':endTime', $endTime, PDO::PARAM_INT);
                $stmt->bindParam(':userId', $targetUserId, PDO::PARAM_INT);
                $stmt->bindParam(':username', $user['username'], PDO::PARAM_STR);
                $stmt->execute();
            } else {
                // Create new activation record
                $stmt = $conn1->prepare("INSERT INTO 5h_active (userID, username, time_end) VALUES (:userId, :username, :endTime)");
                $stmt->bindParam(':userId', $targetUserId, PDO::PARAM_INT);
                $stmt->bindParam(':username', $user['username'], PDO::PARAM_STR);
                $stmt->bindParam(':endTime', $endTime, PDO::PARAM_INT);
                $stmt->execute();
            }

            $message = 'Đã kích hoạt tạm thời tài khoản người dùng thành công (24 giờ).';
            $messageType = 'success';
        } catch (Exception $e) {
            $message = 'Lỗi khi kích hoạt tạm thời tài khoản: ' . $e->getMessage();
            $messageType = 'danger';
        }
    }
}

// Get users with activation status
$users = [];
$totalUsers = 0;

try {
    // Build query based on search term
    $query = "SELECT tu.*,
              CASE
                WHEN 5a.time_end = -1 THEN 'activated'
                WHEN 5a.time_end > 0 THEN 'temp_activated'
                ELSE 'not_activated'
              END AS activation_status,
              5a.time_end
              FROM team_user tu
              LEFT JOIN " . $config2['database'] . ".5h_active 5a ON tu.id = 5a.userID OR tu.username = 5a.username";

    $countQuery = "SELECT COUNT(*) FROM team_user tu";
    $params = [];

    if (!empty($searchTerm)) {
        $query .= " WHERE tu.username LIKE :search OR tu.email LIKE :search";
        $countQuery .= " WHERE tu.username LIKE :search OR tu.email LIKE :search";
        $params[':search'] = "%$searchTerm%";
    }

    $query .= " ORDER BY tu.regdate DESC LIMIT :offset, :perPage";

    // Get total count for pagination
    $stmt = $conn->prepare($countQuery);
    if (!empty($searchTerm)) {
        $stmt->bindParam(':search', $params[':search'], PDO::PARAM_STR);
    }
    $stmt->execute();
    $totalUsers = $stmt->fetchColumn();

    // Get users for current page
    $stmt = $conn->prepare($query);
    if (!empty($searchTerm)) {
        $stmt->bindParam(':search', $params[':search'], PDO::PARAM_STR);
    }
    $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
    $stmt->bindParam(':perPage', $perPage, PDO::PARAM_INT);
    $stmt->execute();
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $message = 'Lỗi khi lấy danh sách người dùng: ' . $e->getMessage();
    $messageType = 'danger';
}

// Calculate pagination
$totalPages = ceil($totalUsers / $perPage);
?>

<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kích hoạt tài khoản - KPAH</title>
    <link href="../assets/homepage/css/bootstrap.min.css" rel="stylesheet" type="text/css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <script src="../assets/homepage/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/homepage/js/jquery-1.9.1.min.js"></script>
    <style>
        body {
            background-color: #1a1a1a;
            color: #f9d686;
            font-family: Arial, sans-serif;
        }

        .admin-container {
            padding: 20px;
        }

        .admin-header {
            background-color: #000;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border: 1px solid #f9d686;
        }

        .admin-title {
            margin: 0;
            color: #f9d686;
            font-weight: bold;
        }

        .admin-user {
            color: #fff;
        }

        .admin-card {
            background-color: #2a2a2a;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #f9d686;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .admin-card h2 {
            color: #f9d686;
            font-size: 1.5rem;
            margin-bottom: 15px;
            border-bottom: 1px solid #f9d686;
            padding-bottom: 10px;
        }

        .admin-table {
            width: 100%;
            margin-top: 15px;
        }

        .admin-table th {
            background-color: #333;
            color: #f9d686;
            padding: 10px;
            text-align: left;
        }

        .admin-table td {
            padding: 10px;
            border-bottom: 1px solid #444;
        }

        .admin-table tr:hover td {
            background-color: #3a3a3a;
        }

        .btn-admin {
            background-color: #f9d686;
            color: #000;
            border: none;
            font-weight: bold;
            padding: 8px 16px;
            margin-right: 5px;
            margin-bottom: 5px;
        }

        .btn-admin:hover {
            background-color: #e5c677;
            color: #000;
        }

        .btn-danger {
            background-color: #dc3545;
            color: #fff;
        }

        .btn-danger:hover {
            background-color: #c82333;
            color: #fff;
        }

        .btn-success {
            background-color: #28a745;
            color: #fff;
        }

        .btn-success:hover {
            background-color: #218838;
            color: #fff;
        }

        .btn-warning {
            background-color: #ffc107;
            color: #000;
        }

        .btn-warning:hover {
            background-color: #e0a800;
            color: #000;
        }

        .admin-menu {
            background-color: #2a2a2a;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid #f9d686;
        }

        .admin-menu ul {
            list-style: none;
            padding: 0;
            margin: 0;
            display: flex;
            flex-wrap: wrap;
        }

        .admin-menu li {
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .admin-menu a {
            display: block;
            padding: 8px 15px;
            background-color: #333;
            color: #f9d686;
            text-decoration: none;
            border-radius: 3px;
            border: 1px solid rgba(249, 214, 134, 0.3);
            transition: all 0.3s ease;
        }

        .admin-menu a:hover,
        .admin-menu a.active {
            background-color: #f9d686;
            color: #000;
        }

        .admin-menu a i {
            margin-right: 5px;
        }

        .search-form {
            margin-bottom: 20px;
        }

        .search-form .form-control {
            background-color: #333;
            border: 1px solid #f9d686;
            color: #fff;
        }

        .search-form .form-control:focus {
            box-shadow: 0 0 0 0.2rem rgba(249, 214, 134, 0.25);
        }

        .pagination {
            margin-top: 20px;
            justify-content: center;
        }

        .pagination .page-item .page-link {
            background-color: #333;
            border-color: #f9d686;
            color: #f9d686;
        }

        .pagination .page-item.active .page-link {
            background-color: #f9d686;
            border-color: #f9d686;
            color: #000;
        }

        .pagination .page-item .page-link:hover {
            background-color: #444;
        }

        .status-activated {
            color: #28a745;
            font-weight: bold;
        }

        .status-temp-activated {
            color: #ffc107;
            font-weight: bold;
        }

        .status-not-activated {
            color: #dc3545;
            font-weight: bold;
        }

        @media (max-width: 768px) {
            .admin-header {
                flex-direction: column;
                text-align: center;
            }

            .admin-user {
                margin-top: 10px;
            }

            .admin-menu ul {
                flex-direction: column;
            }

            .admin-menu li {
                margin-right: 0;
                margin-bottom: 5px;
            }

            .btn-admin {
                display: block;
                width: 100%;
                margin-bottom: 5px;
            }
        }
    </style>
</head>

<body>
    <div class="container admin-container">
        <div class="admin-header">
            <h1 class="admin-title">KPAH - Kích hoạt tài khoản</h1>
            <div class="admin-user">
                Xin chào, <strong><?php echo htmlspecialchars($username); ?></strong> |
                <a href="../Views/Account.php" class="text-light">Tài khoản</a> |
                <a href="../handle/logout.php" class="text-light">Đăng xuất</a>
            </div>
        </div>

        <div class="admin-menu">
            <ul>
                <li><a href="index.php"><i class="fas fa-tachometer-alt"></i> Tổng quan</a></li>
                <li><a href="users.php"><i class="fas fa-users"></i> Quản lý người dùng</a></li>
                <li><a href="bank_topups.php"><i class="fas fa-university"></i> Quản lý nạp ATM</a></li>
                <li><a href="bank_accounts.php"><i class="fas fa-credit-card"></i> Quản lý tài khoản ngân hàng</a></li>
                <li><a href="activations.php" class="active"><i class="fas fa-check-circle"></i> Kích hoạt tài khoản</a></li>
                <li><a href="giftcodes.php"><i class="fas fa-gift"></i> Quản lý Giftcode</a></li>
                <li><a href="api_management.php"><i class="fas fa-plug"></i> Quản lý API</a></li>
                <li><a href="password_decoder.php"><i class="fas fa-key"></i> Giải mã mật khẩu</a></li>
                <li><a href="site_settings.php"><i class="fas fa-cogs"></i> Cấu hình trang web</a></li>

                <li><a href="../index.php"><i class="fas fa-home"></i> Về trang chủ</a></li>
            </ul>
        </div>

        <?php if (!empty($message)): ?>
            <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                <?php echo $message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <div class="admin-card">
            <h2><i class="fas fa-check-circle"></i> Danh sách tài khoản</h2>

            <form class="search-form" method="get" action="activations.php">
                <div class="input-group">
                    <input type="text" class="form-control" placeholder="Tìm kiếm theo tên người dùng, email..."
                        name="search" value="<?php echo htmlspecialchars($searchTerm); ?>">
                    <button class="btn btn-admin" type="submit">
                        <i class="fas fa-search"></i> Tìm kiếm
                    </button>
                </div>
            </form>

            <?php if (empty($users)): ?>
                <p>Không có người dùng nào.</p>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="admin-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Tên đăng nhập</th>
                                <th>Email</th>
                                <th>Ngày đăng ký</th>
                                <th>Trạng thái</th>
                                <th>Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($users as $user): ?>
                                <tr>
                                    <td><?php echo $user['id']; ?></td>
                                    <td><?php echo htmlspecialchars($user['username']); ?></td>
                                    <td><?php echo htmlspecialchars($user['email'] ?? 'Chưa có'); ?></td>
                                    <td><?php echo date('d/m/Y', strtotime($user['regdate'])); ?></td>
                                    <td>
                                        <?php
                                        if (isset($user['activation_status'])) {
                                            switch ($user['activation_status']) {
                                                case 'activated':
                                                    echo '<span class="status-activated">Đã kích hoạt</span>';
                                                    break;
                                                case 'temp_activated':
                                                    $endTime = $user['time_end'];
                                                    $endDate = date('d/m/Y H:i', $endTime);
                                                    echo '<span class="status-temp-activated">Kích hoạt tạm thời (đến ' . $endDate . ')</span>';
                                                    break;
                                                default:
                                                    echo '<span class="status-not-activated">Chưa kích hoạt</span>';
                                            }
                                        } else {
                                            echo '<span class="status-not-activated">Chưa kích hoạt</span>';
                                        }
                                        ?>
                                    </td>
                                    <td>
                                        <?php if (!isset($user['activation_status']) || $user['activation_status'] === 'not_activated'): ?>
                                            <a href="activations.php?action=activate&id=<?php echo $user['id']; ?>"
                                                class="btn btn-sm btn-success"
                                                onclick="return confirm('Bạn có chắc chắn muốn kích hoạt tài khoản này?')">
                                                <i class="fas fa-check"></i> Kích hoạt
                                            </a>
                                            <a href="activations.php?action=temp_activate&id=<?php echo $user['id']; ?>"
                                                class="btn btn-sm btn-warning"
                                                onclick="return confirm('Bạn có chắc chắn muốn kích hoạt tạm thời tài khoản này (24 giờ)?')">
                                                <i class="fas fa-clock"></i> Kích hoạt tạm thời
                                            </a>
                                        <?php elseif ($user['activation_status'] === 'activated'): ?>
                                            <a href="activations.php?action=deactivate&id=<?php echo $user['id']; ?>"
                                                class="btn btn-sm btn-danger"
                                                onclick="return confirm('Bạn có chắc chắn muốn hủy kích hoạt tài khoản này?')">
                                                <i class="fas fa-times"></i> Hủy kích hoạt
                                            </a>
                                        <?php elseif ($user['activation_status'] === 'temp_activated'): ?>
                                            <a href="activations.php?action=activate&id=<?php echo $user['id']; ?>"
                                                class="btn btn-sm btn-success"
                                                onclick="return confirm('Bạn có chắc chắn muốn kích hoạt vĩnh viễn tài khoản này?')">
                                                <i class="fas fa-check"></i> Kích hoạt vĩnh viễn
                                            </a>
                                            <a href="activations.php?action=deactivate&id=<?php echo $user['id']; ?>"
                                                class="btn btn-sm btn-danger"
                                                onclick="return confirm('Bạn có chắc chắn muốn hủy kích hoạt tài khoản này?')">
                                                <i class="fas fa-times"></i> Hủy kích hoạt
                                            </a>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <?php if ($totalPages > 1): ?>
                    <nav aria-label="Page navigation">
                        <ul class="pagination">
                            <?php if ($page > 1): ?>
                                <li class="page-item">
                                    <a class="page-link"
                                        href="activations.php?page=<?php echo ($page - 1); ?>&search=<?php echo urlencode($searchTerm); ?>"
                                        aria-label="Previous">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                            <?php endif; ?>

                            <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                <li class="page-item <?php echo ($i === $page) ? 'active' : ''; ?>">
                                    <a class="page-link"
                                        href="activations.php?page=<?php echo $i; ?>&search=<?php echo urlencode($searchTerm); ?>">
                                        <?php echo $i; ?>
                                    </a>
                                </li>
                            <?php endfor; ?>

                            <?php if ($page < $totalPages): ?>
                                <li class="page-item">
                                    <a class="page-link"
                                        href="activations.php?page=<?php echo ($page + 1); ?>&search=<?php echo urlencode($searchTerm); ?>"
                                        aria-label="Next">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            // Auto-dismiss alerts after 5 seconds
            setTimeout(function() {
                $('.alert').alert('close');
            }, 5000);
        });
    </script>
</body>

</html>