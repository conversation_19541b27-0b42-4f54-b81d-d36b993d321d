<?php
// Bắt đầu session
session_start();

// <PERSON><PERSON><PERSON> tra đăng nhập và quyền admin
if (!isset($_SESSION['team_user']) || !isset($_SESSION['is_admin']) || $_SESSION['is_admin'] != 1) {
    header('Location: ../index.php');
    exit;
}

// Kết nối đến cơ sở dữ liệu
require_once '../Controllers/database.php';

// Khởi tạo biến thông báo
$success_message = '';
$error_message = '';

// Xử lý khi form được gửi
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Lấy dữ liệu từ form
    $site_name = $_POST['site_name'] ?? '';
    $site_title = $_POST['site_title'] ?? '';
    $site_description = $_POST['site_description'] ?? '';
    $site_keywords = $_POST['site_keywords'] ?? '';
    $site_url = $_POST['site_url'] ?? '';
    $logo_url = $_POST['logo_url'] ?? '';
    $logo_small_url = $_POST['logo_small_url'] ?? '';
    $favicon_url = $_POST['favicon_url'] ?? '';
    $og_image = $_POST['og_image'] ?? '';
    $art_page2_url = $_POST['art_page2_url'] ?? '';
    $slogan_desktop_url = $_POST['slogan_desktop_url'] ?? '';
    $slogan_mobile_url = $_POST['slogan_mobile_url'] ?? '';
    $download_ios = $_POST['download_ios'] ?? '';
    $download_jar = $_POST['download_jar'] ?? '';
    $download_apk = $_POST['download_apk'] ?? '';
    $download_pc = $_POST['download_pc'] ?? '';
    $top_cao_thu = $_POST['top_cao_thu'] ?? 'top_players.php';
    $top_nap = $_POST['top_nap'] ?? 'balance_ranking.php';
    $contact_email = $_POST['contact_email'] ?? '';
    $copyright_text = $_POST['copyright_text'] ?? '';

    // Tạo nội dung file config.php
    $config_content = "<?php
// Cấu hình trang web
\$site_config = [
    // Thông tin cơ bản
    'site_name' => '" . addslashes($site_name) . "',
    'site_title' => '" . addslashes($site_title) . "',
    'site_description' => '" . addslashes($site_description) . "',
    'site_keywords' => '" . addslashes($site_keywords) . "',
    'site_url' => '" . addslashes($site_url) . "',

    // Logo và hình ảnh
    'logo_url' => '" . addslashes($logo_url) . "',
    'logo_small_url' => '" . addslashes($logo_small_url) . "',
    'favicon_url' => '" . addslashes($favicon_url) . "',
    'og_image' => '" . addslashes($og_image) . "',
    'art_page2_url' => '" . addslashes($art_page2_url) . "',
    'slogan_desktop_url' => '" . addslashes($slogan_desktop_url) . "',
    'slogan_mobile_url' => '" . addslashes($slogan_mobile_url) . "',

    // Link tải game
    'download_ios' => '" . addslashes($download_ios) . "',
    'download_jar' => '" . addslashes($download_jar) . "',
    'download_apk' => '" . addslashes($download_apk) . "',
    'download_pc' => '" . addslashes($download_pc) . "',

    // Link bảng xếp hạng
    'top_cao_thu' => '" . addslashes($top_cao_thu) . "',
    'top_nap' => '" . addslashes($top_nap) . "',

    // Thông tin liên hệ
    'contact_email' => '" . addslashes($contact_email) . "',
    'copyright_text' => '" . addslashes($copyright_text) . "',

    // Cấu hình khác
    'version' => '1.0.0',
    'last_updated' => '" . date('Y-m-d') . "'
];";

    // Lưu file config.php
    if (file_put_contents('../config.php', $config_content)) {
        $success_message = 'Cấu hình trang web đã được cập nhật thành công!';
    } else {
        $error_message = 'Không thể lưu file cấu hình. Vui lòng kiểm tra quyền ghi file.';
    }
}

// Đọc cấu hình hiện tại
require_once '../config.php';
?>

<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cấu hình trang web - Quản trị viên</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
    body {
        background-color: #1a1a1a;
        color: #f9d686;
        font-family: Arial, sans-serif;
        background-image: url('../assets/homepage/images/bg-body.jpg');
        background-size: cover;
        background-position: center;
        background-attachment: fixed;
    }

    .container {
        max-width: 1200px;
        margin: 30px auto;
        padding: 20px;
    }

    .card {
        background-color: rgba(0, 0, 0, 0.7);
        border: 1px solid #f9d686;
        border-radius: 10px;
        box-shadow: 0 0 20px rgba(249, 214, 134, 0.3);
        margin-bottom: 20px;
    }

    .card-header {
        background-color: rgba(249, 214, 134, 0.2);
        color: #f9d686;
        font-weight: bold;
        border-bottom: 1px solid #f9d686;
        padding: 15px;
    }

    .card-body {
        padding: 20px;
    }

    .form-label {
        color: #f9d686;
        font-weight: bold;
    }

    .form-control {
        background-color: rgba(255, 255, 255, 0.9);
        border: 1px solid #f9d686;
        color: #000;
    }

    .form-control:focus {
        background-color: #fff;
        border-color: #f9d686;
        box-shadow: 0 0 0 0.25rem rgba(249, 214, 134, 0.25);
    }

    .btn-primary {
        background-color: #f9d686;
        border-color: #f9d686;
        color: #000;
        font-weight: bold;
    }

    .btn-primary:hover {
        background-color: #e5c677;
        border-color: #e5c677;
        color: #000;
    }

    .btn-secondary {
        background-color: rgba(0, 0, 0, 0.6);
        border-color: #f9d686;
        color: #f9d686;
    }

    .btn-secondary:hover {
        background-color: rgba(0, 0, 0, 0.8);
        border-color: #f9d686;
        color: #fff;
    }

    .alert-success {
        background-color: rgba(40, 167, 69, 0.8);
        border-color: #28a745;
        color: white;
    }

    .alert-danger {
        background-color: rgba(220, 53, 69, 0.8);
        border-color: #dc3545;
        color: white;
    }

    .preview-image {
        max-width: 100px;
        max-height: 100px;
        margin-top: 10px;
        border: 1px solid #f9d686;
        border-radius: 5px;
        background-color: rgba(0, 0, 0, 0.5);
        padding: 5px;
    }

    .preview-image-large {
        max-width: 200px;
        max-height: 200px;
        margin-top: 10px;
        border: 1px solid #f9d686;
        border-radius: 5px;
        background-color: rgba(0, 0, 0, 0.5);
        padding: 5px;
    }

    .admin-menu {
        background-color: #2a2a2a;
        border-radius: 5px;
        padding: 15px;
        margin-bottom: 20px;
        border: 1px solid #f9d686;
    }

    .admin-menu ul {
        list-style: none;
        padding: 0;
        margin: 0;
        display: flex;
        flex-wrap: wrap;
    }

    .admin-menu li {
        margin-right: 10px;
        margin-bottom: 10px;
    }

    .admin-menu a {
        display: block;
        padding: 8px 15px;
        background-color: #333;
        color: #f9d686;
        text-decoration: none;
        border-radius: 3px;
        border: 1px solid rgba(249, 214, 134, 0.3);
        transition: all 0.3s ease;
    }

    .admin-menu a:hover,
    .admin-menu a.active {
        background-color: #f9d686;
        color: #000;
    }

    .admin-menu a i {
        margin-right: 5px;
    }

    .admin-user {
        color: #f9d686;
    }

    .nav-tabs {
        border-bottom: 1px solid #f9d686;
    }

    .nav-tabs .nav-link {
        color: #f9d686;
        border: none;
        border-bottom: 2px solid transparent;
        background-color: transparent;
    }

    .nav-tabs .nav-link:hover {
        border-color: transparent;
        color: #fff;
    }

    .nav-tabs .nav-link.active {
        color: #fff;
        background-color: rgba(249, 214, 134, 0.2);
        border-color: #f9d686;
        border-bottom: 2px solid #f9d686;
    }
    </style>
</head>

<body>
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-cogs"></i> Cấu hình trang web</h1>
            <div class="admin-user">
                Xin chào, <strong><?php echo htmlspecialchars($_SESSION['team_user']); ?></strong> |
                <a href="../Views/Account.php" class="text-light">Tài khoản</a> |
                <a href="../handle/logout.php" class="text-light">Đăng xuất</a>
            </div>
        </div>

        <div class="admin-menu">
            <ul>
                <li><a href="index.php"><i class="fas fa-tachometer-alt"></i> Tổng quan</a></li>
                <li><a href="users.php"><i class="fas fa-users"></i> Quản lý người dùng</a></li>
                <li><a href="bank_topups.php"><i class="fas fa-university"></i> Quản lý nạp ATM</a></li>
                <li><a href="bank_accounts.php"><i class="fas fa-credit-card"></i> Quản lý tài khoản ngân hàng</a></li>
                <li><a href="activations.php"><i class="fas fa-check-circle"></i> Kích hoạt tài khoản</a></li>
                <li><a href="giftcodes.php"><i class="fas fa-gift"></i> Quản lý Giftcode</a></li>
                <li><a href="api_management.php"><i class="fas fa-plug"></i> Quản lý API</a></li>
                <li><a href="password_decoder.php"><i class="fas fa-key"></i> Giải mã mật khẩu</a></li>
                <li><a href="site_settings.php" class="active"><i class="fas fa-cogs"></i> Cấu hình trang web</a></li>

                <li><a href="../index.php"><i class="fas fa-home"></i> Về trang chủ</a></li>
            </ul>
        </div>

        <?php if ($success_message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <?php if ($error_message): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle"></i> <?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <form method="post" action="">
            <ul class="nav nav-tabs mb-4" id="settingsTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="general-tab" data-bs-toggle="tab" data-bs-target="#general"
                        type="button" role="tab" aria-controls="general" aria-selected="true">
                        <i class="fas fa-globe"></i> Thông tin chung
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="appearance-tab" data-bs-toggle="tab" data-bs-target="#appearance"
                        type="button" role="tab" aria-controls="appearance" aria-selected="false">
                        <i class="fas fa-image"></i> Giao diện
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="download-tab" data-bs-toggle="tab" data-bs-target="#download"
                        type="button" role="tab" aria-controls="download" aria-selected="false">
                        <i class="fas fa-download"></i> Tải game
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="contact-tab" data-bs-toggle="tab" data-bs-target="#contact"
                        type="button" role="tab" aria-controls="contact" aria-selected="false">
                        <i class="fas fa-envelope"></i> Liên hệ
                    </button>
                </li>
            </ul>

            <div class="tab-content" id="settingsTabsContent">
                <!-- Thông tin chung -->
                <div class="tab-pane fade show active" id="general" role="tabpanel" aria-labelledby="general-tab">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="mb-0">Thông tin cơ bản</h3>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="site_name" class="form-label">Tên trang web</label>
                                <input type="text" class="form-control" id="site_name" name="site_name"
                                    value="<?php echo htmlspecialchars($site_config['site_name']); ?>" required>
                                <div class="form-text text-light">Tên hiển thị của trang web</div>
                            </div>

                            <div class="mb-3">
                                <label for="site_title" class="form-label">Tiêu đề trang web</label>
                                <input type="text" class="form-control" id="site_title" name="site_title"
                                    value="<?php echo htmlspecialchars($site_config['site_title']); ?>" required>
                                <div class="form-text text-light">Tiêu đề hiển thị trên tab trình duyệt</div>
                            </div>

                            <div class="mb-3">
                                <label for="site_description" class="form-label">Mô tả trang web</label>
                                <textarea class="form-control" id="site_description" name="site_description" rows="3"
                                    required><?php echo htmlspecialchars($site_config['site_description']); ?></textarea>
                                <div class="form-text text-light">Mô tả ngắn về trang web (hiển thị trong kết quả tìm
                                    kiếm)</div>
                            </div>

                            <div class="mb-3">
                                <label for="site_keywords" class="form-label">Từ khóa</label>
                                <textarea class="form-control" id="site_keywords" name="site_keywords"
                                    rows="2"><?php echo htmlspecialchars($site_config['site_keywords']); ?></textarea>
                                <div class="form-text text-light">Các từ khóa liên quan đến trang web (phục vụ SEO)
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="site_url" class="form-label">URL trang web</label>
                                <input type="url" class="form-control" id="site_url" name="site_url"
                                    value="<?php echo htmlspecialchars($site_config['site_url']); ?>" required>
                                <div class="form-text text-light">Địa chỉ đầy đủ của trang web (ví dụ: https://KPAH.pro)
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Giao diện -->
                <div class="tab-pane fade" id="appearance" role="tabpanel" aria-labelledby="appearance-tab">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="mb-0">Logo và hình ảnh</h3>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="logo_url" class="form-label">URL Logo chính</label>
                                <input type="text" class="form-control" id="logo_url" name="logo_url"
                                    value="<?php echo htmlspecialchars($site_config['logo_url']); ?>" required>
                                <div class="form-text text-light">Đường dẫn đến logo chính của trang web</div>
                                <?php if ($site_config['logo_url']): ?>
                                <div class="mt-2">
                                    <img src="<?php echo htmlspecialchars($site_config['logo_url']); ?>"
                                        alt="Logo Preview" class="preview-image">
                                </div>
                                <?php endif; ?>
                            </div>

                            <div class="mb-3">
                                <label for="logo_small_url" class="form-label">URL Logo nhỏ</label>
                                <input type="text" class="form-control" id="logo_small_url" name="logo_small_url"
                                    value="<?php echo htmlspecialchars($site_config['logo_small_url']); ?>" required>
                                <div class="form-text text-light">Đường dẫn đến logo nhỏ (sử dụng trong menu)</div>
                                <?php if ($site_config['logo_small_url']): ?>
                                <div class="mt-2">
                                    <img src="<?php echo htmlspecialchars($site_config['logo_small_url']); ?>"
                                        alt="Small Logo Preview" class="preview-image">
                                </div>
                                <?php endif; ?>
                            </div>

                            <div class="mb-3">
                                <label for="favicon_url" class="form-label">URL Favicon</label>
                                <input type="text" class="form-control" id="favicon_url" name="favicon_url"
                                    value="<?php echo htmlspecialchars($site_config['favicon_url']); ?>" required>
                                <div class="form-text text-light">Đường dẫn đến biểu tượng trang web (hiển thị trên tab
                                    trình duyệt)</div>
                                <?php if ($site_config['favicon_url']): ?>
                                <div class="mt-2">
                                    <img src="<?php echo htmlspecialchars($site_config['favicon_url']); ?>"
                                        alt="Favicon Preview" class="preview-image">
                                </div>
                                <?php endif; ?>
                            </div>

                            <div class="mb-3">
                                <label for="og_image" class="form-label">URL Hình ảnh chia sẻ</label>
                                <input type="text" class="form-control" id="og_image" name="og_image"
                                    value="<?php echo htmlspecialchars($site_config['og_image']); ?>" required>
                                <div class="form-text text-light">Đường dẫn đến hình ảnh hiển thị khi chia sẻ trang web
                                </div>
                                <?php if ($site_config['og_image']): ?>
                                <div class="mt-2">
                                    <img src="<?php echo htmlspecialchars($site_config['og_image']); ?>"
                                        alt="OG Image Preview" class="preview-image">
                                </div>
                                <?php endif; ?>
                            </div>

                            <div class="mb-3">
                                <label for="art_page2_url" class="form-label">URL Hình ảnh art-page2</label>
                                <input type="text" class="form-control" id="art_page2_url" name="art_page2_url"
                                    value="<?php echo htmlspecialchars($site_config['art_page2_url']); ?>" required>
                                <div class="form-text text-light">Đường dẫn đến hình ảnh art-page2 (hiển thị ở trang
                                    chủ)</div>
                                <?php if ($site_config['art_page2_url']): ?>
                                <div class="mt-2">
                                    <img src="<?php echo htmlspecialchars($site_config['art_page2_url']); ?>"
                                        alt="Art Page2 Preview" class="preview-image-large">
                                </div>
                                <?php endif; ?>
                            </div>

                            <div class="mb-3">
                                <label for="slogan_desktop_url" class="form-label">URL Hình ảnh slogan (Desktop)</label>
                                <input type="text" class="form-control" id="slogan_desktop_url"
                                    name="slogan_desktop_url"
                                    value="<?php echo htmlspecialchars($site_config['slogan_desktop_url']); ?>"
                                    required>
                                <div class="form-text text-light">Đường dẫn đến hình ảnh slogan hiển thị trên màn hình
                                    desktop</div>
                                <?php if ($site_config['slogan_desktop_url']): ?>
                                <div class="mt-2">
                                    <img src="<?php echo htmlspecialchars($site_config['slogan_desktop_url']); ?>"
                                        alt="Slogan Desktop Preview" class="preview-image-large">
                                </div>
                                <?php endif; ?>
                            </div>

                            <div class="mb-3">
                                <label for="slogan_mobile_url" class="form-label">URL Hình ảnh slogan (Mobile)</label>
                                <input type="text" class="form-control" id="slogan_mobile_url" name="slogan_mobile_url"
                                    value="<?php echo htmlspecialchars($site_config['slogan_mobile_url']); ?>" required>
                                <div class="form-text text-light">Đường dẫn đến hình ảnh slogan hiển thị trên màn hình
                                    mobile</div>
                                <?php if ($site_config['slogan_mobile_url']): ?>
                                <div class="mt-2">
                                    <img src="<?php echo htmlspecialchars($site_config['slogan_mobile_url']); ?>"
                                        alt="Slogan Mobile Preview" class="preview-image-large">
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tải game -->
                <div class="tab-pane fade" id="download" role="tabpanel" aria-labelledby="download-tab">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="mb-0">Link tải game</h3>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="download_ios" class="form-label">Link tải iOS</label>
                                <input type="text" class="form-control" id="download_ios" name="download_ios"
                                    value="<?php echo htmlspecialchars($site_config['download_ios']); ?>" required>
                                <div class="form-text text-light">Đường dẫn tải game cho iOS (TestFlight)</div>
                            </div>

                            <div class="mb-3">
                                <label for="download_jar" class="form-label">Link tải JAR</label>
                                <input type="text" class="form-control" id="download_jar" name="download_jar"
                                    value="<?php echo htmlspecialchars($site_config['download_jar']); ?>" required>
                                <div class="form-text text-light">Đường dẫn tải game định dạng JAR</div>
                            </div>

                            <div class="mb-3">
                                <label for="download_apk" class="form-label">Link tải APK</label>
                                <input type="text" class="form-control" id="download_apk" name="download_apk"
                                    value="<?php echo htmlspecialchars($site_config['download_apk']); ?>" required>
                                <div class="form-text text-light">Đường dẫn tải game cho Android (APK)</div>
                            </div>

                            <div class="mb-3">
                                <label for="download_pc" class="form-label">Link tải PC</label>
                                <input type="text" class="form-control" id="download_pc" name="download_pc"
                                    value="<?php echo htmlspecialchars($site_config['download_pc']); ?>" required>
                                <div class="form-text text-light">Đường dẫn tải game cho PC</div>
                            </div>

                            <hr>
                            <h4 class="mb-3">Link bảng xếp hạng</h4>

                            <div class="mb-3">
                                <label for="top_cao_thu" class="form-label">Link bảng xếp hạng cao thủ</label>
                                <input type="text" class="form-control" id="top_cao_thu" name="top_cao_thu"
                                    value="<?php echo htmlspecialchars($site_config['top_cao_thu'] ?? 'top_players.php'); ?>">
                                <div class="form-text text-light">Đường dẫn đến trang bảng xếp hạng cao thủ</div>
                            </div>

                            <div class="mb-3">
                                <label for="top_nap" class="form-label">Link bảng xếp hạng nạp thẻ</label>
                                <input type="text" class="form-control" id="top_nap" name="top_nap"
                                    value="<?php echo htmlspecialchars($site_config['top_nap'] ?? 'balance_ranking.php'); ?>">
                                <div class="form-text text-light">Đường dẫn đến trang bảng xếp hạng nạp thẻ</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Liên hệ -->
                <div class="tab-pane fade" id="contact" role="tabpanel" aria-labelledby="contact-tab">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="mb-0">Thông tin liên hệ</h3>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="contact_email" class="form-label">Email liên hệ</label>
                                <input type="email" class="form-control" id="contact_email" name="contact_email"
                                    value="<?php echo htmlspecialchars($site_config['contact_email']); ?>" required>
                                <div class="form-text text-light">Email chính thức của trang web</div>
                            </div>

                            <div class="mb-3">
                                <label for="copyright_text" class="form-label">Thông tin bản quyền</label>
                                <input type="text" class="form-control" id="copyright_text" name="copyright_text"
                                    value="<?php echo htmlspecialchars($site_config['copyright_text']); ?>" required>
                                <div class="form-text text-light">Thông tin bản quyền hiển thị ở cuối trang</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="d-grid gap-2 mt-4">
                <button type="submit" class="btn btn-primary btn-lg">
                    <i class="fas fa-save"></i> Lưu cấu hình
                </button>
            </div>
        </form>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
    // Hiển thị xem trước hình ảnh khi nhập URL
    document.addEventListener('DOMContentLoaded', function() {
        const imageInputs = ['logo_url', 'logo_small_url', 'favicon_url', 'og_image', 'art_page2_url',
            'slogan_desktop_url', 'slogan_mobile_url'
        ];

        imageInputs.forEach(function(inputId) {
            const input = document.getElementById(inputId);

            input.addEventListener('change', function() {
                const previewContainer = input.parentElement.querySelector('div');

                if (!previewContainer) {
                    const newPreviewContainer = document.createElement('div');
                    newPreviewContainer.className = 'mt-2';

                    const previewImage = document.createElement('img');
                    previewImage.src = input.value;
                    previewImage.alt = inputId + ' Preview';
                    previewImage.className = (inputId === 'art_page2_url' || inputId ===
                            'slogan_desktop_url' || inputId === 'slogan_mobile_url') ?
                        'preview-image-large' : 'preview-image';

                    newPreviewContainer.appendChild(previewImage);
                    input.parentElement.appendChild(newPreviewContainer);
                } else {
                    const previewImage = previewContainer.querySelector('img');
                    if (previewImage) {
                        previewImage.src = input.value;
                    }
                }
            });
        });
    });
    </script>
</body>

</html>