<?php
// B<PERSON>t hiển thị lỗi để debug
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Thông tin kết nối cơ sở dữ liệu
require_once 'Controllers/database.php';

echo "<h1>Kiểm tra cấu trúc bảng user_balance và team_user</h1>";

try {
    // Kết nối đến cơ sở dữ liệu
    $conn = connectPDO($config1);
    
    echo "<p>Kết nối thành công đến cơ sở dữ liệu</p>";
    
    // Ki<PERSON><PERSON> tra bảng team_user
    echo "<h2>Bảng team_user</h2>";
    $result = $conn->query("SHOW CREATE TABLE team_user");
    $row = $result->fetch(PDO::FETCH_ASSOC);
    echo "<pre>" . $row['Create Table'] . "</pre>";
    
    // Lấy dữ liệu từ bảng team_user
    $result = $conn->query("SELECT * FROM team_user LIMIT 10");
    $users = $result->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Dữ liệu trong bảng team_user:</h3>";
    if (count($users) > 0) {
        echo "<table border='1'>";
        echo "<tr>";
        foreach (array_keys($users[0]) as $column) {
            echo "<th>$column</th>";
        }
        echo "</tr>";
        
        foreach ($users as $user) {
            echo "<tr>";
            foreach ($user as $value) {
                echo "<td>" . htmlspecialchars($value) . "</td>";
            }
            echo "</tr>";
        }
        
        echo "</table>";
    } else {
        echo "<p>Không có dữ liệu trong bảng team_user</p>";
    }
    
    // Kiểm tra bảng user_balance
    echo "<h2>Bảng user_balance</h2>";
    $result = $conn->query("SHOW CREATE TABLE user_balance");
    $row = $result->fetch(PDO::FETCH_ASSOC);
    echo "<pre>" . $row['Create Table'] . "</pre>";
    
    // Lấy dữ liệu từ bảng user_balance
    $result = $conn->query("SELECT * FROM user_balance LIMIT 10");
    $balances = $result->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Dữ liệu trong bảng user_balance:</h3>";
    if (count($balances) > 0) {
        echo "<table border='1'>";
        echo "<tr>";
        foreach (array_keys($balances[0]) as $column) {
            echo "<th>$column</th>";
        }
        echo "</tr>";
        
        foreach ($balances as $balance) {
            echo "<tr>";
            foreach ($balance as $value) {
                echo "<td>" . htmlspecialchars($value) . "</td>";
            }
            echo "</tr>";
        }
        
        echo "</table>";
    } else {
        echo "<p>Không có dữ liệu trong bảng user_balance</p>";
    }
    
    // Kiểm tra bảng bank_transactions
    echo "<h2>Bảng bank_transactions</h2>";
    $result = $conn->query("SHOW CREATE TABLE bank_transactions");
    $row = $result->fetch(PDO::FETCH_ASSOC);
    echo "<pre>" . $row['Create Table'] . "</pre>";
    
    // Lấy dữ liệu từ bảng bank_transactions
    $result = $conn->query("SELECT * FROM bank_transactions LIMIT 10");
    $transactions = $result->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Dữ liệu trong bảng bank_transactions:</h3>";
    if (count($transactions) > 0) {
        echo "<table border='1'>";
        echo "<tr>";
        foreach (array_keys($transactions[0]) as $column) {
            echo "<th>$column</th>";
        }
        echo "</tr>";
        
        foreach ($transactions as $transaction) {
            echo "<tr>";
            foreach ($transaction as $value) {
                echo "<td>" . htmlspecialchars($value) . "</td>";
            }
            echo "</tr>";
        }
        
        echo "</table>";
    } else {
        echo "<p>Không có dữ liệu trong bảng bank_transactions</p>";
    }
    
} catch (PDOException $e) {
    echo "<h2 style='color:red'>Lỗi cơ sở dữ liệu</h2>";
    echo "<p>Lỗi: " . $e->getMessage() . "</p>";
}
?>
