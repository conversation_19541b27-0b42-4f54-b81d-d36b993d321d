// Script để sửa lỗi modal trên thiết bị di động
document.addEventListener('DOMContentLoaded', function() {
    // Kiểm tra xem có phải thiết bị di động không
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

    if (isMobile) {
        console.log('Mobile device detected, applying mobile fixes');

        // Thay thế modal bằng confirm dialog trên thiết bị di động
        const approveButtons = document.querySelectorAll('.btn-sm.btn-success[data-bs-toggle="modal"]');
        const rejectButtons = document.querySelectorAll('.btn-sm.btn-danger[data-bs-toggle="modal"]');
        const detailButtons = document.querySelectorAll('.btn-sm.btn-info[data-bs-toggle="modal"]');

        console.log('Found buttons:', approveButtons.length, 'approve,', rejectButtons.length, 'reject,', detailButtons.length, 'detail');

        // Xử lý nút duyệt
        approveButtons.forEach(function(button) {
            // Xóa sự kiện mặc định
            button.removeAttribute('data-bs-toggle');
            button.removeAttribute('data-bs-target');

            button.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                const transactionId = this.getAttribute('data-transaction-id');
                const username = this.getAttribute('data-username');
                const amount = this.getAttribute('data-amount');

                if (confirm('Bạn có chắc chắn muốn duyệt giao dịch của ' + username + ' với số tiền ' + amount + '?')) {
                    // Tạo form và submit
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = '';

                    const input = document.createElement('input');
                    input.type = 'hidden';
                    input.name = 'transaction_id';
                    input.value = transactionId;

                    const submitButton = document.createElement('input');
                    submitButton.type = 'hidden';
                    submitButton.name = 'approve_transaction';
                    submitButton.value = '1';

                    form.appendChild(input);
                    form.appendChild(submitButton);
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        });

        // Xử lý nút từ chối
        rejectButtons.forEach(function(button) {
            // Xóa sự kiện mặc định
            button.removeAttribute('data-bs-toggle');
            button.removeAttribute('data-bs-target');

            button.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                const transactionId = this.getAttribute('data-transaction-id');
                const username = this.getAttribute('data-username');

                const reason = prompt('Nhập lý do từ chối giao dịch của ' + username + ':');
                if (reason !== null && reason.trim() !== '') {
                    // Tạo form và submit
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = '';

                    const inputId = document.createElement('input');
                    inputId.type = 'hidden';
                    inputId.name = 'transaction_id';
                    inputId.value = transactionId;

                    const inputReason = document.createElement('input');
                    inputReason.type = 'hidden';
                    inputReason.name = 'reject_reason';
                    inputReason.value = reason;

                    const submitButton = document.createElement('input');
                    submitButton.type = 'hidden';
                    submitButton.name = 'reject_transaction';
                    submitButton.value = '1';

                    form.appendChild(inputId);
                    form.appendChild(inputReason);
                    form.appendChild(submitButton);
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        });

        // Xử lý nút chi tiết
        detailButtons.forEach(function(button) {
            // Xóa sự kiện mặc định
            button.removeAttribute('data-bs-toggle');
            button.removeAttribute('data-bs-target');

            button.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                const transactionId = this.getAttribute('data-transaction-id');
                const username = this.getAttribute('data-username');
                const amount = this.getAttribute('data-amount');
                const xuAmount = this.getAttribute('data-xu');
                const transactionCode = this.getAttribute('data-code');
                const status = this.getAttribute('data-status');
                const date = this.getAttribute('data-date');

                alert(
                    'Chi tiết giao dịch:\n\n' +
                    'ID: ' + transactionId + '\n' +
                    'Người dùng: ' + username + '\n' +
                    'Số tiền: ' + amount + '\n' +
                    'Xu: ' + xuAmount + '\n' +
                    'Mã giao dịch: ' + transactionCode + '\n' +
                    'Trạng thái: ' + status + '\n' +
                    'Ngày giao dịch: ' + date
                );
            });
        });

        // Vô hiệu hóa tất cả các modal
        const modals = document.querySelectorAll('.modal');
        modals.forEach(function(modal) {
            // Thêm class để đánh dấu modal đã bị vô hiệu hóa
            modal.classList.add('mobile-disabled');
        });

        // Thêm CSS để ẩn modal trên thiết bị di động
        const style = document.createElement('style');
        style.textContent = `
            @media (max-width: 768px) {
                .mobile-disabled {
                    display: none !important;
                }
            }
        `;
        document.head.appendChild(style);

        console.log('Mobile fixes applied successfully');
    }
});
