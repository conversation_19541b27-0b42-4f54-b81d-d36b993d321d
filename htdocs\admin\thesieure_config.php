<?php
// <PERSON><PERSON><PERSON> hiể<PERSON> thị lỗi để debug
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Include database configuration
require_once '../Controllers/database.php';

// <PERSON><PERSON><PERSON> tra đăng nhập admin
session_start();
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

// Kết nối đến database
$conn = connectPDO($config1);

// Kiểm tra bảng card_api_config có tồn tại không
$tableCheck = $conn->query("SHOW TABLES LIKE 'card_api_config'");
if ($tableCheck->rowCount() == 0) {
    // Tạo bảng card_api_config nếu chưa tồn tại
    $sql = "CREATE TABLE IF NOT EXISTS card_api_config (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        api_name VARCHAR(50) NOT NULL,
        api_url VARCHAR(255) NOT NULL,
        merchant_id VARCHAR(100),
        api_key VARCHAR(255),
        secret_key VARCHAR(255),
        callback_url VARCHAR(255),
        request_method VARCHAR(10) DEFAULT 'POST',
        is_active TINYINT(1) NOT NULL DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    $conn->exec($sql);
}

// Lấy cấu hình API TheSieuRe.com
$stmt = $conn->prepare("SELECT * FROM card_api_config WHERE api_name = 'THESIEURE' LIMIT 1");
$stmt->execute();
$apiConfig = $stmt->fetch(PDO::FETCH_ASSOC);

// Xử lý form cập nhật cấu hình
$message = '';
$success = false;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $apiName = 'THESIEURE';
    $apiUrl = $_POST['api_url'];
    $merchantId = $_POST['merchant_id'];
    $secretKey = $_POST['secret_key'];
    $callbackUrl = $_POST['callback_url'];
    $requestMethod = $_POST['request_method'];
    $isActive = isset($_POST['is_active']) ? 1 : 0;

    try {
        if ($apiConfig) {
            // Cập nhật cấu hình hiện có
            $stmt = $conn->prepare("UPDATE card_api_config SET 
                api_url = :api_url,
                merchant_id = :merchant_id,
                secret_key = :secret_key,
                callback_url = :callback_url,
                request_method = :request_method,
                is_active = :is_active
                WHERE api_name = :api_name");

            $stmt->bindParam(':api_url', $apiUrl, PDO::PARAM_STR);
            $stmt->bindParam(':merchant_id', $merchantId, PDO::PARAM_STR);
            $stmt->bindParam(':secret_key', $secretKey, PDO::PARAM_STR);
            $stmt->bindParam(':callback_url', $callbackUrl, PDO::PARAM_STR);
            $stmt->bindParam(':request_method', $requestMethod, PDO::PARAM_STR);
            $stmt->bindParam(':is_active', $isActive, PDO::PARAM_INT);
            $stmt->bindParam(':api_name', $apiName, PDO::PARAM_STR);

            $stmt->execute();
            $message = 'Cập nhật cấu hình API TheSieuRe.com thành công!';
            $success = true;
        } else {
            // Thêm cấu hình mới
            $stmt = $conn->prepare("INSERT INTO card_api_config (
                api_name, api_url, merchant_id, secret_key, callback_url, request_method, is_active
            ) VALUES (
                :api_name, :api_url, :merchant_id, :secret_key, :callback_url, :request_method, :is_active
            )");

            $stmt->bindParam(':api_name', $apiName, PDO::PARAM_STR);
            $stmt->bindParam(':api_url', $apiUrl, PDO::PARAM_STR);
            $stmt->bindParam(':merchant_id', $merchantId, PDO::PARAM_STR);
            $stmt->bindParam(':secret_key', $secretKey, PDO::PARAM_STR);
            $stmt->bindParam(':callback_url', $callbackUrl, PDO::PARAM_STR);
            $stmt->bindParam(':request_method', $requestMethod, PDO::PARAM_STR);
            $stmt->bindParam(':is_active', $isActive, PDO::PARAM_INT);

            $stmt->execute();
            $message = 'Thêm cấu hình API TheSieuRe.com thành công!';
            $success = true;
        }

        // Lấy lại cấu hình sau khi cập nhật
        $stmt = $conn->prepare("SELECT * FROM card_api_config WHERE api_name = 'THESIEURE' LIMIT 1");
        $stmt->execute();
        $apiConfig = $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        $message = 'Lỗi: ' . $e->getMessage();
        $success = false;
    }
}

// Lấy danh sách tỷ lệ quy đổi xu
$stmt = $conn->query("SELECT * FROM xu_rates ORDER BY amount ASC");
$xuRates = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Xử lý form cập nhật tỷ lệ quy đổi xu
if (isset($_POST['update_xu_rates'])) {
    try {
        // Xóa tất cả tỷ lệ hiện có
        $conn->exec("TRUNCATE TABLE xu_rates");

        // Thêm tỷ lệ mới
        $amounts = $_POST['amount'];
        $xus = $_POST['xu'];

        for ($i = 0; $i < count($amounts); $i++) {
            if (!empty($amounts[$i]) && !empty($xus[$i])) {
                $stmt = $conn->prepare("INSERT INTO xu_rates (amount, xu) VALUES (:amount, :xu)");
                $stmt->bindParam(':amount', $amounts[$i], PDO::PARAM_INT);
                $stmt->bindParam(':xu', $xus[$i], PDO::PARAM_INT);
                $stmt->execute();
            }
        }

        $message = 'Cập nhật tỷ lệ quy đổi xu thành công!';
        $success = true;

        // Lấy lại danh sách tỷ lệ quy đổi xu
        $stmt = $conn->query("SELECT * FROM xu_rates ORDER BY amount ASC");
        $xuRates = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        $message = 'Lỗi: ' . $e->getMessage();
        $success = false;
    }
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cấu hình API TheSieuRe.com - KPAH</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
            padding-top: 20px;
        }

        .card {
            margin-bottom: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .card-header {
            background-color: #343a40;
            color: white;
            border-radius: 10px 10px 0 0 !important;
        }

        .btn-primary {
            background-color: #343a40;
            border-color: #343a40;
        }

        .btn-primary:hover {
            background-color: #23272b;
            border-color: #23272b;
        }

        .btn-danger {
            background-color: #dc3545;
            border-color: #dc3545;
        }

        .btn-danger:hover {
            background-color: #c82333;
            border-color: #bd2130;
        }

        .form-check-input:checked {
            background-color: #343a40;
            border-color: #343a40;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1 class="mb-4">Cấu hình API TheSieuRe.com</h1>

        <?php if (!empty($message)): ?>
            <div class="alert <?php echo $success ? 'alert-success' : 'alert-danger'; ?> alert-dismissible fade show" role="alert">
                <?php echo $message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Cấu hình API</h5>
                    </div>
                    <div class="card-body">
                        <form method="post" action="">
                            <div class="mb-3">
                                <label for="api_url" class="form-label">API URL</label>
                                <input type="text" class="form-control" id="api_url" name="api_url" value="<?php echo $apiConfig ? htmlspecialchars($apiConfig['api_url']) : 'https://thesieure.com/chargingws/v2'; ?>" required>
                                <div class="form-text">URL của API TheSieuRe.com</div>
                            </div>

                            <div class="mb-3">
                                <label for="merchant_id" class="form-label">Partner ID</label>
                                <input type="text" class="form-control" id="merchant_id" name="merchant_id" value="<?php echo $apiConfig ? htmlspecialchars($apiConfig['merchant_id']) : ''; ?>" required>
                                <div class="form-text">ID đối tác của bạn trên TheSieuRe.com</div>
                            </div>

                            <div class="mb-3">
                                <label for="secret_key" class="form-label">Partner Key</label>
                                <input type="text" class="form-control" id="secret_key" name="secret_key" value="<?php echo $apiConfig ? htmlspecialchars($apiConfig['secret_key']) : ''; ?>" required>
                                <div class="form-text">Khóa bí mật của bạn trên TheSieuRe.com</div>
                            </div>

                            <div class="mb-3">
                                <label for="callback_url" class="form-label">Callback URL</label>
                                <input type="text" class="form-control" id="callback_url" name="callback_url" value="<?php echo $apiConfig ? htmlspecialchars($apiConfig['callback_url']) : 'http://yourdomain.com/api/card_callback.php'; ?>" required>
                                <div class="form-text">URL callback để nhận kết quả từ TheSieuRe.com</div>
                            </div>

                            <div class="mb-3">
                                <label for="request_method" class="form-label">Phương thức request</label>
                                <select class="form-select" id="request_method" name="request_method" required>
                                    <option value="POST" <?php echo ($apiConfig && $apiConfig['request_method'] == 'POST') ? 'selected' : ''; ?>>POST</option>
                                    <option value="GET" <?php echo ($apiConfig && $apiConfig['request_method'] == 'GET') ? 'selected' : ''; ?>>GET</option>
                                </select>
                                <div class="form-text">Phương thức gửi request đến API</div>
                            </div>

                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="is_active" name="is_active" <?php echo (!$apiConfig || $apiConfig['is_active']) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="is_active">Kích hoạt API</label>
                            </div>

                            <button type="submit" class="btn btn-primary">Lưu cấu hình</button>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Tỷ lệ quy đổi xu</h5>
                    </div>
                    <div class="card-body">
                        <form method="post" action="" id="xuRatesForm">
                            <div class="table-responsive">
                                <table class="table table-bordered" id="xuRatesTable">
                                    <thead>
                                        <tr>
                                            <th>Mệnh giá (VNĐ)</th>
                                            <th>Xu</th>
                                            <th>Thao tác</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php if (empty($xuRates)): ?>
                                            <tr>
                                                <td><input type="number" class="form-control" name="amount[]" required></td>
                                                <td><input type="number" class="form-control" name="xu[]" required></td>
                                                <td>
                                                    <button type="button" class="btn btn-sm btn-danger remove-row" disabled>
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                        <?php else: ?>
                                            <?php foreach ($xuRates as $index => $rate): ?>
                                                <tr>
                                                    <td><input type="number" class="form-control" name="amount[]" value="<?php echo $rate['amount']; ?>" required></td>
                                                    <td><input type="number" class="form-control" name="xu[]" value="<?php echo $rate['xu']; ?>" required></td>
                                                    <td>
                                                        <button type="button" class="btn btn-sm btn-danger remove-row" <?php echo count($xuRates) <= 1 ? 'disabled' : ''; ?>>
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        <?php endif; ?>
                                    </tbody>
                                </table>
                            </div>

                            <div class="mb-3">
                                <button type="button" class="btn btn-sm btn-success" id="addRow">
                                    <i class="fas fa-plus"></i> Thêm dòng
                                </button>
                            </div>

                            <button type="submit" name="update_xu_rates" class="btn btn-primary">Cập nhật tỷ lệ</button>
                        </form>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Hướng dẫn</h5>
                    </div>
                    <div class="card-body">
                        <h6>Cấu hình API TheSieuRe.com</h6>
                        <ol>
                            <li>Đăng nhập vào tài khoản TheSieuRe.com của bạn</li>
                            <li>Lấy thông tin Partner ID và Partner Key từ trang cấu hình API</li>
                            <li>Nhập các thông tin vào form bên trái</li>
                            <li>Đảm bảo Callback URL có thể truy cập được từ internet</li>
                            <li>Nhấp vào nút "Lưu cấu hình" để lưu cấu hình</li>
                        </ol>

                        <h6>Tỷ lệ quy đổi xu</h6>
                        <ol>
                            <li>Thiết lập tỷ lệ quy đổi từ VNĐ sang xu cho từng mệnh giá</li>
                            <li>Ví dụ: 10,000 VNĐ = 10 xu, 20,000 VNĐ = 20 xu, v.v.</li>
                            <li>Nhấp vào nút "Cập nhật tỷ lệ" để lưu tỷ lệ quy đổi</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Thêm dòng mới vào bảng tỷ lệ quy đổi xu
            document.getElementById('addRow').addEventListener('click', function() {
                const tbody = document.querySelector('#xuRatesTable tbody');
                const newRow = document.createElement('tr');
                newRow.innerHTML = `
                    <td><input type="number" class="form-control" name="amount[]" required></td>
                    <td><input type="number" class="form-control" name="xu[]" required></td>
                    <td>
                        <button type="button" class="btn btn-sm btn-danger remove-row">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                `;
                tbody.appendChild(newRow);

                // Kích hoạt tất cả các nút xóa
                document.querySelectorAll('.remove-row').forEach(button => {
                    button.disabled = false;
                });
            });

            // Xóa dòng khỏi bảng tỷ lệ quy đổi xu
            document.querySelector('#xuRatesTable').addEventListener('click', function(e) {
                if (e.target.classList.contains('remove-row') || e.target.closest('.remove-row')) {
                    const button = e.target.classList.contains('remove-row') ? e.target : e.target.closest('.remove-row');
                    const row = button.closest('tr');
                    const tbody = row.parentNode;

                    // Chỉ xóa nếu có nhiều hơn 1 dòng
                    if (tbody.children.length > 1) {
                        tbody.removeChild(row);

                        // Nếu chỉ còn 1 dòng, vô hiệu hóa nút xóa
                        if (tbody.children.length === 1) {
                            tbody.querySelector('.remove-row').disabled = true;
                        }
                    }
                }
            });
        });
    </script>
</body>

</html>