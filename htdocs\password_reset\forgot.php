<?php
/**
 * <PERSON><PERSON> forgot password requests
 */

// Start session
session_start();

// Include required files
require_once __DIR__ . '/config.php';
require_once __DIR__ . '/database.php';
require_once __DIR__ . '/mailer.php';
require_once __DIR__ . '/utils.php';

// Check if form is submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get email from form
    $email = isset($_POST['email']) ? sanitizeInput($_POST['email']) : '';
    
    // Validate email
    if (empty($email) || !validateEmail($email)) {
        if (isAjaxRequest()) {
            sendJsonResponse(false, 'Vui lòng nhập địa chỉ email hợp lệ.');
        } else {
            redirectWithMessage('../index.php', 'Vui lòng nhập địa chỉ email hợp lệ.');
        }
    }
    
    try {
        // Connect to database
        $conn = connectDB();
        
        // Ensure reset tokens table exists
        ensureResetTokensTable($conn);
        
        // Find user by email
        $user = findUserByEmail($conn, $email);
        
        if (!$user) {
            if (isAjaxRequest()) {
                sendJsonResponse(false, 'Không tìm thấy tài khoản với địa chỉ email này.');
            } else {
                redirectWithMessage('../index.php', 'Không tìm thấy tài khoản với địa chỉ email này.');
            }
        }
        
        // Generate token
        $token = generateToken();
        $userId = $user['id'];
        $username = $user['username'];
        
        // Create reset token
        createResetToken($conn, $userId, $token, $app_config['token_expiry']);
        
        // Send password reset email
        $emailSent = sendPasswordResetEmail($email, $username, $token);
        
        if ($emailSent) {
            if (isAjaxRequest()) {
                sendJsonResponse(true, 'Hướng dẫn đặt lại mật khẩu đã được gửi đến email của bạn.');
            } else {
                redirectWithMessage('../index.php', 'Hướng dẫn đặt lại mật khẩu đã được gửi đến email của bạn.', 'success');
            }
        } else {
            // If email sending fails, show the reset link directly
            include __DIR__ . '/templates/reset_link.php';
            exit;
        }
    } catch (Exception $e) {
        debugLog("Error processing password reset request: " . $e->getMessage());
        
        if (isAjaxRequest()) {
            sendJsonResponse(false, 'Có lỗi xảy ra trong quá trình xử lý yêu cầu. Vui lòng thử lại sau.');
        } else {
            redirectWithMessage('../index.php', 'Có lỗi xảy ra trong quá trình xử lý yêu cầu. Vui lòng thử lại sau.');
        }
    }
} else {
    // Redirect to home page if accessed directly
    header("Location: ../index.php");
    exit;
}
