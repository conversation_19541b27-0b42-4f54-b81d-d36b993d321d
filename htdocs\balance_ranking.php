<?php
// Bật hiển thị lỗi để debug
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Thông tin kết nối cơ sở dữ liệu
require_once 'Controllers/database.php';

// Kiểm tra xem người dùng đã đăng nhập chưa
session_start();
$isLoggedIn = isset($_SESSION['team_user']) && !empty($_SESSION['team_user']);
$username = $isLoggedIn ? $_SESSION['team_user'] : '';
$userId = $isLoggedIn ? $_SESSION['id'] : 0;

// Số người dùng hiển thị trong bảng xếp hạng
$limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 20;
if ($limit <= 0 || $limit > 100) {
    $limit = 20; // Giới hạn mặc định nếu giá trị không hợp lệ
}

try {
    // Kết nối đến cơ sở dữ liệu
    $conn = connectPDO($config1);

    // Lấy thông tin số dư của người dùng hiện tại (nếu đã đăng nhập)
    $currentUserBalance = 0;
    $currentUserRank = 0;

    if ($isLoggedIn) {
        $stmt = $conn->prepare("
            SELECT ub.balance
            FROM user_balance ub
            WHERE ub.user_id = :userId
        ");
        $stmt->bindParam(':userId', $userId, PDO::PARAM_INT);
        $stmt->execute();
        $userBalanceData = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($userBalanceData) {
            $currentUserBalance = $userBalanceData['balance'];
        }

        // Lấy thứ hạng của người dùng hiện tại
        $stmt = $conn->prepare("
            SELECT COUNT(*) + 1 as rank
            FROM user_balance ub
            WHERE ub.balance > (
                SELECT balance FROM user_balance WHERE user_id = :userId
            )
        ");
        $stmt->bindParam(':userId', $userId, PDO::PARAM_INT);
        $stmt->execute();
        $rankData = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($rankData) {
            $currentUserRank = $rankData['rank'];
        }
    }

    // Lấy danh sách xếp hạng số dư
    $stmt = $conn->prepare("
        SELECT
            tu.id,
            tu.username,
            ub.balance,
            ub.updated_at,
            (SELECT COUNT(*) + 1 FROM user_balance ub2 WHERE ub2.balance > ub.balance) as rank
        FROM
            user_balance ub
        JOIN
            team_user tu ON ub.user_id = tu.id
        ORDER BY
            ub.balance DESC
        LIMIT :limit
    ");
    $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
    $stmt->execute();
    $balanceRanking = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Lấy tổng số người dùng có số dư
    $stmt = $conn->query("SELECT COUNT(*) as total FROM user_balance");
    $totalUsers = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

    // Lấy tổng số dư của tất cả người dùng
    $stmt = $conn->query("SELECT SUM(balance) as total FROM user_balance");
    $totalBalance = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
} catch (PDOException $e) {
    $error = "Lỗi cơ sở dữ liệu: " . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bảng Xếp Hạng Số Dư - KPAH Game</title>
    <link rel="icon" href='/favicon.png' type="image/x-icon" />
    <link href="assets/homepage/css/bootstrap.min.css" rel="stylesheet" type="text/css">
    <link rel="stylesheet" href="assets/homepage/css/style.css">
    <link rel="stylesheet" href="assets/css/main.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <script src="assets/homepage/js/bootstrap.bundle.min.js"></script>
    <script src="assets/homepage/js/jquery-1.9.1.min.js"></script>
    <style>
        body {
            background-color: #1a1a1a;
            color: #f9d686;
            font-family: Arial, sans-serif;
            background-image: url('assets/homepage/images/bg-body.jpg');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
        }

        .container {
            max-width: 1200px;
            margin: 30px auto;
            padding: 20px;
        }

        .card {
            background-color: rgba(0, 0, 0, 0.7);
            border: 1px solid #f9d686;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(249, 214, 134, 0.3);
            margin-bottom: 20px;
        }

        .card-header {
            background-color: rgba(249, 214, 134, 0.2);
            color: #f9d686;
            font-weight: bold;
            border-bottom: 1px solid #f9d686;
            padding: 15px;
        }

        .card-body {
            padding: 20px;
        }

        .table {
            color: #f9d686;
            border-color: #f9d686;
        }

        .table th {
            background-color: rgba(249, 214, 134, 0.2);
            color: #f9d686;
            border-color: #f9d686;
        }

        .table td {
            border-color: #f9d686;
        }

        .table-hover tbody tr:hover {
            background-color: rgba(249, 214, 134, 0.1);
        }

        .rank-1 {
            color: gold;
            font-weight: bold;
        }

        .rank-2 {
            color: silver;
            font-weight: bold;
        }

        .rank-3 {
            color: #cd7f32;
            /* Bronze */
            font-weight: bold;
        }

        .current-user {
            background-color: rgba(249, 214, 134, 0.2);
            font-weight: bold;
        }

        .stats-card {
            background-color: rgba(0, 0, 0, 0.7);
            border: 1px solid #f9d686;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: center;
        }

        .stats-card h3 {
            color: #f9d686;
            font-size: 18px;
            margin-bottom: 10px;
        }

        .stats-card .value {
            font-size: 24px;
            font-weight: bold;
            color: white;
        }

        .btn-custom {
            background-color: #f9d686;
            color: #000;
            border: none;
            font-weight: bold;
        }

        .btn-custom:hover {
            background-color: #e5c677;
            color: #000;
        }

        .pagination .page-link {
            background-color: rgba(0, 0, 0, 0.7);
            color: #f9d686;
            border-color: #f9d686;
        }

        .pagination .page-link:hover {
            background-color: rgba(249, 214, 134, 0.2);
            color: #f9d686;
        }

        .pagination .page-item.active .page-link {
            background-color: #f9d686;
            color: #000;
            border-color: #f9d686;
        }

        .back-link {
            margin-bottom: 20px;
        }

        .back-link a {
            color: #f9d686;
            text-decoration: none;
        }

        .back-link a:hover {
            text-decoration: underline;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .stats-row {
                flex-direction: column;
            }

            .stats-card {
                margin-bottom: 10px;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="back-link">
            <a href="/"><i class="fas fa-arrow-left"></i> Quay lại trang chủ</a>
        </div>

        <h1 class="text-center mb-4"><i class="fas fa-trophy"></i> Bảng Xếp Hạng Số Dư</h1>

        <?php if (isset($error)): ?>
            <div class="alert alert-danger">
                <?php echo $error; ?>
            </div>
        <?php else: ?>
            <div class="row stats-row mb-4">
                <div class="col-md-4">
                    <div class="stats-card">
                        <h3><i class="fas fa-users"></i> Tổng số người dùng</h3>
                        <div class="value"><?php echo number_format($totalUsers); ?></div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stats-card">
                        <h3><i class="fas fa-coins"></i> Tổng số dư</h3>
                        <div class="value"><?php echo number_format($totalBalance); ?> xu</div>
                    </div>
                </div>
                <?php if ($isLoggedIn): ?>
                    <div class="col-md-4">
                        <div class="stats-card">
                            <h3><i class="fas fa-user"></i> Thứ hạng của bạn</h3>
                            <div class="value">#<?php echo number_format($currentUserRank); ?></div>
                            <div>Số dư: <?php echo number_format($currentUserBalance); ?> xu</div>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="col-md-4">
                        <div class="stats-card">
                            <h3><i class="fas fa-sign-in-alt"></i> Đăng nhập</h3>
                            <div class="value">Để xem thứ hạng của bạn</div>
                            <a href="#" data-bs-toggle="modal" data-bs-target="#loginModal" class="btn btn-custom mt-2">Đăng nhập</a>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h2 class="mb-0"><i class="fas fa-list"></i> Danh sách xếp hạng</h2>
                    <div class="dropdown">
                        <button class="btn btn-custom dropdown-toggle" type="button" id="dropdownMenuButton" data-bs-toggle="dropdown" aria-expanded="false">
                            Hiển thị: <?php echo $limit; ?>
                        </button>
                        <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                            <li><a class="dropdown-item" href="?limit=10">10 người dùng</a></li>
                            <li><a class="dropdown-item" href="?limit=20">20 người dùng</a></li>
                            <li><a class="dropdown-item" href="?limit=50">50 người dùng</a></li>
                            <li><a class="dropdown-item" href="?limit=100">100 người dùng</a></li>
                        </ul>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Thứ hạng</th>
                                    <th>Tên người dùng</th>
                                    <th>Số dư</th>
                                    <th>Cập nhật lần cuối</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (count($balanceRanking) > 0): ?>
                                    <?php foreach ($balanceRanking as $index => $user): ?>
                                        <tr class="<?php echo ($isLoggedIn && $user['id'] == $userId) ? 'current-user' : ''; ?>">
                                            <td>
                                                <?php if ($user['rank'] == 1): ?>
                                                    <span class="rank-1"><i class="fas fa-crown"></i> 1</span>
                                                <?php elseif ($user['rank'] == 2): ?>
                                                    <span class="rank-2"><i class="fas fa-medal"></i> 2</span>
                                                <?php elseif ($user['rank'] == 3): ?>
                                                    <span class="rank-3"><i class="fas fa-award"></i> 3</span>
                                                <?php else: ?>
                                                    <?php echo $user['rank']; ?>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php echo htmlspecialchars($user['username']); ?>
                                                <?php if ($isLoggedIn && $user['id'] == $userId): ?>
                                                    <span class="badge bg-warning text-dark">Bạn</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo number_format($user['balance']); ?> xu</td>
                                            <td><?php echo date('d/m/Y H:i', strtotime($user['updated_at'])); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="4" class="text-center">Không có dữ liệu</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h3 class="mb-0"><i class="fas fa-info-circle"></i> Thông tin</h3>
                </div>
                <div class="card-body">
                    <p>Bảng xếp hạng số dư hiển thị danh sách người dùng có số dư cao nhất trong hệ thống.</p>
                    <p>Số dư được cập nhật tự động khi có giao dịch nạp tiền hoặc sử dụng dịch vụ.</p>
                    <p>Để tăng thứ hạng của bạn, hãy nạp thêm tiền vào tài khoản.</p>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <script>
        $(document).ready(function() {
            // Highlight current user row
            $('.current-user').addClass('pulse-animation');
        });
    </script>
</body>

</html>