<?php
// Database configuration
$config1 = [
    'host' => 'localhost',
    'username' => 'root',
    'password' => '',
    'database' => 'account'
];

$config2 = [
    'host' => 'localhost',
    'username' => 'root',
    'password' => '',
    'database' => 'game_db'
];

// Use the debugLog function from the including file

// Function to connect to database using mysqli
function connectDB($config)
{
    $conn = new mysqli($config['host'], $config['username'], $config['password'], $config['database']);

    // Check connection
    if ($conn->connect_error) {
        die("Connection failed: " . $conn->connect_error);
    }

    // Set charset to utf8
    $conn->set_charset("utf8");

    return $conn;
}

// Function to connect to database using PDO
function connectPDO($config)
{
    try {
        // Kiểm tra xem cơ sở dữ liệu có tồn tại không
        $pdo = new PDO("mysql:host={$config['host']}", $config['username'], $config['password'], [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
        ]);

        // Kiểm tra xem cơ sở dữ liệu có tồn tại không
        $stmt = $pdo->query("SHOW DATABASES LIKE '{$config['database']}'");
        if ($stmt->rowCount() == 0) {
            // Tạo cơ sở dữ liệu nếu chưa tồn tại
            $pdo->exec("CREATE DATABASE IF NOT EXISTS {$config['database']} CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            if (function_exists('debugLog')) {
                debugLog("Created database '{$config['database']}'");
            } else {
                error_log("Created database '{$config['database']}'");
            }
        }

        // Kết nối đến cơ sở dữ liệu
        $dsn = "mysql:host={$config['host']};dbname={$config['database']};charset=utf8";
        $options = [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
        ];
        $pdo = new PDO($dsn, $config['username'], $config['password'], $options);
        return $pdo;
    } catch (PDOException $e) {
        if (function_exists('debugLog')) {
            debugLog("Database connection error: " . $e->getMessage());
        } else {
            error_log("Database connection error: " . $e->getMessage());
        }
        throw $e; // Throw lại lỗi để xử lý ở nơi gọi hàm
    }
}
// Function to create necessary tables
function createTables($conn, $conn1)
{
    try {
        // Create team_user table in account database
        $conn->exec("CREATE TABLE IF NOT EXISTS team_user (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) NOT NULL UNIQUE,
            password VARCHAR(255) NOT NULL,
            phone VARCHAR(20) DEFAULT NULL,
            regdate DATETIME DEFAULT CURRENT_TIMESTAMP,
            ban TINYINT(1) DEFAULT 0,
            provider VARCHAR(50) DEFAULT NULL,
            fromgame VARCHAR(50) DEFAULT NULL,
            email VARCHAR(100) DEFAULT NULL,
            is_admin TINYINT(1) DEFAULT 0
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

        // Create user_balance table in account database
        $conn->exec("CREATE TABLE IF NOT EXISTS user_balance (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            balance DECIMAL(10,2) DEFAULT 0.00,
            last_updated DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES team_user(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

        // Create card_transactions table in account database
        $conn->exec("CREATE TABLE IF NOT EXISTS card_transactions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            card_number VARCHAR(50) NOT NULL,
            card_type VARCHAR(50) NOT NULL,
            amount DECIMAL(10,2) NOT NULL,
            transaction_date DATETIME DEFAULT CURRENT_TIMESTAMP,
            status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
            FOREIGN KEY (user_id) REFERENCES team_user(id) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

        // Create 5h_active table in game_db database
        $conn1->exec("CREATE TABLE IF NOT EXISTS 5h_active (
            id INT AUTO_INCREMENT PRIMARY KEY,
            userID INT NOT NULL,
            username VARCHAR(50) NOT NULL,
            time_end INT DEFAULT 0
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

        // Create tob_char table in game_db database
        $conn1->exec("CREATE TABLE IF NOT EXISTS tob_char (
            id INT AUTO_INCREMENT PRIMARY KEY,
            charname VARCHAR(50) NOT NULL,
            exp INT DEFAULT 0,
            lastLv INT DEFAULT 0
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

        // Create giftcode table in game_db database
        $conn1->exec("CREATE TABLE IF NOT EXISTS giftcode (
            id INT AUTO_INCREMENT PRIMARY KEY,
            giftcode VARCHAR(50) NOT NULL UNIQUE,
            xu INT DEFAULT 0,
            luong INT DEFAULT 0,
            luongLock INT DEFAULT 0,
            item VARCHAR(255) DEFAULT NULL,
            expire DATETIME DEFAULT '0000-00-00 00:00:00',
            limit_use INT DEFAULT 1,
            type VARCHAR(20) DEFAULT 'normal'
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

        // Create giftcode_used table in game_db database
        $conn1->exec("CREATE TABLE IF NOT EXISTS giftcode_used (
            id INT AUTO_INCREMENT PRIMARY KEY,
            giftcode_id INT NOT NULL,
            user_id INT NOT NULL,
            username VARCHAR(50) NOT NULL,
            used_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY (giftcode_id, user_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

        if (function_exists('debugLog')) {
            debugLog("All tables created successfully");
        } else {
            error_log("All tables created successfully");
        }
        return true;
    } catch (PDOException $e) {
        if (function_exists('debugLog')) {
            debugLog("Error creating tables: " . $e->getMessage());
        } else {
            error_log("Error creating tables: " . $e->getMessage());
        }
        return false;
    }
}
