<?php
// Start the session at the beginning of the file
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['team_user']) || empty($_SESSION['team_user']) || !isset($_SESSION['is_admin']) || !$_SESSION['is_admin']) {
    header("Location: ../index.php?error=" . urlencode('Bạn không có quyền truy cập trang này.'));
    exit();
}

// Include database configuration
require_once '../Controllers/database.php';

// Connect to databases
try {
    // Connect to account database
    $conn = connectPDO($config1);

    // Connect to game_db database
    $conn1 = connectPDO($config2);
} catch (PDOException $e) {
    die("Lỗi kết nối cơ sở dữ liệu: " . $e->getMessage());
}

// Get user information
$userId = $_SESSION['id'];
$username = $_SESSION['team_user'];

// Initialize variables
$message = '';
$messageType = '';
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$perPage = 10;
$offset = ($page - 1) * $perPage;
$searchTerm = isset($_GET['search']) ? trim($_GET['search']) : '';
$filter = isset($_GET['filter']) ? trim($_GET['filter']) : 'pending';

// Handle transaction actions
if (isset($_GET['action'])) {
    $action = $_GET['action'];
    $transactionId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

    // View transaction details
    if ($action === 'view' && $transactionId > 0) {
        try {
            // Get transaction details
            $stmt = $conn->prepare("SELECT ct.*, tu.username FROM card_transactions ct
                                   JOIN team_user tu ON ct.user_id = tu.id
                                   WHERE ct.id = :id");
            $stmt->bindParam(':id', $transactionId, PDO::PARAM_INT);
            $stmt->execute();
            $transactionDetails = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$transactionDetails) {
                $message = 'Không tìm thấy giao dịch.';
                $messageType = 'danger';
            }
        } catch (PDOException $e) {
            $message = 'Lỗi khi lấy thông tin giao dịch: ' . $e->getMessage();
            $messageType = 'danger';
        }
    }

    // Approve transaction
    if ($action === 'approve' && $transactionId > 0) {
        try {
            // Begin transaction
            $conn->beginTransaction();

            // Get transaction details
            $stmt = $conn->prepare("SELECT * FROM card_transactions WHERE id = :id");
            $stmt->bindParam(':id', $transactionId, PDO::PARAM_INT);
            $stmt->execute();
            $transaction = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$transaction) {
                throw new Exception('Không tìm thấy giao dịch.');
            }

            if ($transaction['status'] !== 'pending') {
                throw new Exception('Giao dịch này đã được xử lý trước đó.');
            }

            // Update transaction status
            $stmt = $conn->prepare("UPDATE card_transactions SET status = 'approved', notes = CONCAT(IFNULL(notes, ''), ' | Duyệt bởi admin: ', :adminUsername, ' vào lúc: ', NOW()) WHERE id = :id");
            $stmt->bindParam(':id', $transactionId, PDO::PARAM_INT);
            $stmt->bindParam(':adminUsername', $username, PDO::PARAM_STR);
            $stmt->execute();

            // Update user balance
            $stmt = $conn->prepare("SELECT * FROM user_balance WHERE user_id = :userId");
            $stmt->bindParam(':userId', $transaction['user_id'], PDO::PARAM_INT);
            $stmt->execute();
            $balance = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($balance) {
                // Update existing balance
                $newBalance = $balance['balance'] + $transaction['amount'];
                $stmt = $conn->prepare("UPDATE user_balance SET balance = :balance, last_updated = NOW() WHERE user_id = :userId");
                $stmt->bindParam(':balance', $newBalance, PDO::PARAM_STR);
                $stmt->bindParam(':userId', $transaction['user_id'], PDO::PARAM_INT);
                $stmt->execute();
            } else {
                // Create new balance record
                $stmt = $conn->prepare("INSERT INTO user_balance (user_id, balance, last_updated) VALUES (:userId, :balance, NOW())");
                $stmt->bindParam(':userId', $transaction['user_id'], PDO::PARAM_INT);
                $stmt->bindParam(':balance', $transaction['amount'], PDO::PARAM_STR);
                $stmt->execute();
            }

            // Commit transaction
            $conn->commit();

            $message = 'Đã duyệt giao dịch thành công.';
            $messageType = 'success';
        } catch (Exception $e) {
            // Rollback transaction on error
            $conn->rollBack();

            $message = 'Lỗi khi duyệt giao dịch: ' . $e->getMessage();
            $messageType = 'danger';
        }
    }

    // Reject transaction
    if ($action === 'reject' && $transactionId > 0) {
        try {
            // Get transaction details
            $stmt = $conn->prepare("SELECT * FROM card_transactions WHERE id = :id");
            $stmt->bindParam(':id', $transactionId, PDO::PARAM_INT);
            $stmt->execute();
            $transaction = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$transaction) {
                throw new Exception('Không tìm thấy giao dịch.');
            }

            if ($transaction['status'] !== 'pending') {
                throw new Exception('Giao dịch này đã được xử lý trước đó.');
            }

            // Update transaction status
            $stmt = $conn->prepare("UPDATE card_transactions SET status = 'rejected', notes = CONCAT(IFNULL(notes, ''), ' | Từ chối bởi admin: ', :adminUsername, ' vào lúc: ', NOW()) WHERE id = :id");
            $stmt->bindParam(':id', $transactionId, PDO::PARAM_INT);
            $stmt->bindParam(':adminUsername', $username, PDO::PARAM_STR);
            $stmt->execute();

            $message = 'Đã từ chối giao dịch thành công.';
            $messageType = 'success';
        } catch (Exception $e) {
            $message = 'Lỗi khi từ chối giao dịch: ' . $e->getMessage();
            $messageType = 'danger';
        }
    }
}

// Get transactions with search, filter and pagination
$transactions = [];
$totalTransactions = 0;

try {
    // Build query based on search term and filter
    $query = "SELECT ct.*, tu.username FROM card_transactions ct JOIN team_user tu ON ct.user_id = tu.id";
    $countQuery = "SELECT COUNT(*) FROM card_transactions ct JOIN team_user tu ON ct.user_id = tu.id";
    $whereConditions = [];
    $params = [];

    // Add filter condition
    if ($filter === 'pending') {
        $whereConditions[] = "ct.status = 'pending'";
    } elseif ($filter === 'approved') {
        $whereConditions[] = "ct.status = 'approved'";
    } elseif ($filter === 'rejected') {
        $whereConditions[] = "ct.status = 'rejected'";
    }

    // Add search condition
    if (!empty($searchTerm)) {
        $whereConditions[] = "(tu.username LIKE :search OR ct.card_number LIKE :search OR ct.card_type LIKE :search)";
        $params[':search'] = "%$searchTerm%";
    }

    // Combine where conditions
    if (!empty($whereConditions)) {
        $query .= " WHERE " . implode(" AND ", $whereConditions);
        $countQuery .= " WHERE " . implode(" AND ", $whereConditions);
    }

    $query .= " ORDER BY ct.transaction_date DESC LIMIT :offset, :perPage";

    // Get total count for pagination
    $stmt = $conn->prepare($countQuery);
    foreach ($params as $key => $value) {
        $stmt->bindValue($key, $value, PDO::PARAM_STR);
    }
    $stmt->execute();
    $totalTransactions = $stmt->fetchColumn();

    // Get transactions for current page
    $stmt = $conn->prepare($query);
    foreach ($params as $key => $value) {
        $stmt->bindValue($key, $value, PDO::PARAM_STR);
    }
    $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
    $stmt->bindParam(':perPage', $perPage, PDO::PARAM_INT);
    $stmt->execute();
    $transactions = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $message = 'Lỗi khi lấy danh sách giao dịch: ' . $e->getMessage();
    $messageType = 'danger';
}

// Calculate pagination
$totalPages = ceil($totalTransactions / $perPage);
?>

<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nạp thẻ thủ công - KPAH</title>
    <link href="../assets/homepage/css/bootstrap.min.css" rel="stylesheet" type="text/css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <script src="../assets/homepage/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/homepage/js/jquery-1.9.1.min.js"></script>
    <style>
        body {
            background-color: #1a1a1a;
            color: #f9d686;
            font-family: Arial, sans-serif;
        }

        .admin-container {
            padding: 20px;
        }

        .admin-header {
            background-color: #000;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border: 1px solid #f9d686;
        }

        .admin-title {
            margin: 0;
            color: #f9d686;
            font-weight: bold;
        }

        .admin-user {
            color: #fff;
        }

        .admin-card {
            background-color: #2a2a2a;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #f9d686;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .admin-card h2 {
            color: #f9d686;
            font-size: 1.5rem;
            margin-bottom: 15px;
            border-bottom: 1px solid #f9d686;
            padding-bottom: 10px;
        }

        .admin-menu {
            background-color: #2a2a2a;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid #f9d686;
        }

        .admin-menu ul {
            list-style: none;
            padding: 0;
            margin: 0;
            display: flex;
            flex-wrap: wrap;
        }

        .admin-menu li {
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .admin-menu a {
            display: block;
            padding: 8px 15px;
            background-color: #333;
            color: #f9d686;
            text-decoration: none;
            border-radius: 3px;
            border: 1px solid rgba(249, 214, 134, 0.3);
            transition: all 0.3s ease;
        }

        .admin-menu a:hover,
        .admin-menu a.active {
            background-color: #f9d686;
            color: #000;
        }

        .admin-menu a i {
            margin-right: 5px;
        }

        .form-control {
            background-color: #333;
            border: 1px solid #f9d686;
            color: #fff;
        }

        .form-control:focus {
            background-color: #444;
            color: #fff;
            border-color: #f9d686;
            box-shadow: 0 0 0 0.2rem rgba(249, 214, 134, 0.25);
        }

        .form-label {
            color: #f9d686;
            font-weight: bold;
        }

        .btn-admin {
            background-color: #f9d686;
            color: #000;
            border: none;
            font-weight: bold;
            padding: 8px 16px;
            margin-right: 5px;
            margin-bottom: 5px;
        }

        .btn-admin:hover {
            background-color: #e5c677;
            color: #000;
        }

        .user-info {
            background-color: #333;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .user-info h3 {
            color: #f9d686;
            font-size: 1.2rem;
            margin-bottom: 10px;
        }

        .info-item {
            margin-bottom: 10px;
        }

        .info-label {
            font-weight: bold;
            color: #f9d686;
            width: 100px;
            display: inline-block;
        }

        @media (max-width: 768px) {
            .admin-header {
                flex-direction: column;
                text-align: center;
            }

            .admin-user {
                margin-top: 10px;
            }

            .admin-menu ul {
                flex-direction: column;
            }

            .admin-menu li {
                margin-right: 0;
                margin-bottom: 5px;
            }

            .btn-admin {
                display: block;
                width: 100%;
                margin-bottom: 5px;
            }
        }
    </style>
</head>

<body>
    <div class="container admin-container">
        <div class="admin-header">
            <h1 class="admin-title">KPAH - Nạp thẻ thủ công</h1>
            <div class="admin-user">
                Xin chào, <strong><?php echo htmlspecialchars($username); ?></strong> |
                <a href="../Views/Account.php" class="text-light">Tài khoản</a> |
                <a href="../handle/logout.php" class="text-light">Đăng xuất</a>
            </div>
        </div>

        <div class="admin-menu">
            <ul>
                <li><a href="index.php"><i class="fas fa-tachometer-alt"></i> Tổng quan</a></li>
                <li><a href="users.php"><i class="fas fa-users"></i> Quản lý người dùng</a></li>
                <li><a href="bank_topups.php"><i class="fas fa-university"></i> Quản lý nạp ATM</a></li>
                <li><a href="bank_accounts.php"><i class="fas fa-credit-card"></i> Quản lý tài khoản ngân hàng</a></li>
                <li><a href="activations.php"><i class="fas fa-check-circle"></i> Kích hoạt tài khoản</a></li>
                <li><a href="giftcodes.php"><i class="fas fa-gift"></i> Quản lý Giftcode</a></li>
                <li><a href="api_management.php"><i class="fas fa-plug"></i> Quản lý API</a></li>
                <li><a href="password_decoder.php"><i class="fas fa-key"></i> Giải mã mật khẩu</a></li>
                <li><a href="site_settings.php"><i class="fas fa-cogs"></i> Cấu hình trang web</a></li>
                <li><a href="manual_topup.php" class="active"><i class="fas fa-plus-circle"></i> Nạp thẻ thủ công</a></li>

                <li><a href="../index.php"><i class="fas fa-home"></i> Về trang chủ</a></li>
            </ul>
        </div>

        <?php if (!empty($message)): ?>
            <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                <?php echo $message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <div class="admin-card">
            <h2><i class="fas fa-plus-circle"></i> Nạp thẻ thủ công</h2>

            <form method="post" action="">
                <div class="mb-3">
                    <label for="user_id" class="form-label">Chọn người dùng</label>
                    <select class="form-control" id="user_id" name="user_id" required>
                        <option value="">-- Chọn người dùng --</option>
                        <?php foreach ($users as $user): ?>
                            <option value="<?php echo $user['id']; ?>"
                                <?php echo ($selectedUserId == $user['id']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($user['username']); ?>
                                <?php if (!empty($user['email'])): ?>
                                    (<?php echo htmlspecialchars($user['email']); ?>)
                                <?php endif; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <?php if ($selectedUser): ?>
                    <div class="user-info">
                        <h3>Thông tin người dùng</h3>
                        <div class="info-item">
                            <span class="info-label">ID:</span>
                            <?php echo $selectedUser['id']; ?>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Tên đăng nhập:</span>
                            <?php echo htmlspecialchars($selectedUser['username']); ?>
                        </div>
                        <?php if (!empty($selectedUser['email'])): ?>
                            <div class="info-item">
                                <span class="info-label">Email:</span>
                                <?php echo htmlspecialchars($selectedUser['email']); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>

                <div class="mb-3">
                    <label for="card_type" class="form-label">Loại thẻ</label>
                    <select class="form-control" id="card_type" name="card_type" required>
                        <option value="">-- Chọn loại thẻ --</option>
                        <option value="viettel">Viettel</option>
                        <option value="mobifone">Mobifone</option>
                        <option value="vinaphone">Vinaphone</option>
                        <option value="gate">Gate</option>
                        <option value="zing">Zing</option>
                        <option value="manual">Nạp thủ công</option>
                    </select>
                </div>

                <div class="mb-3">
                    <label for="card_amount" class="form-label">Mệnh giá</label>
                    <select class="form-control" id="card_amount" name="card_amount" required>
                        <option value="">-- Chọn mệnh giá --</option>
                        <option value="10000">10,000 VNĐ (100 xu)</option>
                        <option value="20000">20,000 VNĐ (220 xu)</option>
                        <option value="50000">50,000 VNĐ (600 xu)</option>
                        <option value="100000">100,000 VNĐ (1,300 xu)</option>
                        <option value="200000">200,000 VNĐ (2,700 xu)</option>
                        <option value="500000">500,000 VNĐ (7,000 xu)</option>
                    </select>
                </div>

                <div class="mb-3">
                    <label for="notes" class="form-label">Ghi chú</label>
                    <textarea class="form-control" id="notes" name="notes" rows="3"
                        placeholder="Nhập ghi chú về giao dịch này (nếu có)"></textarea>
                </div>

                <div class="d-grid gap-2">
                    <button type="submit" name="submit_manual_topup" class="btn btn-admin">
                        <i class="fas fa-plus-circle"></i> Thêm nạp thẻ thủ công
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            // Auto-dismiss alerts after 5 seconds
            setTimeout(function() {
                $('.alert').alert('close');
            }, 5000);

            // Redirect to the same page with user_id parameter when a user is selected
            $('#user_id').change(function() {
                var userId = $(this).val();
                if (userId) {
                    window.location.href = 'manual_topup.php?user_id=' + userId;
                }
            });
        });
    </script>
</body>

</html>