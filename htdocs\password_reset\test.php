<?php
/**
 * Test script for password reset functionality
 */

// Include required files
require_once __DIR__ . '/config.php';
require_once __DIR__ . '/database.php';
require_once __DIR__ . '/mailer.php';
require_once __DIR__ . '/utils.php';

// Function to test database connection
function testDatabaseConnection() {
    try {
        $conn = connectDB();
        echo "<div class='test-result success'>";
        echo "<h3>Database Connection</h3>";
        echo "<p>Successfully connected to database.</p>";
        echo "</div>";
        return $conn;
    } catch (Exception $e) {
        echo "<div class='test-result failure'>";
        echo "<h3>Database Connection</h3>";
        echo "<p>Failed to connect to database: " . $e->getMessage() . "</p>";
        echo "</div>";
        return null;
    }
}

// Function to test reset tokens table
function testResetTokensTable($conn) {
    if (!$conn) return;
    
    try {
        ensureResetTokensTable($conn);
        echo "<div class='test-result success'>";
        echo "<h3>Reset Tokens Table</h3>";
        echo "<p>Reset tokens table exists or was created successfully.</p>";
        echo "</div>";
    } catch (Exception $e) {
        echo "<div class='test-result failure'>";
        echo "<h3>Reset Tokens Table</h3>";
        echo "<p>Failed to create reset tokens table: " . $e->getMessage() . "</p>";
        echo "</div>";
    }
}

// Function to test email sending
function testEmailSending() {
    if (isset($_POST['test_email'])) {
        $email = $_POST['test_email'];
        
        if (!validateEmail($email)) {
            echo "<div class='test-result failure'>";
            echo "<h3>Email Sending</h3>";
            echo "<p>Invalid email address.</p>";
            echo "</div>";
            return;
        }
        
        try {
            $token = generateToken();
            $emailSent = sendPasswordResetEmail($email, 'Test User', $token);
            
            if ($emailSent) {
                echo "<div class='test-result success'>";
                echo "<h3>Email Sending</h3>";
                echo "<p>Test email sent successfully to {$email}.</p>";
                echo "</div>";
            } else {
                echo "<div class='test-result failure'>";
                echo "<h3>Email Sending</h3>";
                echo "<p>Failed to send test email. Check error logs for details.</p>";
                echo "</div>";
            }
        } catch (Exception $e) {
            echo "<div class='test-result failure'>";
            echo "<h3>Email Sending</h3>";
            echo "<p>Error sending email: " . $e->getMessage() . "</p>";
            echo "</div>";
        }
    }
}

// Function to display configuration
function displayConfiguration() {
    global $db_config, $email_config, $app_config;
    
    echo "<div class='test-result info'>";
    echo "<h3>Configuration</h3>";
    
    echo "<h4>Database Configuration</h4>";
    echo "<ul>";
    echo "<li>Host: " . $db_config['host'] . "</li>";
    echo "<li>Database: " . $db_config['database'] . "</li>";
    echo "</ul>";
    
    echo "<h4>Email Configuration</h4>";
    echo "<ul>";
    echo "<li>SMTP Host: " . $email_config['host'] . "</li>";
    echo "<li>SMTP Port: " . $email_config['port'] . "</li>";
    echo "<li>Encryption: " . $email_config['encryption'] . "</li>";
    echo "<li>From Email: " . $email_config['from_email'] . "</li>";
    echo "</ul>";
    
    echo "<h4>Application Configuration</h4>";
    echo "<ul>";
    echo "<li>Site URL: " . $app_config['site_url'] . "</li>";
    echo "<li>Token Expiry: " . $app_config['token_expiry'] . " hours</li>";
    echo "<li>Min Password Length: " . $app_config['min_password_length'] . " characters</li>";
    echo "</ul>";
    
    echo "</div>";
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Reset Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        
        h1, h2, h3, h4 {
            color: #333;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }
        
        .test-result {
            margin-bottom: 20px;
            padding: 15px;
            border-radius: 5px;
        }
        
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .failure {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .info {
            background-color: #e2f0fb;
            border: 1px solid #b8daff;
            color: #004085;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        input[type="email"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        
        .btn {
            display: inline-block;
            padding: 8px 16px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        
        .btn:hover {
            background-color: #0069d9;
        }
        
        .back-link {
            margin-top: 20px;
        }
        
        .back-link a {
            color: #007bff;
            text-decoration: none;
        }
        
        .back-link a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Password Reset Test</h1>
        
        <p>This page tests the password reset functionality.</p>
        
        <?php
        // Display configuration
        displayConfiguration();
        
        // Test database connection
        $conn = testDatabaseConnection();
        
        // Test reset tokens table
        testResetTokensTable($conn);
        
        // Test email sending
        testEmailSending();
        ?>
        
        <h2>Test Email Sending</h2>
        <form method="post" action="">
            <div class="form-group">
                <label for="test_email">Email Address:</label>
                <input type="email" id="test_email" name="test_email" required placeholder="Enter email address to send test email">
            </div>
            <button type="submit" class="btn">Send Test Email</button>
        </form>
        
        <div class="back-link">
            <a href="../index.php">&larr; Back to Home</a>
        </div>
    </div>
</body>
</html>
