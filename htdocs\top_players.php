<?php
// Bật hiển thị lỗi để debug
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Thông tin kết nối cơ sở dữ liệu
require_once 'Controllers/database.php';

// Kiểm tra xem người dùng đã đăng nhập chưa
session_start();
$isLoggedIn = isset($_SESSION['team_user']) && !empty($_SESSION['team_user']);
$username = $isLoggedIn ? $_SESSION['team_user'] : '';
$userId = $isLoggedIn ? $_SESSION['id'] : 0;

// Số người dùng hiển thị trong bảng xếp hạng
$limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 20;
if ($limit <= 0 || $limit > 100) {
    $limit = 20; // Giới hạn mặc định nếu giá trị không hợp lệ
}

// Loại xếp hạng (cấp độ hoặc kinh nghiệm)
$rankType = isset($_GET['type']) && $_GET['type'] === 'xp' ? 'xp' : 'level';

try {
    // Kết nối đến cơ sở dữ liệu game_db
    $conn = connectPDO($config2);

    // Lấy thông tin nhân vật của người dùng hiện tại (nếu đã đăng nhập)
    $currentUserCharacter = null;
    $currentUserRank = 0;

    if ($isLoggedIn) {
        // Lấy thông tin nhân vật từ bảng tob_char dựa trên username
        $stmt = $conn->prepare("
            SELECT id, charname, xp, lastLv
            FROM tob_char
            WHERE charname = :username
        ");
        $stmt->bindParam(':username', $username, PDO::PARAM_STR);
        $stmt->execute();
        $currentUserCharacter = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($currentUserCharacter) {
            // Lấy thứ hạng của người dùng hiện tại dựa trên loại xếp hạng
            if ($rankType === 'xp') {
                $stmt = $conn->prepare("
                    SELECT COUNT(*) + 1 as rank
                    FROM tob_char
                    WHERE xp > (
                        SELECT xp FROM tob_char WHERE charname = :username
                    )
                ");
            } else {
                $stmt = $conn->prepare("
                    SELECT COUNT(*) + 1 as rank
                    FROM tob_char
                    WHERE lastLv > (
                        SELECT lastLv FROM tob_char WHERE charname = :username
                    ) OR (lastLv = (
                        SELECT lastLv FROM tob_char WHERE charname = :username
                    ) AND xp > (
                        SELECT xp FROM tob_char WHERE charname = :username
                    ))
                ");
            }
            $stmt->bindParam(':username', $username, PDO::PARAM_STR);
            $stmt->execute();
            $rankData = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($rankData) {
                $currentUserRank = $rankData['rank'];
            }
        }
    }

    // Lấy danh sách xếp hạng cao thủ
    if ($rankType === 'xp') {
        // Xếp hạng theo kinh nghiệm
        $stmt = $conn->prepare("
            SELECT
                id,
                charname,
                xp,
                lastLv,
                (SELECT COUNT(*) + 1 FROM tob_char tc2 WHERE tc2.xp > tc1.xp) as rank
            FROM
                tob_char tc1
            ORDER BY
                xp DESC
            LIMIT :limit
        ");
    } else {
        // Xếp hạng theo cấp độ, nếu cấp độ bằng nhau thì xếp theo kinh nghiệm
        $stmt = $conn->prepare("
            SELECT
                id,
                charname,
                xp,
                lastLv,
                (SELECT COUNT(*) + 1 FROM tob_char tc2
                 WHERE tc2.lastLv > tc1.lastLv OR (tc2.lastLv = tc1.lastLv AND tc2.xp > tc1.xp)) as rank
            FROM
                tob_char tc1
            ORDER BY
                lastLv DESC, xp DESC
            LIMIT :limit
        ");
    }
    $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
    $stmt->execute();
    $topPlayers = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Lấy tổng số nhân vật
    $stmt = $conn->query("SELECT COUNT(*) as total FROM tob_char");
    $totalPlayers = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

    // Lấy cấp độ cao nhất
    $stmt = $conn->query("SELECT MAX(lastLv) as max_level FROM tob_char");
    $maxLevel = $stmt->fetch(PDO::FETCH_ASSOC)['max_level'];

    // Lấy kinh nghiệm cao nhất
    $stmt = $conn->query("SELECT MAX(xp) as max_xp FROM tob_char");
    $maxxp = $stmt->fetch(PDO::FETCH_ASSOC)['max_xp'];
} catch (PDOException $e) {
    $error = "Lỗi cơ sở dữ liệu: " . $e->getMessage();
}

// Hàm chuyển đổi kinh nghiệm sang định dạng dễ đọc
function formatxp($xp)
{
    if ($xp >= 1000000000000) { // Nghìn tỷ
        return round($xp / 1000000000000, 2) . ' nghìn tỷ';
    } elseif ($xp >= 1000000000) { // Tỷ
        return round($xp / 1000000000, 2) . ' tỷ';
    } elseif ($xp >= 1000000) { // Triệu
        return round($xp / 1000000, 2) . ' triệu';
    } elseif ($xp >= 1000) { // Nghìn
        return round($xp / 1000, 2) . ' nghìn';
    } else {
        return $xp;
    }
}

// Hàm tính phần trăm kinh nghiệm so với người cao nhất
function calculatexpPercentage($xp, $maxxp)
{
    if ($maxxp > 0) {
        return round(($xp / $maxxp) * 100, 2);
    }
    return 0;
}

// Hàm tính màu sắc dựa trên cấp độ
function getLevelColor($level)
{
    if ($level >= 150) {
        return '#ff5722'; // Cam đậm
    } elseif ($level >= 130) {
        return '#ff9800'; // Cam
    } elseif ($level >= 110) {
        return '#ffc107'; // Vàng
    } elseif ($level >= 90) {
        return '#8bc34a'; // Xanh lá
    } elseif ($level >= 70) {
        return '#03a9f4'; // Xanh dương
    } elseif ($level >= 50) {
        return '#3f51b5'; // Indigo
    } else {
        return '#9e9e9e'; // Xám
    }
}

// Hàm lấy biểu tượng cho cấp độ
function getLevelIcon($level)
{
    if ($level >= 150) {
        return '<i class="fas fa-dragon"></i>';
    } elseif ($level >= 130) {
        return '<i class="fas fa-fire"></i>';
    } elseif ($level >= 110) {
        return '<i class="fas fa-bolt"></i>';
    } elseif ($level >= 90) {
        return '<i class="fas fa-star"></i>';
    } elseif ($level >= 70) {
        return '<i class="fas fa-shield-alt"></i>';
    } elseif ($level >= 50) {
        return '<i class="fas fa-sword"></i>';
    } else {
        return '<i class="fas fa-user"></i>';
    }
}
?>

<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bảng Xếp Hạng Cao Thủ - KPAH Game</title>
    <link rel="icon" href='/favicon.png' type="image/x-icon" />
    <link href="assets/homepage/css/bootstrap.min.css" rel="stylesheet" type="text/css">
    <link rel="stylesheet" href="assets/homepage/css/style.css">
    <link rel="stylesheet" href="assets/css/main.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <script src="assets/homepage/js/bootstrap.bundle.min.js"></script>
    <script src="assets/homepage/js/jquery-1.9.1.min.js"></script>
    <style>
        body {
            background-color: #1a1a1a;
            color: #f9d686;
            font-family: Arial, sans-serif;
            background-image: url('assets/homepage/images/bg-body.jpg');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
        }

        .container {
            max-width: 1200px;
            margin: 30px auto;
            padding: 20px;
        }

        .card {
            background-color: rgba(0, 0, 0, 0.7);
            border: 1px solid #f9d686;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(249, 214, 134, 0.3);
            margin-bottom: 20px;
        }

        .card-header {
            background-color: rgba(249, 214, 134, 0.2);
            color: #f9d686;
            font-weight: bold;
            border-bottom: 1px solid #f9d686;
            padding: 15px;
        }

        .card-body {
            padding: 20px;
        }

        .table {
            color: #f9d686;
            border-color: #f9d686;
        }

        .table th {
            background-color: rgba(249, 214, 134, 0.2);
            color: #f9d686;
            border-color: #f9d686;
        }

        .table td {
            border-color: #f9d686;
        }

        .table-hover tbody tr:hover {
            background-color: rgba(249, 214, 134, 0.1);
        }

        .rank-1 {
            color: gold;
            font-weight: bold;
        }

        .rank-2 {
            color: silver;
            font-weight: bold;
        }

        .rank-3 {
            color: #cd7f32;
            /* Bronze */
            font-weight: bold;
        }

        .current-user {
            background-color: rgba(249, 214, 134, 0.2);
            font-weight: bold;
        }

        .stats-card {
            background-color: rgba(0, 0, 0, 0.7);
            border: 1px solid #f9d686;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: center;
        }

        .stats-card h3 {
            color: #f9d686;
            font-size: 18px;
            margin-bottom: 10px;
        }

        .stats-card .value {
            font-size: 24px;
            font-weight: bold;
            color: white;
        }

        .btn-custom {
            background-color: #f9d686;
            color: #000;
            border: none;
            font-weight: bold;
        }

        .btn-custom:hover {
            background-color: #e5c677;
            color: #000;
        }

        .btn-custom.active {
            background-color: #e5c677;
            box-shadow: 0 0 10px rgba(249, 214, 134, 0.5);
        }

        .pagination .page-link {
            background-color: rgba(0, 0, 0, 0.7);
            color: #f9d686;
            border-color: #f9d686;
        }

        .pagination .page-link:hover {
            background-color: rgba(249, 214, 134, 0.2);
            color: #f9d686;
        }

        .pagination .page-item.active .page-link {
            background-color: #f9d686;
            color: #000;
            border-color: #f9d686;
        }

        .back-link {
            margin-bottom: 20px;
        }

        .back-link a {
            color: #f9d686;
            text-decoration: none;
        }

        .back-link a:hover {
            text-decoration: underline;
        }

        .progress {
            height: 10px;
            background-color: rgba(0, 0, 0, 0.5);
            border-radius: 5px;
            overflow: hidden;
            margin-top: 5px;
        }

        .progress-bar {
            background-color: #f9d686;
        }

        .level-badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 10px;
            color: white;
            font-weight: bold;
            margin-right: 5px;
        }

        .character-name {
            display: flex;
            align-items: center;
        }

        .character-icon {
            width: 30px;
            height: 30px;
            margin-right: 10px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .stats-row {
                flex-direction: column;
            }

            .stats-card {
                margin-bottom: 10px;
            }

            .table th,
            .table td {
                padding: 0.5rem;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="back-link">
            <a href="/"><i class="fas fa-arrow-left"></i> Quay lại trang chủ</a>
        </div>

        <h1 class="text-center mb-4"><i class="fas fa-trophy"></i> Bảng Xếp Hạng Cao Thủ</h1>

        <?php if (isset($error)): ?>
            <div class="alert alert-danger">
                <?php echo $error; ?>
            </div>
        <?php else: ?>
            <div class="row stats-row mb-4">
                <div class="col-md-4">
                    <div class="stats-card">
                        <h3><i class="fas fa-users"></i> Tổng số nhân vật</h3>
                        <div class="value"><?php echo number_format($totalPlayers); ?></div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="stats-card">
                        <h3><i class="fas fa-level-up-alt"></i> Cấp độ cao nhất</h3>
                        <div class="value"><?php echo number_format($maxLevel); ?></div>
                    </div>
                </div>
                <?php if ($isLoggedIn && $currentUserCharacter): ?>
                    <div class="col-md-4">
                        <div class="stats-card">
                            <h3><i class="fas fa-user"></i> Thứ hạng của bạn</h3>
                            <div class="value">#<?php echo number_format($currentUserRank); ?></div>
                            <div>Cấp độ: <?php echo number_format($currentUserCharacter['lastLv']); ?></div>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="col-md-4">
                        <div class="stats-card">
                            <h3><i class="fas fa-sign-in-alt"></i> Đăng nhập</h3>
                            <div class="value">Để xem thứ hạng của bạn</div>
                            <a href="#" data-bs-toggle="modal" data-bs-target="#loginModal" class="btn btn-custom mt-2">Đăng
                                nhập</a>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h2 class="mb-0"><i class="fas fa-list"></i> Danh sách xếp hạng</h2>
                    <div class="d-flex">
                        <div class="btn-group me-2">
                            <a href="?type=level&limit=<?php echo $limit; ?>"
                                class="btn btn-custom <?php echo $rankType === 'level' ? 'active' : ''; ?>">
                                <i class="fas fa-level-up-alt"></i> Theo cấp độ
                            </a>
                            <a href="?type=xp&limit=<?php echo $limit; ?>"
                                class="btn btn-custom <?php echo $rankType === 'xp' ? 'active' : ''; ?>">
                                <i class="fas fa-star"></i> Theo kinh nghiệm
                            </a>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-custom dropdown-toggle" type="button" id="dropdownMenuButton"
                                data-bs-toggle="dropdown" aria-xpanded="false">
                                Hiển thị: <?php echo $limit; ?>
                            </button>
                            <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                <li><a class="dropdown-item" href="?type=<?php echo $rankType; ?>&limit=10">10 người
                                        chơi</a></li>
                                <li><a class="dropdown-item" href="?type=<?php echo $rankType; ?>&limit=20">20 người
                                        chơi</a></li>
                                <li><a class="dropdown-item" href="?type=<?php echo $rankType; ?>&limit=50">50 người
                                        chơi</a></li>
                                <li><a class="dropdown-item" href="?type=<?php echo $rankType; ?>&limit=100">100 người
                                        chơi</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Thứ hạng</th>
                                    <th>Tên nhân vật</th>
                                    <th>Cấp độ</th>
                                    <th>Kinh nghiệm</th>
                                    <?php if ($rankType === 'xp'): ?>
                                        <th>% So với Top 1</th>
                                    <?php endif; ?>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (count($topPlayers) > 0): ?>
                                    <?php foreach ($topPlayers as $player): ?>
                                        <tr
                                            class="<?php echo ($isLoggedIn && $player['charname'] === $username) ? 'current-user' : ''; ?>">
                                            <td>
                                                <?php if ($player['rank'] == 1): ?>
                                                    <span class="rank-1"><i class="fas fa-crown"></i> 1</span>
                                                <?php elseif ($player['rank'] == 2): ?>
                                                    <span class="rank-2"><i class="fas fa-medal"></i> 2</span>
                                                <?php elseif ($player['rank'] == 3): ?>
                                                    <span class="rank-3"><i class="fas fa-award"></i> 3</span>
                                                <?php else: ?>
                                                    <?php echo $player['rank']; ?>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="character-name">
                                                    <div class="character-icon"
                                                        style="background-color: <?php echo getLevelColor($player['lastLv']); ?>">
                                                        <?php echo getLevelIcon($player['lastLv']); ?>
                                                    </div>
                                                    <?php echo htmlspecialchars($player['charname']); ?>
                                                    <?php if ($isLoggedIn && $player['charname'] === $username): ?>
                                                        <span class="badge bg-warning text-dark ms-2">Bạn</span>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="level-badge"
                                                    style="background-color: <?php echo getLevelColor($player['lastLv']); ?>">
                                                    <?php echo $player['lastLv']; ?>
                                                </span>
                                            </td>
                                            <td><?php echo formatxp($player['xp']); ?></td>
                                            <?php if ($rankType === 'xp'): ?>
                                                <td>
                                                    <?php
                                                    $percentage = calculatexpPercentage($player['xp'], $maxxp);
                                                    echo $percentage . '%';
                                                    ?>
                                                    <div class="progress">
                                                        <div class="progress-bar" role="progressbar"
                                                            style="width: <?php echo $percentage; ?>%"
                                                            aria-valuenow="<?php echo $percentage; ?>" aria-valuemin="0"
                                                            aria-valuemax="100"></div>
                                                    </div>
                                                </td>
                                            <?php endif; ?>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="<?php echo $rankType === 'xp' ? 5 : 4; ?>" class="text-center">Không có dữ
                                            liệu</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h3 class="mb-0"><i class="fas fa-info-circle"></i> Thông tin</h3>
                </div>
                <div class="card-body">
                    <p>Bảng xếp hạng cao thủ hiển thị danh sách người chơi có cấp độ và kinh nghiệm cao nhất trong game.</p>
                    <p>Bạn có thể chọn xếp hạng theo cấp độ hoặc theo kinh nghiệm.</p>
                    <p>Cấp độ và kinh nghiệm được cập nhật tự động từ dữ liệu game.</p>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <script>
        $(document).ready(function() {
            // Highlight current user row
            $('.current-user').addClass('pulse-animation');
        });
    </script>
</body>

</html>