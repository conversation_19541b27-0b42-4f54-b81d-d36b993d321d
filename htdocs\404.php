<?php
// Set HTTP response code
http_response_code(404);

// Get the requested URL
$requestedUrl = htmlspecialchars($_SERVER['REQUEST_URI']);

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
$isLoggedIn = isset($_SESSION['team_user']) && !empty($_SESSION['team_user']);
$username = $isLoggedIn ? $_SESSION['team_user'] : '';
?>
<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>404 - Không tìm thấy trang - KPAH Game</title>
    <link rel="icon" href='/favicon.png' type="image/x-icon" />
    <link href="assets/homepage/css/bootstrap.min.css" rel="stylesheet" type="text/css">
    <link rel="stylesheet" href="assets/homepage/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <script src="assets/homepage/js/bootstrap.bundle.min.js"></script>
    <script src="assets/homepage/js/jquery-1.9.1.min.js"></script>
    <style>
        body {
            background-color: #1a1a1a;
            color: #f9d686;
            font-family: Arial, sans-serif;
            background-image: url('assets/homepage/images/bg-body.jpg');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
        }

        .error-container {
            max-width: 800px;
            margin: 100px auto;
            padding: 30px;
            background-color: rgba(0, 0, 0, 0.7);
            border: 1px solid #f9d686;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(249, 214, 134, 0.3);
            text-align: center;
        }

        .error-code {
            font-size: 120px;
            font-weight: bold;
            color: #f9d686;
            margin-bottom: 20px;
            text-shadow: 0 0 10px rgba(249, 214, 134, 0.5);
        }

        .error-message {
            font-size: 24px;
            margin-bottom: 30px;
        }

        .error-details {
            margin-bottom: 30px;
            color: #ccc;
        }

        .btn-custom {
            background-color: #f9d686;
            color: #000;
            border: none;
            font-weight: bold;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }

        .btn-custom:hover {
            background-color: #e5c677;
            transform: scale(1.05);
        }

        .error-image {
            max-width: 300px;
            margin: 20px auto;
        }

        .suggestions {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid rgba(249, 214, 134, 0.3);
        }

        .suggestions h3 {
            margin-bottom: 15px;
            color: #f9d686;
        }

        .suggestions ul {
            list-style-type: none;
            padding: 0;
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 10px;
        }

        .suggestions li {
            margin-bottom: 10px;
        }

        .suggestions a {
            color: #f9d686;
            text-decoration: none;
            padding: 5px 10px;
            border: 1px solid #f9d686;
            border-radius: 5px;
            display: inline-block;
            transition: all 0.3s ease;
        }

        .suggestions a:hover {
            background-color: rgba(249, 214, 134, 0.2);
        }

        @media (max-width: 768px) {
            .error-container {
                margin: 50px auto;
                padding: 20px;
            }

            .error-code {
                font-size: 80px;
            }

            .error-message {
                font-size: 20px;
            }
        }
    </style>
</head>

<body>
    <div class="error-container">
        <div class="error-code">404</div>
        <div class="error-message">Không tìm thấy trang</div>
        <div class="error-details">
            Trang bạn đang tìm kiếm không tồn tại hoặc đã bị di chuyển.
            <br>
            <small>URL: <?php echo $requestedUrl; ?></small>
        </div>

        <div class="error-image">
            <img src="assets/homepage/images/404.png" alt="404 Error" class="img-fluid">
        </div>

        <div class="action-buttons">
            <a href="/" class="btn-custom">
                <i class="fas fa-home"></i> Trang chủ
            </a>
            <?php if (!$isLoggedIn): ?>
                <a href="#" data-bs-toggle="modal" data-bs-target="#loginModal" class="btn-custom">
                    <i class="fas fa-sign-in-alt"></i> Đăng nhập
                </a>
            <?php endif; ?>
            <a href="javascript:history.back()" class="btn-custom">
                <i class="fas fa-arrow-left"></i> Quay lại
            </a>
        </div>

        <div class="suggestions">
            <h3>Bạn có thể thử truy cập:</h3>
            <ul>
                <li><a href="/"><i class="fas fa-home"></i> Trang chủ</a></li>
                <li><a href="/Views/Account"><i class="fas fa-user"></i> Tài khoản</a></li>
                <li><a href="/balance_ranking"><i class="fas fa-trophy"></i> Bảng xếp hạng</a></li>
            </ul>
        </div>
    </div>

    <?php if (!$isLoggedIn): ?>
        <!-- Login Modal -->
        <div class="modal fade" id="loginModal" tabindex="-1" aria-labelledby="loginModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered" style="max-width: 400px; transform: none;">
                <div class="modal-content" style="background-image: url('assets/homepage/images/bg-modal.jpg'); aspect-ratio: auto;">
                    <div class="modal-header">
                        <h5 class="modal-title" id="loginModalLabel">Đăng Nhập</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <form action="/Controllers/Login" method="post">
                            <div class="mb-3">
                                <label for="loginUsername" class="form-label">Tên đăng nhập</label>
                                <input type="text" class="form-control" id="loginUsername" name="user" required>
                            </div>
                            <div class="mb-3">
                                <label for="loginPassword" class="form-label">Mật khẩu</label>
                                <input type="password" class="form-control" id="loginPassword" name="pass" required>
                            </div>
                            <div class="mb-3 text-end">
                                <a href="#" data-bs-toggle="modal" data-bs-target="#forgotPasswordModal" data-bs-dismiss="modal" style="color: #f9d686; text-decoration: none;">Quên mật khẩu?</a>
                            </div>
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary">Đăng Nhập</button>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#registerModal">Đăng Ký</button>
                    </div>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <script>
        // Report 404 error to analytics (if needed)
        document.addEventListener('DOMContentLoaded', function() {
            console.log('404 error: <?php echo $requestedUrl; ?>');

            // Example of sending to Google Analytics (if implemented)
            // if (typeof ga === 'function') {
            //     ga('send', 'event', '404', 'page not found', '<?php echo $requestedUrl; ?>');
            // }
        });
    </script>
</body>

</html>