<?php
// Start the session at the beginning of the file
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['team_user']) || empty($_SESSION['team_user']) || !isset($_SESSION['is_admin']) || !$_SESSION['is_admin']) {
    header("Location: ../index.php?error=" . urlencode('Bạn không có quyền truy cập trang này.'));
    exit();
}

// Include database configuration
require_once '../Controllers/database.php';

// Connect to databases
try {
    // Connect to account database
    $conn = connectPDO($config1);

    // Connect to game_db database
    $conn1 = connectPDO($config2);

    // Create giftcode table if it doesn't exist
    $conn1->exec("CREATE TABLE IF NOT EXISTS giftcode (
        id INT AUTO_INCREMENT PRIMARY KEY,
        giftcode VARCHAR(50) NOT NULL UNIQUE,
        xu INT DEFAULT 0,
        luong INT DEFAULT 0,
        luongLock INT DEFAULT 0,
        item VARCHAR(255) DEFAULT NULL,
        expire DATETIME DEFAULT NULL,
        limit_use INT DEFAULT 1,
        type VARCHAR(20) DEFAULT 'normal'
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");

    // Create giftcode_used table if it doesn't exist
    $conn1->exec("CREATE TABLE IF NOT EXISTS giftcode_used (
        id INT AUTO_INCREMENT PRIMARY KEY,
        giftcode_id INT NOT NULL,
        user_id INT NOT NULL,
        username VARCHAR(50) NOT NULL,
        used_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        UNIQUE KEY (giftcode_id, user_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
} catch (PDOException $e) {
    die("Lỗi kết nối cơ sở dữ liệu: " . $e->getMessage());
}

// Get user information
$userId = $_SESSION['id'];
$username = $_SESSION['team_user'];

// Initialize variables
$message = '';
$messageType = '';

// Handle form submission for adding new giftcode
if (isset($_POST['add_giftcode'])) {
    $giftcode = trim($_POST['giftcode'] ?? '');
    $xu = intval($_POST['xu'] ?? 0);
    $luong = intval($_POST['luong'] ?? 0);
    $luongLock = intval($_POST['luongLock'] ?? 0);
    $item = trim($_POST['item'] ?? '');
    $limitUse = intval($_POST['limit_use'] ?? 1);
    $type = trim($_POST['type'] ?? 'normal');
    // Sử dụng chuỗi rỗng thay vì null nếu không có giá trị expire
    $expire = !empty($_POST['expire']) ? date('Y-m-d H:i:s', strtotime($_POST['expire'])) : '0000-00-00 00:00:00';

    // Validate input
    if (empty($giftcode)) {
        $message = 'Vui lòng nhập mã giftcode.';
        $messageType = 'danger';
    } elseif ($xu <= 0 && $luong <= 0 && $luongLock <= 0 && empty($item)) {
        $message = 'Vui lòng nhập ít nhất một phần thưởng (xu, lượng, lượng khóa hoặc vật phẩm).';
        $messageType = 'danger';
    } elseif ($limitUse <= 0) {
        $message = 'Giới hạn sử dụng phải lớn hơn 0.';
        $messageType = 'danger';
    } else {
        try {
            // Check if giftcode already exists
            $stmt = $conn1->prepare("SELECT id FROM giftcode WHERE giftcode = :giftcode");
            $stmt->bindParam(':giftcode', $giftcode, PDO::PARAM_STR);
            $stmt->execute();

            if ($stmt->rowCount() > 0) {
                $message = 'Giftcode này đã tồn tại.';
                $messageType = 'danger';
            } else {
                // Insert new giftcode
                $stmt = $conn1->prepare("INSERT INTO giftcode (giftcode, xu, luong, luongLock, item, expire, limit_use, type)
                                        VALUES (:giftcode, :xu, :luong, :luongLock, :item, :expire, :limit_use, :type)");
                $stmt->bindParam(':giftcode', $giftcode, PDO::PARAM_STR);
                $stmt->bindParam(':xu', $xu, PDO::PARAM_INT);
                $stmt->bindParam(':luong', $luong, PDO::PARAM_INT);
                $stmt->bindParam(':luongLock', $luongLock, PDO::PARAM_INT);
                $stmt->bindParam(':item', $item, PDO::PARAM_STR);
                $stmt->bindParam(':expire', $expire, PDO::PARAM_STR);
                $stmt->bindParam(':limit_use', $limitUse, PDO::PARAM_INT);
                $stmt->bindParam(':type', $type, PDO::PARAM_STR);

                if ($stmt->execute()) {
                    $message = 'Thêm giftcode thành công.';
                    $messageType = 'success';
                } else {
                    $message = 'Có lỗi xảy ra khi thêm giftcode.';
                    $messageType = 'danger';
                }
            }
        } catch (PDOException $e) {
            $message = 'Lỗi: ' . $e->getMessage();
            $messageType = 'danger';
        }
    }
}

// Handle delete giftcode
if (isset($_GET['action']) && $_GET['action'] == 'delete' && isset($_GET['id'])) {
    $id = intval($_GET['id']);

    try {
        // Check if giftcode exists
        $stmt = $conn1->prepare("SELECT id FROM giftcode WHERE id = :id");
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $stmt->execute();

        if ($stmt->rowCount() == 0) {
            $message = 'Không tìm thấy giftcode.';
            $messageType = 'danger';
        } else {
            // Delete giftcode
            $stmt = $conn1->prepare("DELETE FROM giftcode WHERE id = :id");
            $stmt->bindParam(':id', $id, PDO::PARAM_INT);

            if ($stmt->execute()) {
                // Also delete usage records
                $stmt = $conn1->prepare("DELETE FROM giftcode_used WHERE giftcode_id = :id");
                $stmt->bindParam(':id', $id, PDO::PARAM_INT);
                $stmt->execute();

                $message = 'Xóa giftcode thành công.';
                $messageType = 'success';
            } else {
                $message = 'Có lỗi xảy ra khi xóa giftcode.';
                $messageType = 'danger';
            }
        }
    } catch (PDOException $e) {
        $message = 'Lỗi: ' . $e->getMessage();
        $messageType = 'danger';
    }
}

// Get giftcodes with pagination
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$limit = 10;
$offset = ($page - 1) * $limit;

// Count total giftcodes
try {
    $stmt = $conn1->query("SELECT COUNT(*) FROM giftcode");
    $totalGiftcodes = $stmt->fetchColumn();
    $totalPages = ceil($totalGiftcodes / $limit);
} catch (PDOException $e) {
    $totalGiftcodes = 0;
    $totalPages = 1;
}

// Get giftcodes for current page
$giftcodes = [];
try {
    $stmt = $conn1->prepare("SELECT * FROM giftcode ORDER BY id DESC LIMIT :offset, :limit");
    $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
    $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
    $stmt->execute();
    $giftcodes = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    // Handle error silently
}

// Get usage data for a specific giftcode
$usageData = [];
if (isset($_GET['action']) && $_GET['action'] == 'view_usage' && isset($_GET['id'])) {
    $id = intval($_GET['id']);

    try {
        // Get giftcode info
        $stmt = $conn1->prepare("SELECT * FROM giftcode WHERE id = :id");
        $stmt->bindParam(':id', $id, PDO::PARAM_INT);
        $stmt->execute();
        $giftcodeInfo = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($giftcodeInfo) {
            // Get usage data
            $stmt = $conn1->prepare("SELECT * FROM giftcode_used WHERE giftcode_id = :id ORDER BY used_at DESC");
            $stmt->bindParam(':id', $id, PDO::PARAM_INT);
            $stmt->execute();
            $usageData = $stmt->fetchAll(PDO::FETCH_ASSOC);
        } else {
            $message = 'Không tìm thấy giftcode.';
            $messageType = 'danger';
        }
    } catch (PDOException $e) {
        $message = 'Lỗi: ' . $e->getMessage();
        $messageType = 'danger';
    }
}
?>

<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quản lý Giftcode - KPAH</title>
    <link href="../assets/homepage/css/bootstrap.min.css" rel="stylesheet" type="text/css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <script src="../assets/homepage/js/jquery-1.9.1.min.js"></script>
    <script src="../assets/homepage/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/vn.js"></script>
    <style>
        body {
            background-color: #1a1a1a;
            color: #f9d686;
            font-family: Arial, sans-serif;
        }

        .admin-container {
            padding: 20px;
        }

        .admin-header {
            background-color: #000;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border: 1px solid #f9d686;
        }

        .admin-title {
            margin: 0;
            color: #f9d686;
            font-weight: bold;
        }

        .admin-user {
            color: #fff;
        }

        .admin-card {
            background-color: #2a2a2a;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #f9d686;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .admin-card h2 {
            color: #f9d686;
            font-size: 1.5rem;
            margin-bottom: 15px;
            border-bottom: 1px solid #f9d686;
            padding-bottom: 10px;
        }

        .admin-menu {
            background-color: #2a2a2a;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid #f9d686;
        }

        .admin-menu ul {
            list-style: none;
            padding: 0;
            margin: 0;
            display: flex;
            flex-wrap: wrap;
        }

        .admin-menu li {
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .admin-menu a {
            display: block;
            padding: 8px 15px;
            background-color: #333;
            color: #f9d686;
            text-decoration: none;
            border-radius: 3px;
            border: 1px solid rgba(249, 214, 134, 0.3);
            transition: all 0.3s ease;
        }

        .admin-menu a:hover,
        .admin-menu a.active {
            background-color: #f9d686;
            color: #000;
        }

        .admin-menu a i {
            margin-right: 5px;
        }

        .admin-table {
            width: 100%;
            margin-top: 15px;
        }

        .admin-table th {
            background-color: #333;
            color: #f9d686;
            padding: 10px;
            text-align: left;
        }

        .admin-table td {
            padding: 10px;
            border-bottom: 1px solid #444;
        }

        .admin-table tr:hover td {
            background-color: #3a3a3a;
        }

        .btn-admin {
            background-color: #f9d686;
            color: #000;
            border: none;
            font-weight: bold;
            padding: 8px 16px;
            margin-right: 5px;
        }

        .btn-admin:hover {
            background-color: #e5c677;
            color: #000;
        }

        .btn-danger {
            background-color: #dc3545;
            color: #fff;
        }

        .btn-danger:hover {
            background-color: #c82333;
            color: #fff;
        }

        .badge {
            padding: 5px 8px;
            border-radius: 3px;
            font-size: 0.8rem;
            font-weight: normal;
        }

        .table-responsive {
            color: #f9d686;
        }

        .table {
            color: #f9d686;
        }

        .table tbody tr td {
            color: #f9d686;
        }

        .form-label {
            color: #f9d686;
        }

        .form-control,
        .form-select {
            background-color: #333;
            color: #f9d686;
            border-color: #555;
        }

        .form-control:focus,
        .form-select:focus {
            background-color: #444;
            color: #f9d686;
            border-color: #f9d686;
            box-shadow: 0 0 0 0.25rem rgba(249, 214, 134, 0.25);
        }

        .alert-info {
            background-color: #2a4d69;
            color: #f9d686;
            border-color: #3a6d99;
        }

        .alert-success {
            background-color: #2a6941;
            color: #f9d686;
            border-color: #3a8951;
        }

        .alert-danger {
            background-color: #692a2a;
            color: #f9d686;
            border-color: #893a3a;
        }

        .text-muted {
            color: #aaa !important;
        }

        .pagination .page-link {
            background-color: #333;
            color: #f9d686;
            border-color: #555;
        }

        .pagination .page-link:hover {
            background-color: #444;
            color: #f9d686;
            border-color: #f9d686;
        }

        .pagination .page-item.active .page-link {
            background-color: #f9d686;
            color: #000;
            border-color: #f9d686;
        }

        @media (max-width: 768px) {
            .admin-header {
                flex-direction: column;
                text-align: center;
            }

            .admin-user {
                margin-top: 10px;
            }

            .admin-menu ul {
                flex-direction: column;
            }

            .admin-menu li {
                margin-right: 0;
                margin-bottom: 5px;
            }
        }
    </style>
</head>

<body>
    <div class="container admin-container">
        <div class="admin-header">
            <h1 class="admin-title">KPAH - Quản lý Giftcode</h1>
            <div class="admin-user">
                Xin chào, <strong><?php echo htmlspecialchars($username); ?></strong> |
                <a href="../Views/Account.php" class="text-light">Tài khoản</a> |
                <a href="../handle/logout.php" class="text-light">Đăng xuất</a>
            </div>
        </div>

        <div class="admin-menu">
            <ul>
                <li><a href="index.php"><i class="fas fa-tachometer-alt"></i> Tổng quan</a></li>
                <li><a href="users.php"><i class="fas fa-users"></i> Quản lý người dùng</a></li>
                <li><a href="bank_topups.php"><i class="fas fa-university"></i> Quản lý nạp ATM</a></li>
                <li><a href="bank_accounts.php"><i class="fas fa-credit-card"></i> Quản lý tài khoản ngân hàng</a></li>
                <li><a href="activations.php"><i class="fas fa-check-circle"></i> Kích hoạt tài khoản</a></li>
                <li><a href="giftcodes.php" class="active"><i class="fas fa-gift"></i> Quản lý Giftcode</a></li>
                <li><a href="api_management.php"><i class="fas fa-plug"></i> Quản lý API</a></li>
                <li><a href="password_decoder.php"><i class="fas fa-key"></i> Giải mã mật khẩu</a></li>
                <li><a href="site_settings.php"><i class="fas fa-cogs"></i> Cấu hình trang web</a></li>
                <li><a href="../index.php"><i class="fas fa-home"></i> Về trang chủ</a></li>
            </ul>
        </div>

        <?php if (!empty($message)): ?>
            <div class="alert alert-<?php echo $messageType; ?>" role="alert">
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>

        <?php if (isset($_GET['action']) && $_GET['action'] == 'view_usage' && isset($_GET['id'])): ?>
            <!-- View usage history -->
            <div class="admin-card">
                <h2><i class="fas fa-history"></i> Lịch sử sử dụng Giftcode</h2>
                <p>
                    <a href="giftcodes.php" class="btn btn-secondary"><i class="fas fa-arrow-left"></i> Quay lại</a>
                    <?php if (!empty($giftcodeInfo)): ?>
                        <span class="ms-3">Giftcode:
                            <strong><?php echo htmlspecialchars($giftcodeInfo['giftcode']); ?></strong></span>
                    <?php endif; ?>
                </p>

                <?php if (empty($usageData)): ?>
                    <div class="alert alert-info">Giftcode này chưa được sử dụng.</div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Người dùng</th>
                                    <th>Thời gian sử dụng</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($usageData as $usage): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($usage['id']); ?></td>
                                        <td><?php echo htmlspecialchars($usage['username']); ?></td>
                                        <td><?php echo date('d/m/Y H:i:s', strtotime($usage['used_at'])); ?></td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <!-- Add new giftcode form -->
            <div class="admin-card">
                <h2><i class="fas fa-plus-circle"></i> Thêm Giftcode mới</h2>
                <form method="post" action="">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="giftcode" class="form-label">Mã Giftcode</label>
                                <input type="text" class="form-control" id="giftcode" name="giftcode" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="type" class="form-label">Loại</label>
                                <select class="form-select" id="type" name="type">
                                    <option value="normal">Thường</option>
                                    <option value="vip">VIP</option>
                                    <option value="event">Sự kiện</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="xu" class="form-label">Xu</label>
                                <input type="number" class="form-control" id="xu" name="xu" value="0" min="0">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="luong" class="form-label">Lượng</label>
                                <input type="number" class="form-control" id="luong" name="luong" value="0" min="0">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="luongLock" class="form-label">Lượng khóa</label>
                                <input type="number" class="form-control" id="luongLock" name="luongLock" value="0" min="0">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="limit_use" class="form-label">Giới hạn sử dụng</label>
                                <input type="number" class="form-control" id="limit_use" name="limit_use" value="1" min="1">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="item" class="form-label">Vật phẩm (định dạng: id:số lượng,id:số lượng)</label>
                                <input type="text" class="form-control" id="item" name="item"
                                    placeholder="Ví dụ: 100:1,200:5">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="expire" class="form-label">Thời hạn (để trống nếu không có)</label>
                                <input type="text" class="form-control" id="expire" name="expire">
                            </div>
                        </div>
                    </div>

                    <button type="submit" name="add_giftcode" class="btn btn-primary">Thêm Giftcode</button>
                </form>
            </div>

            <!-- Giftcode list -->
            <div class="admin-card">
                <h2><i class="fas fa-list"></i> Danh sách Giftcode</h2>

                <?php if (empty($giftcodes)): ?>
                    <div class="alert alert-info">Chưa có giftcode nào.</div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Giftcode</th>
                                    <th>Phần thưởng</th>
                                    <th>Loại</th>
                                    <th>Giới hạn sử dụng</th>
                                    <th>Thời hạn</th>
                                    <th>Thao tác</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($giftcodes as $code): ?>
                                    <tr>
                                        <td><span class="text-light"><?php echo htmlspecialchars($code['id']); ?></span></td>
                                        <td><span class="text-light"><?php echo htmlspecialchars($code['giftcode']); ?></span></td>
                                        <td>
                                            <?php if ($code['xu'] > 0): ?>
                                                <span class="badge bg-success"><?php echo number_format($code['xu']); ?> Xu</span>
                                            <?php endif; ?>
                                            <?php if ($code['luong'] > 0): ?>
                                                <span class="badge bg-primary"><?php echo number_format($code['luong']); ?> Lượng</span>
                                            <?php endif; ?>
                                            <?php if ($code['luongLock'] > 0): ?>
                                                <span class="badge bg-info"><?php echo number_format($code['luongLock']); ?> Lượng
                                                    khóa</span>
                                            <?php endif; ?>
                                            <?php if (!empty($code['item'])): ?>
                                                <span class="badge bg-warning">Vật phẩm</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($code['type'] == 'vip'): ?>
                                                <span class="badge bg-danger">VIP</span>
                                            <?php elseif ($code['type'] == 'event'): ?>
                                                <span class="badge bg-warning">Sự kiện</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">Thường</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php
                                            // Count how many times this giftcode has been used
                                            $stmt = $conn1->prepare("SELECT COUNT(*) FROM giftcode_used WHERE giftcode_id = :giftcode_id");
                                            $stmt->bindParam(':giftcode_id', $code['id'], PDO::PARAM_INT);
                                            $stmt->execute();
                                            $usedCount = $stmt->fetchColumn();
                                            echo '<span class="text-light">' . htmlspecialchars($usedCount) . '/' . htmlspecialchars($code['limit_use']) . '</span>';
                                            ?>
                                        </td>
                                        <td>
                                            <?php if (!empty($code['expire']) && $code['expire'] != '0000-00-00 00:00:00'): ?>
                                                <span
                                                    class="text-warning"><?php echo date('d/m/Y H:i', strtotime($code['expire'])); ?></span>
                                            <?php else: ?>
                                                <span class="text-info">Không giới hạn</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <a href="giftcodes.php?action=view_usage&id=<?php echo $code['id']; ?>"
                                                class="btn btn-sm btn-info" title="Xem lịch sử sử dụng">
                                                <i class="fas fa-history"></i>
                                            </a>
                                            <a href="giftcodes.php?action=delete&id=<?php echo $code['id']; ?>"
                                                class="btn btn-sm btn-danger"
                                                onclick="return confirm('Bạn có chắc chắn muốn xóa giftcode này?')" title="Xóa">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <?php if ($totalPages > 1): ?>
                        <nav aria-label="Page navigation" class="mt-3">
                            <ul class="pagination justify-content-center">
                                <?php if ($page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="giftcodes.php?page=<?php echo $page - 1; ?>" aria-label="Previous">
                                            <span aria-hidden="true">&laquo;</span>
                                        </a>
                                    </li>
                                <?php endif; ?>

                                <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                    <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                        <a class="page-link" href="giftcodes.php?page=<?php echo $i; ?>"><?php echo $i; ?></a>
                                    </li>
                                <?php endfor; ?>

                                <?php if ($page < $totalPages): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="giftcodes.php?page=<?php echo $page + 1; ?>" aria-label="Next">
                                            <span aria-hidden="true">&raquo;</span>
                                        </a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>

    <script>
        $(document).ready(function() {
            // Initialize flatpickr for datetime picker
            try {
                // Check if flatpickr and its locale are properly loaded
                if (typeof flatpickr === 'function') {
                    // Check if the Vietnamese locale is available
                    if (flatpickr.l10ns && flatpickr.l10ns.vn) {
                        flatpickr("#expire", {
                            enableTime: true,
                            dateFormat: "Y-m-d H:i",
                            locale: "vn",
                            placeholder: "Chọn thời hạn"
                        });
                    } else {
                        // Fallback to default locale if Vietnamese is not available
                        flatpickr("#expire", {
                            enableTime: true,
                            dateFormat: "Y-m-d H:i",
                            placeholder: "Chọn thời hạn"
                        });
                        console.log("Vietnamese locale not available for flatpickr, using default");
                    }
                } else {
                    console.error("flatpickr is not properly loaded");
                }
            } catch (error) {
                console.error("Error initializing flatpickr:", error);
                // Fallback to a basic input field
                $("#expire").attr("type", "datetime-local");
            }
        });
    </script>
</body>

</html>