-- Create the account database if it doesn't exist
CREATE DATABASE IF NOT EXISTS account CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Use the account database
USE account;

-- Create team_user table if it doesn't exist
CREATE TABLE IF NOT EXISTS team_user (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    phone VARCHAR(20) DEFAULT NULL,
    regdate DATETIME DEFAULT CURRENT_TIMESTAMP,
    ban TINYINT(1) DEFAULT 0,
    provider VARCHAR(50) DEFAULT 'local',
    fromgame VARCHAR(50) DEFAULT 'KPAH',
    email VARCHAR(100) DEFAULT NULL,
    is_admin TINYINT(1) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create user_balance table if it doesn't exist
CREATE TABLE IF NOT EXISTS user_balance (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    balance DECIMAL(10,2) DEFAULT 0.00,
    last_updated DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES team_user(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create card_transactions table if it doesn't exist
CREATE TABLE IF NOT EXISTS card_transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    card_number VARCHAR(50) NOT NULL,
    card_type VARCHAR(50) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    transaction_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    xu_amount INT DEFAULT 0,
    notes TEXT,
    FOREIGN KEY (user_id) REFERENCES team_user(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create bank_accounts table if it doesn't exist
CREATE TABLE IF NOT EXISTS bank_accounts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    bank_name VARCHAR(255) NOT NULL,
    account_name VARCHAR(255) NOT NULL,
    account_number VARCHAR(50) NOT NULL,
    qr_code_path VARCHAR(255),
    is_active TINYINT(1) NOT NULL DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create bank_transactions table if it doesn't exist
CREATE TABLE IF NOT EXISTS bank_transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    bank_account_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    xu_amount INT NOT NULL,
    transaction_code VARCHAR(255),
    user_account VARCHAR(50),
    status ENUM('pending', 'approved', 'rejected') NOT NULL DEFAULT 'pending',
    notes TEXT,
    transaction_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES team_user(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create xu_rates table if it doesn't exist
CREATE TABLE IF NOT EXISTS xu_rates (
    id INT AUTO_INCREMENT PRIMARY KEY,
    amount DECIMAL(10,2) NOT NULL,
    xu INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create password_reset_tokens table if it doesn't exist
CREATE TABLE IF NOT EXISTS password_reset_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    token VARCHAR(255) NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    expires_at DATETIME NOT NULL,
    used TINYINT(1) DEFAULT 0,
    FOREIGN KEY (user_id) REFERENCES team_user(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create the game_db database if it doesn't exist
CREATE DATABASE IF NOT EXISTS game_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Use the game_db database
USE game_db;

-- Create 5h_active table if it doesn't exist
CREATE TABLE IF NOT EXISTS 5h_active (
    id INT AUTO_INCREMENT PRIMARY KEY,
    userID INT NOT NULL,
    username VARCHAR(50) NOT NULL,
    time_end INT DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create tob_char table if it doesn't exist
CREATE TABLE IF NOT EXISTS tob_char (
    id INT AUTO_INCREMENT PRIMARY KEY,
    charname VARCHAR(50) NOT NULL,
    lastLv INT DEFAULT 1,
    gold INT DEFAULT 0,
    topNap INT DEFAULT 0,
    xp INT DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default admin user if not exists
USE account;
INSERT INTO team_user (username, password, email, is_admin)
SELECT 'admin', '*4ACFE3202A5FF5CF467898FC58AAB1D615029441', '<EMAIL>', 1
WHERE NOT EXISTS (SELECT 1 FROM team_user WHERE username = 'admin');

-- Insert a test user if not exists
INSERT INTO team_user (username, password, email, is_admin)
SELECT 'testuser', '*4ACFE3202A5FF5CF467898FC58AAB1D615029441', '<EMAIL>', 0
WHERE NOT EXISTS (SELECT 1 FROM team_user WHERE username = 'testuser');

-- Insert default xu rates if not exists
INSERT INTO xu_rates (amount, xu)
SELECT 10000, 10
WHERE NOT EXISTS (SELECT 1 FROM xu_rates WHERE amount = 10000);

INSERT INTO xu_rates (amount, xu)
SELECT 20000, 20
WHERE NOT EXISTS (SELECT 1 FROM xu_rates WHERE amount = 20000);

INSERT INTO xu_rates (amount, xu)
SELECT 50000, 55
WHERE NOT EXISTS (SELECT 1 FROM xu_rates WHERE amount = 50000);

INSERT INTO xu_rates (amount, xu)
SELECT 100000, 120
WHERE NOT EXISTS (SELECT 1 FROM xu_rates WHERE amount = 100000);

INSERT INTO xu_rates (amount, xu)
SELECT 200000, 250
WHERE NOT EXISTS (SELECT 1 FROM xu_rates WHERE amount = 200000);

INSERT INTO xu_rates (amount, xu)
SELECT 500000, 650
WHERE NOT EXISTS (SELECT 1 FROM xu_rates WHERE amount = 500000);

-- Insert default bank accounts if not exists
INSERT INTO bank_accounts (bank_name, account_name, account_number, is_active)
SELECT 'Vietcombank', 'NGUYEN VAN A', '**********', 1
WHERE NOT EXISTS (SELECT 1 FROM bank_accounts WHERE account_number = '**********');

INSERT INTO bank_accounts (bank_name, account_name, account_number, is_active)
SELECT 'Techcombank', 'NGUYEN VAN A', '**********', 1
WHERE NOT EXISTS (SELECT 1 FROM bank_accounts WHERE account_number = '**********');

INSERT INTO bank_accounts (bank_name, account_name, account_number, is_active)
SELECT 'MBBank', 'NGUYEN VAN A', '**********', 1
WHERE NOT EXISTS (SELECT 1 FROM bank_accounts WHERE account_number = '**********');

-- Insert a test character if not exists
USE game_db;
INSERT INTO tob_char (id, charname, lastLv, gold, topNap, exp)
SELECT 2, 'TestCharacter', 1, 0, 0, 0
WHERE NOT EXISTS (SELECT 1 FROM tob_char WHERE id = 2);

-- Activate the test user account
INSERT INTO 5h_active (userID, username, time_end)
SELECT 2, 'testuser', -1
WHERE NOT EXISTS (SELECT 1 FROM 5h_active WHERE userID = 2);

-- Add some initial balance for the test user
USE account;
INSERT INTO user_balance (user_id, balance)
SELECT 2, 50000
WHERE NOT EXISTS (SELECT 1 FROM user_balance WHERE user_id = 2);

-- Add a sample transaction for the test user
INSERT INTO card_transactions (user_id, card_number, card_type, amount, status, xu_amount, notes)
SELECT 2, 'SAMPLE-**********', 'manual', 50000, 'approved', 600, 'Sample transaction for testing'
WHERE NOT EXISTS (SELECT 1 FROM card_transactions WHERE card_number = 'SAMPLE-**********');

-- Add a sample bank transaction for the test user
INSERT INTO bank_transactions (user_id, bank_account_id, amount, xu_amount, transaction_code, user_account, status, notes)
SELECT 2, 1, 50000, 600, 'BANK-**********', 'testuser', 'approved', 'Sample bank transaction for testing'
WHERE NOT EXISTS (SELECT 1 FROM bank_transactions WHERE transaction_code = 'BANK-**********');
