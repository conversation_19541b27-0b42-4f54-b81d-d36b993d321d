<?php
// Include database configuration
require_once 'Controllers/database.php';

// Connect to database
try {
    $conn = connectPDO($config1);
    echo "Connected to database successfully.<br>";
    
    // Check if card_api_config table exists
    $tableCheck = $conn->query("SHOW TABLES LIKE 'card_api_config'");
    echo "Table card_api_config exists: " . ($tableCheck->rowCount() > 0 ? 'Yes' : 'No') . "<br>";
    
    if ($tableCheck->rowCount() > 0) {
        // Check existing records
        $stmt = $conn->query("SELECT * FROM card_api_config WHERE api_name = 'THESIEURE'");
        $config = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($config) {
            echo "<h3>TheSieuRe API Configuration:</h3>";
            echo "<ul>";
            echo "<li>API Name: " . htmlspecialchars($config['api_name']) . "</li>";
            echo "<li>API URL: " . htmlspecialchars($config['api_url']) . "</li>";
            echo "<li>Partner ID: " . htmlspecialchars($config['merchant_id']) . "</li>";
            echo "<li>Secret Key: " . substr(htmlspecialchars($config['secret_key']), 0, 5) . "..." . "</li>";
            echo "<li>Callback URL: " . htmlspecialchars($config['callback_url']) . "</li>";
            echo "<li>Request Method: " . htmlspecialchars($config['request_method']) . "</li>";
            echo "<li>Is Active: " . ($config['is_active'] ? 'Yes' : 'No') . "</li>";
            echo "</ul>";
        } else {
            echo "<p>No TheSieuRe API configuration found.</p>";
        }
    }
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage();
}
?>
