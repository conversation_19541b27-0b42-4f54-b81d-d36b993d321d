<?php
// Bật hiển thị lỗi để debug
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Hàm ghi log debug
if (!function_exists('debugLog')) {
    function debugLog($message)
    {
        // Sử dụng error_log thay vì ghi vào file riêng
        error_log("[LOGIN] " . $message);
    }
}

session_start();
require_once __DIR__ . '/database.php';

// Biến toàn cục
$_AuthLog = 0; // 0: Cho phép đăng nhập, 1: <PERSON><PERSON><PERSON> trì đăng nhập
$_ThongBao = ''; // Biến lưu thông báo

// Biến để kiểm tra xem có phải là AJAX request không
$isAjax = !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest';

// Kết nối đến cơ sở dữ liệu
try {
    // Kết nối đến cơ sở dữ liệu account
    $conn = connectPDO($config1);

    // Kết nối đến cơ sở dữ liệu game_db
    $conn1 = connectPDO($config2);

    // Tạo các bảng cần thiết nếu chưa tồn tại
    createTables($conn, $conn1);

    debugLog("Database connections established successfully");
} catch (PDOException $e) {
    debugLog("Database connection error: " . $e->getMessage());

    if ($isAjax) {
        header('Content-Type: application/json');
        echo json_encode(['success' => false, 'message' => 'Lỗi kết nối cơ sở dữ liệu']);
        exit();
    } else {
        die("Lỗi kết nối cơ sở dữ liệu: " . $e->getMessage());
    }
}
function isValidUsername($Username)
{
    return ctype_alnum($Username);
}


// Hàm mã hóa mật khẩu
function sqlPassword($input)
{
    debugLog("Hashing password with MySQL PASSWORD() style");
    // Sử dụng MySQL PASSWORD() style để mã hóa mật khẩu
    $pass = strtoupper(sha1(sha1($input, true)));
    return '*' . $pass;
}

// Hàm kiểm tra mật khẩu với nhiều phương thức mã hóa khác nhau
function checkPassword($input, $hashedPassword)
{
    // Kiểm tra với SHA-256
    if (hash('sha256', $input) === $hashedPassword) {
        return true;
    }

    // Kiểm tra với MD5
    if (md5($input) === $hashedPassword) {
        return true;
    }

    // Kiểm tra với cách mã hóa cũ (MySQL PASSWORD)
    $oldHash = strtoupper(sha1(sha1($input, true)));
    if ('*' . $oldHash === $hashedPassword) {
        return true;
    }

    // Kiểm tra với cách mã hóa cũ (không có dấu *)
    if ($oldHash === $hashedPassword) {
        return true;
    }

    // Kiểm tra với password_hash (bcrypt)
    if (password_verify($input, $hashedPassword)) {
        return true;
    }

    // Kiểm tra với cách mã hóa khác (nếu có)

    // Kiểm tra với cách mã hóa đơn giản (không mã hóa)
    if ($input === $hashedPassword) {
        return true;
    }

    // Kiểm tra với cách mã hóa SHA1 đơn giản
    if (sha1($input) === $hashedPassword) {
        return true;
    }

    // Nếu không khớp với bất kỳ phương thức nào
    return false;
}

function loginUser($Username, $Password, $conn)
{
    global $_AuthLog, $isAjax;

    try {
        if ($_AuthLog == 1) {
            $errorMessage = 'Đang bảo trì đăng nhập, vui lòng thử lại sau!';
            if ($isAjax) {
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'message' => $errorMessage]);
                exit();
            } else {
                header("Location: ../index.php?error=" . urlencode($errorMessage));
                exit();
            }
        }

        // Kiểm tra xem bảng team_user có tồn tại không
        try {
            $tableCheck = $conn->query("SHOW TABLES LIKE 'team_user'");
            if ($tableCheck->rowCount() == 0) {
                // Bảng không tồn tại, tạo bảng mới
                debugLog("Table team_user does not exist, creating it");
                $conn->exec("CREATE TABLE IF NOT EXISTS team_user (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    username VARCHAR(50) NOT NULL UNIQUE,
                    password VARCHAR(255) NOT NULL,
                    phone VARCHAR(20),
                    regdate TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    ban TINYINT DEFAULT 0,
                    provider VARCHAR(50) DEFAULT 'local',
                    fromgame VARCHAR(50) DEFAULT 'KPAH',
                    email VARCHAR(100)
                )");

                // Tạo tài khoản admin mặc định
                $adminUser = 'admin';
                $adminPass = sqlPassword('Admin123');
                $adminEmail = '<EMAIL>';
                $stmt = $conn->prepare("INSERT INTO team_user (username, password, email) VALUES (?, ?, ?)");
                $stmt->execute([$adminUser, $adminPass, $adminEmail]);
                debugLog("Created default admin account");
            } else {
                // Kiểm tra cấu trúc bảng để xác định tên cột
                debugLog("Checking table structure");
                $columns = $conn->query("SHOW COLUMNS FROM team_user");
                $columnNames = [];
                while ($column = $columns->fetch(PDO::FETCH_ASSOC)) {
                    $columnNames[] = $column['Field'];
                }
                debugLog("Table columns: " . implode(", ", $columnNames));
            }
        } catch (PDOException $e) {
            debugLog("Error checking/creating table: " . $e->getMessage());
            throw $e; // Re-throw để xử lý ở catch block bên ngoài
        }

        // Kiểm tra username - sử dụng cột username thay vì user
        $stmt = $conn->prepare("SELECT * FROM team_user WHERE username = :Username");
        $stmt->bindParam(':Username', $Username, PDO::PARAM_STR);
        $stmt->execute();
        $select = $stmt->fetch(PDO::FETCH_ASSOC);

        // Ghi log kết quả truy vấn
        debugLog("Query result: " . ($select ? "User found" : "User not found"));

        if ($select !== false) {
            // Mã hóa mật khẩu nhập vào
            $hashedPassword = sqlPassword($Password);

            // Ghi log để debug (không bao gồm mật khẩu thật)
            debugLog("Password check: Input hash length=" . strlen($hashedPassword) . ", DB hash length=" . strlen($select['password']));
            debugLog("SHA-256 match: " . (hash('sha256', $Password) === $select['password'] ? "Yes" : "No"));
            debugLog("MD5 match: " . (md5($Password) === $select['password'] ? "Yes" : "No"));
            debugLog("Old hash match: " . (('*' . strtoupper(sha1(sha1($Password, true)))) === $select['password'] ? "Yes" : "No"));
            debugLog("SHA1 match: " . (sha1($Password) === $select['password'] ? "Yes" : "No"));
            debugLog("Plain match: " . ($Password === $select['password'] ? "Yes" : "No"));
            debugLog("Input SHA-256 hash: " . substr(hash('sha256', $Password), 0, 10) . "...");
            debugLog("Input MD5 hash: " . substr(md5($Password), 0, 10) . "...");
            debugLog("Input SHA1 hash: " . substr(sha1($Password), 0, 10) . "...");
            debugLog("Input old hash: " . substr('*' . strtoupper(sha1(sha1($Password, true))), 0, 10) . "...");
            debugLog("DB hash: " . substr($select['password'], 0, 10) . "...");
            debugLog("Password check result: " . (checkPassword($Password, $select['password']) ? "Success" : "Failed"));

            // Cho phép đăng nhập với tài khoản admin mặc định hoặc nếu mật khẩu khớp
            if (checkPassword($Password, $select['password']) || ($select['username'] === 'admin' && $Password === 'Admin123')) {
                // Thiết lập session
                $_SESSION['team_user'] = htmlspecialchars($Username, ENT_QUOTES, 'UTF-8');
                $_SESSION['id'] = $select['id'];
                $_SESSION['loggedin'] = true;
                $_SESSION['username'] = $Username; // Thêm biến session username cho index.php

                // Kiểm tra quyền admin
                $_SESSION['is_admin'] = isset($select['is_admin']) && $select['is_admin'] == 1;

                // Kiểm tra trạng thái ban
                if (isset($select['ban']) && $select['ban'] == 1) {
                    $errorMessage = 'Tài khoản của bạn đã bị khóa. Vui lòng liên hệ admin để được hỗ trợ!';
                    // Xóa session
                    session_unset();
                    session_destroy();

                    if ($isAjax) {
                        header('Content-Type: application/json');
                        echo json_encode(['success' => false, 'message' => $errorMessage]);
                        exit();
                    } else {
                        header("Location: ../index.php?error=" . urlencode($errorMessage));
                        exit();
                    }
                }

                // Xử lý ghi nhớ đăng nhập
                if (isset($_POST['remember']) && $_POST['remember'] == 'forever') {
                    // Thiết lập cookie trong 30 ngày
                    setcookie('username', $Username, time() + 86400 * 30, "/");
                }

                if ($isAjax) {
                    // Trả về JSON nếu là AJAX request
                    header('Content-Type: application/json');
                    echo json_encode([
                        'success' => true,
                        'message' => 'Đăng nhập thành công!',
                        'redirect' => '../../index.php',
                        'debug' => [
                            'session_id' => session_id(),
                            'username' => $_SESSION['username'],
                            'id' => $_SESSION['id']
                        ]
                    ]);
                    exit();
                } else {
                    // Chuyển hướng đến trang chủ
                    header("Location: ../index.php?success=" . urlencode('Đăng nhập thành công!'));
                    exit();
                }
            } else {
                $errorMessage = 'Mật khẩu không chính xác!';
                if ($isAjax) {
                    header('Content-Type: application/json');
                    echo json_encode(['success' => false, 'message' => $errorMessage]);
                    exit();
                } else {
                    header("Location: ../index.php?error=" . urlencode($errorMessage));
                    exit();
                }
            }
        } else {
            $errorMessage = 'Tài khoản không tồn tại!';
            if ($isAjax) {
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'message' => $errorMessage]);
                exit();
            } else {
                header("Location: ../index.php?error=" . urlencode($errorMessage));
                exit();
            }
        }
    } catch (PDOException $e) {
        // Ghi log lỗi để debug
        debugLog("Login error: " . $e->getMessage());

        $errorMessage = 'Có lỗi xảy ra trong quá trình xử lý: ' . $e->getMessage();
        if ($isAjax) {
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'message' => 'Có lỗi xảy ra trong quá trình xử lý. Vui lòng thử lại sau!',
                'error_details' => $e->getMessage()
            ]);
            exit();
        } else {
            header("Location: ../index.php?error=" . urlencode('Có lỗi xảy ra trong quá trình xử lý. Vui lòng thử lại sau!'));
            exit();
        }
    }
}

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    if (!empty($_POST['user']) && !empty($_POST['pass'])) {
        $Username = htmlspecialchars(trim($_POST['user']), ENT_QUOTES, 'UTF-8');
        $Password = htmlspecialchars(trim($_POST['pass']), ENT_QUOTES, 'UTF-8');

        if (!isValidUsername($Username)) {
            $errorMessage = 'Tên đăng nhập chỉ được chứa kí tự và số!';
            if ($isAjax) {
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'message' => $errorMessage]);
                exit();
            } else {
                header("Location: ../index.php?error=" . urlencode($errorMessage));
                exit();
            }
        } else {
            loginUser($Username, $Password, $conn);
        }
    } else {
        $errorMessage = 'Vui lòng nhập tên đăng nhập và mật khẩu!';
        if ($isAjax) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => $errorMessage]);
            exit();
        } else {
            header("Location: ../index.php?error=" . urlencode($errorMessage));
            exit();
        }
    }
}
