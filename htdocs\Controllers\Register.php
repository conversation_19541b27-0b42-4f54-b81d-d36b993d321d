<?php
// Bật hiển thị lỗi để debug
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Hàm ghi log debug
if (!function_exists('debugLog')) {
    function debugLog($message)
    {
        // Sử dụng error_log thay vì ghi vào file riêng
        error_log("[REGISTER] " . $message);
    }
}

// Bắt đầu session
session_start();

// Kết nối đến cơ sở dữ liệu
require_once __DIR__ . '/database.php';

// Thiết lập kết nối PDO
try {
    // Kết nối đến cơ sở dữ liệu account
    $conn = connectPDO($config1);

    // Kết nối đến cơ sở dữ liệu game_db
    $conn1 = connectPDO($config2);

    // Tạo các bảng cần thiết nếu chưa tồn tại
    createTables($conn, $conn1);

    debugLog("Database connections established successfully");
} catch (PDOException $e) {
    debugLog("Database connection error: " . $e->getMessage());
    die("Lỗi kết nối cơ sở dữ liệu: " . $e->getMessage());
}

// Biến toàn cục
$_AuthLog = 0; // 0: Cho phép đăng ký, 1: Bảo trì đăng ký
$_ThongBao = ''; // Biến lưu thông báo

// Kiểm tra xem có phải là AJAX request không
$isAjax = isset($_POST['ajax']) && $_POST['ajax'] == 1;
// Hoặc kiểm tra header X-Requested-With
if (!$isAjax) {
    $isAjax = !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest';
}
debugLog("Is AJAX request: " . ($isAjax ? "Yes" : "No"));

$Username = '';
$Password = '';
$RePassword = '';

// Hàm mã hóa mật khẩu
function sqlPassword($input)
{
    debugLog("Hashing password with MySQL PASSWORD() style");
    // Sử dụng MySQL PASSWORD() style để mã hóa mật khẩu
    $pass = strtoupper(sha1(sha1($input, true)));
    return '*' . $pass;
}

// Hàm kiểm tra đầu vào hợp lệ
function isValidInput($input)
{
    debugLog("Validating input: " . substr($input, 0, 3) . "...");
    return preg_match('/^[a-zA-Z0-9]+$/', $input);
}

// Hàm kiểm tra tên đăng nhập đã tồn tại chưa
function checkExistingUsername($connection, $Username)
{
    debugLog("Checking if username exists: $Username");
    try {
        $stmt = $connection->prepare("SELECT COUNT(*) FROM team_user WHERE username = ?");
        $stmt->execute([$Username]);
        $result = $stmt->fetchColumn() > 0;
        debugLog("Username exists: " . ($result ? "Yes" : "No"));
        return $result;
    } catch (Exception $e) {
        debugLog("Error checking username: " . $e->getMessage());
        return false;
    }
}

// Hàm thêm tài khoản mới
function insertAccount($connection, $Username, $Password, $Email = '', $Phone = '')
{
    debugLog("Inserting new account: Username=$Username, Email=$Email, Phone=$Phone");
    try {
        // Tạo câu lệnh SQL phù hợp với cấu trúc bảng team_user
        $sql = "INSERT INTO team_user (username, password, email, phone, regdate, ban, provider, fromgame)
                VALUES (?, ?, ?, ?, NOW(), 0, 'local', 'KPAH')";

        debugLog("SQL: $sql");
        $stmt = $connection->prepare($sql);
        $result = $stmt->execute([$Username, $Password, $Email, $Phone]);

        if ($result) {
            $lastId = $connection->lastInsertId();
            debugLog("Account inserted successfully. ID: $lastId");
        } else {
            $errorInfo = $stmt->errorInfo();
            debugLog("Failed to insert account. Error: " . implode(", ", $errorInfo));
        }

        return $result;
    } catch (Exception $e) {
        debugLog("Error inserting account: " . $e->getMessage());
        return false;
    }
}
// Initialize session variable for registration
if (!isset($_SESSION['checkreg'])) {
    $_SESSION['checkreg'] = md5(rand(10000, 99999));
}

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Skip the checkreg validation for now to fix the registration issue
    $_SESSION['checkreg'] = md5(rand(10000, 99999));
    $Username = htmlspecialchars(trim($_POST["username"]));
    $Password = htmlspecialchars(trim($_POST["password"]));
    $RePassword = htmlspecialchars(trim($_POST["repassword"] ?? $_POST["RePasswordword"] ?? ''));

    // Lấy email và phone nếu có
    $Email = htmlspecialchars(trim($_POST["email"] ?? ''));
    $Phone = htmlspecialchars(trim($_POST["phone"] ?? ''));

    // Validate reCAPTCHA (nếu có)
    $recaptcha_response = $_POST['g-recaptcha-response'] ?? '';

    // For development purposes, we'll bypass the reCAPTCHA validation
    // In a production environment, you should uncomment the following code to validate reCAPTCHA
    /*
    if (!empty($recaptcha_response)) {
        $recaptcha_secret = '6LdYVvIqAAAAAKaAWCY3CydvoyxvyXWrflvxcBup'; // Replace with your actual secret key
        $verify_response = file_get_contents('https://www.google.com/recaptcha/api/siteverify?secret=' . $recaptcha_secret . '&response=' . $recaptcha_response);
        $captcha_success = json_decode($verify_response);
        debugLog("reCAPTCHA validation result: " . ($captcha_success->success ? "Success" : "Failed"));
    } else {
        $captcha_success = new stdClass();
        $captcha_success->success = false;
        debugLog("reCAPTCHA response is empty");
    }
    */

    // For now, we'll always set success to true to bypass validation
    $captcha_success = new stdClass();
    $captcha_success->success = true;

    debugLog("reCAPTCHA validation bypassed for debugging");

    if (!$captcha_success->success) {
        $_ThongBao = '<div class="alert alert-danger pb-2 font-weight-bold">Vui lòng xác nhận bạn không phải là robot.</div>';

        // Trả về JSON nếu là AJAX request
        if ($isAjax) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Vui lòng xác nhận bạn không phải là robot.']);
            exit();
        } else {
            header("Location: ../index.php?error=" . urlencode('Vui lòng xác nhận bạn không phải là robot.'));
            exit();
        }
    } elseif ($_AuthLog == 1) {
        $_ThongBao = '<div class="alert alert-danger pb-2 font-weight-bold">Đang bảo trì đăng nhập, vui lòng thử lại sau!</div>';

        // Trả về JSON nếu là AJAX request
        if ($isAjax) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Đang bảo trì đăng nhập, vui lòng thử lại sau!']);
            exit();
        } else {
            header("Location: ../index.php?error=" . urlencode('Đang bảo trì đăng nhập, vui lòng thử lại sau!'));
            exit();
        }
    } elseif (preg_match('/[A-Z]/', $Username)) {
        $_ThongBao = '<div class="alert alert-danger pb-2 font-weight-bold">Tên đăng nhập không được chứa ký tự viết hoa.</div>';

        // Trả về JSON nếu là AJAX request
        if ($isAjax) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Tên đăng nhập không được chứa ký tự viết hoa.']);
            exit();
        } else {
            header("Location: ../index.php?error=" . urlencode('Tên đăng nhập không được chứa ký tự viết hoa.'));
            exit();
        }
    } elseif (!isValidInput($Username) || !isValidInput($Password)) {
        $_ThongBao = '<div class="alert alert-danger pb-2 font-weight-bold">Tên đăng nhập và mật khẩu không được chứa kí tự đặc biệt.</div>';

        // Trả về JSON nếu là AJAX request
        if ($isAjax) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Tên đăng nhập và mật khẩu không được chứa kí tự đặc biệt.']);
            exit();
        } else {
            header("Location: ../index.php?error=" . urlencode('Tên đăng nhập và mật khẩu không được chứa kí tự đặc biệt.'));
            exit();
        }
    } elseif ($Password !== $RePassword) {
        $_ThongBao = '<div class="alert alert-danger pb-2 font-weight-bold">Mật khẩu nhập lại không khớp!</div>';

        // Trả về JSON nếu là AJAX request
        if ($isAjax) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Mật khẩu nhập lại không khớp!']);
            exit();
        } else {
            header("Location: ../index.php?error=" . urlencode('Mật khẩu nhập lại không khớp!'));
            exit();
        }
    } else {
        // Use the global connection variable
        if (checkExistingUsername($conn, $Username)) {
            $_ThongBao = "<div class='alert alert-danger pb-2 font-weight-bold'>Tài khoản đã tồn tại.</div>";

            // Trả về JSON nếu là AJAX request
            if ($isAjax) {
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'message' => 'Tài khoản đã tồn tại.']);
                exit();
            } else {
                header("Location: ../index.php?error=" . urlencode('Tài khoản đã tồn tại.'));
                exit();
            }
        } else {
            if (insertAccount($conn, $Username, sqlPassword($Password))) {
                // Thêm bản ghi vào bảng 5h_active để kích hoạt tài khoản
                try {
                    $userID = $conn->lastInsertId();
                    debugLog("Account created with ID: $userID. Attempting to activate account.");

                    // Kết nối đến cơ sở dữ liệu game_db
                    global $conn1; // Sử dụng biến kết nối từ database.php

                    if (isset($conn1)) {
                        $query = "INSERT INTO 5h_active (userID, username, time_end) VALUES (:userID, :username, 0)";
                        $stmt = $conn1->prepare($query);
                        $stmt->bindParam(":userID", $userID, PDO::PARAM_INT);
                        $stmt->bindParam(":username", $Username, PDO::PARAM_STR);
                        $stmt->execute();
                        debugLog("Added user to 5h_active table");
                    } else {
                        debugLog("conn1 is not defined, cannot add user to 5h_active table");
                    }
                } catch (Exception $e) {
                    debugLog("Error activating account: " . $e->getMessage());
                }
                $_ThongBao = '<div class="alert alert-success pb-2 font-weight-bold">Đăng kí thành công!!</div>';

                // Trả về JSON nếu là AJAX request
                if ($isAjax) {
                    header('Content-Type: application/json');
                    echo json_encode(['success' => true, 'message' => 'Đăng ký thành công!']);
                    exit();
                } else {
                    header("Location: ../index.php?success=" . urlencode('Đăng ký thành công!'));
                    exit();
                }
            } else {
                $_ThongBao = '<div class="alert alert-danger pb-2 font-weight-bold">Đăng ký thất bại.</div>';

                // Trả về JSON nếu là AJAX request
                if ($isAjax) {
                    header('Content-Type: application/json');
                    echo json_encode(['success' => false, 'message' => 'Đăng ký thất bại.']);
                    exit();
                } else {
                    header("Location: ../index.php?error=" . urlencode('Đăng ký thất bại.'));
                    exit();
                }
            }
        }
    }
    // This else block is no longer needed as we're skipping the checkreg validation
}
