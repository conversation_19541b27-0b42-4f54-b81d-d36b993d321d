<?php
// Dynamic theme CSS generator
header('Content-Type: text/css');

// Load configuration
require_once '../../config.php';

// Get theme configuration
$theme = $site_config['theme'] ?? [];

// Default values if theme not set
$defaults = [
    'primary_color' => '#f9d686',
    'primary_dark' => '#e5c677',
    'secondary_color' => '#343a40',
    'text_color' => '#f8f9fa',
    'bg_dark' => 'rgba(0, 0, 0, 0.7)',
    'bg_darker' => 'rgba(0, 0, 0, 0.8)',
    'border_color' => 'rgba(249, 214, 134, 0.3)',
    'success_color' => '#28a745',
    'warning_color' => '#ffc107',
    'danger_color' => '#dc3545',
    'info_color' => '#17a2b8',
    'background_image' => 'assets/homepage/images/bg-page1.jpg',
    'admin_bg_color' => '#1a1a1a',
    'admin_card_bg' => '#2a2a2a',
    'admin_header_bg' => '#000000',
    'toolbar_color' => '#333333',
    'button_hover_effect' => 'enabled',
    'border_radius' => '10px',
    'box_shadow' => '0 4px 6px rgba(0, 0, 0, 0.1)'
];

// Merge with defaults
foreach ($defaults as $key => $value) {
    if (!isset($theme[$key])) {
        $theme[$key] = $value;
    }
}

// Convert border_color if it's a hex color
if (strpos($theme['border_color'], '#') === 0) {
    // Convert hex to rgba with 0.3 opacity
    $hex = str_replace('#', '', $theme['border_color']);
    $r = hexdec(substr($hex, 0, 2));
    $g = hexdec(substr($hex, 2, 2));
    $b = hexdec(substr($hex, 4, 2));
    $theme['border_color'] = "rgba($r, $g, $b, 0.3)";
}

// Generate CSS
?>
/* Dynamic Theme CSS - Generated from config */
:root {
    --primary-color: <?php echo $theme['primary_color']; ?>;
    --primary-dark: <?php echo $theme['primary_dark']; ?>;
    --secondary-color: <?php echo $theme['secondary_color']; ?>;
    --text-color: <?php echo $theme['text_color']; ?>;
    --bg-dark: <?php echo $theme['bg_dark']; ?>;
    --bg-darker: <?php echo $theme['bg_darker']; ?>;
    --border-color: <?php echo $theme['border_color']; ?>;
    --success-color: <?php echo $theme['success_color']; ?>;
    --warning-color: <?php echo $theme['warning_color']; ?>;
    --danger-color: <?php echo $theme['danger_color']; ?>;
    --info-color: <?php echo $theme['info_color']; ?>;
    --admin-bg-color: <?php echo $theme['admin_bg_color']; ?>;
    --admin-card-bg: <?php echo $theme['admin_card_bg']; ?>;
    --admin-header-bg: <?php echo $theme['admin_header_bg']; ?>;
    --toolbar-color: <?php echo $theme['toolbar_color']; ?>;
    --border-radius: <?php echo $theme['border_radius']; ?>;
    --box-shadow: <?php echo $theme['box_shadow']; ?>;
}

/* Apply theme to body */
body {
    background-image: url('../../<?php echo $theme['background_image']; ?>');
    color: var(--primary-color);
}

/* Admin specific styles */
.admin-container,
.admin-page {
    background-color: var(--admin-bg-color);
    color: var(--primary-color);
}

.admin-header {
    background-color: var(--admin-header-bg);
    border: 2px solid var(--primary-color);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.admin-card,
.card {
    background-color: var(--admin-card-bg);
    border: 1px solid var(--primary-color);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.admin-menu {
    background-color: var(--admin-card-bg);
    border: 1px solid var(--primary-color);
    border-radius: var(--border-radius);
}

.admin-menu a {
    background-color: var(--toolbar-color);
    color: var(--primary-color);
    border: 1px solid var(--border-color);
    border-radius: calc(var(--border-radius) / 2);
}

.admin-menu a:hover,
.admin-menu a.active {
    background-color: var(--primary-color);
    color: var(--secondary-color);
}

/* Button styles */
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--secondary-color);
    border-radius: calc(var(--border-radius) / 2);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
    color: var(--secondary-color);
    <?php if ($theme['button_hover_effect'] === 'enabled'): ?>
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    <?php endif; ?>
}

.btn-secondary {
    background-color: transparent;
    border-color: var(--primary-color);
    color: var(--primary-color);
    border-radius: calc(var(--border-radius) / 2);
}

.btn-secondary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--secondary-color);
}

/* Form controls */
.form-control {
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid var(--primary-color);
    color: var(--text-color);
    border-radius: calc(var(--border-radius) / 2);
}

.form-control:focus {
    background-color: rgba(255, 255, 255, 0.15);
    border-color: var(--primary-color);
    color: var(--text-color);
    box-shadow: 0 0 0 0.2rem var(--border-color);
}

/* Alert styles */
.alert-success {
    background-color: rgba(40, 167, 69, 0.1);
    border-color: var(--success-color);
    color: var(--success-color);
    border-radius: var(--border-radius);
}

.alert-warning {
    background-color: rgba(255, 193, 7, 0.1);
    border-color: var(--warning-color);
    color: var(--warning-color);
    border-radius: var(--border-radius);
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.1);
    border-color: var(--danger-color);
    color: var(--danger-color);
    border-radius: var(--border-radius);
}

.alert-info {
    background-color: rgba(23, 162, 184, 0.1);
    border-color: var(--info-color);
    color: var(--info-color);
    border-radius: var(--border-radius);
}

/* Modal styles */
.modal-content {
    background-color: var(--admin-card-bg);
    border: 1px solid var(--primary-color);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.modal-header {
    border-bottom: 1px solid var(--primary-color);
    background-color: var(--border-color);
}

.modal-footer {
    border-top: 1px solid var(--primary-color);
}

/* Table styles */
.table {
    color: var(--text-color);
}

.table th {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.table td {
    border-color: var(--border-color);
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: var(--border-color);
}

/* Stat cards */
.stat-card {
    background-color: var(--toolbar-color);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

/* Navigation */
.nav-tabs .nav-link {
    color: var(--primary-color);
    background-color: transparent;
    border: 1px solid transparent;
    border-radius: calc(var(--border-radius) / 2) calc(var(--border-radius) / 2) 0 0;
}

.nav-tabs .nav-link.active {
    background-color: var(--primary-color);
    color: var(--secondary-color);
    border-color: var(--primary-color);
}

.nav-tabs .nav-link:hover {
    background-color: var(--border-color);
    border-color: var(--primary-color);
}

/* Scrollbar */
::-webkit-scrollbar-thumb {
    background-color: var(--primary-color);
    border-radius: calc(var(--border-radius) / 2);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .admin-header {
        padding: 15px;
        margin-bottom: 20px;
    }
    
    .admin-menu ul {
        flex-direction: column;
    }
    
    .admin-menu li {
        margin-right: 0;
        margin-bottom: 5px;
    }
}

/* Animation for hover effects */
<?php if ($theme['button_hover_effect'] === 'enabled'): ?>
.btn, .admin-menu a, .card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}
<?php endif; ?>
