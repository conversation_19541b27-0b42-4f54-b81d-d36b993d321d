<?php
// Start the session at the beginning of the file
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['team_user']) || empty($_SESSION['team_user']) || !isset($_SESSION['is_admin']) || !$_SESSION['is_admin']) {
    header("Location: ../index.php?error=" . urlencode('Bạn không có quyền truy cập trang này.'));
    exit();
}

// Include database configuration
require_once '../Controllers/database.php';

// Connect to databases
try {
    // Connect to account database
    $conn = connectPDO($config1);

    // Connect to game_db database
    $conn1 = connectPDO($config2);
} catch (PDOException $e) {
    die("Lỗi kết nối cơ sở dữ liệu: " . $e->getMessage());
}

// Get user information
$userId = $_SESSION['id'];
$username = $_SESSION['team_user'];

// Initialize variables
$message = '';
$messageType = '';
$searchTerm = isset($_GET['search']) ? trim($_GET['search']) : '';
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$perPage = 10;
$offset = ($page - 1) * $perPage;

// Handle topup actions
if (isset($_GET['action'])) {
    $action = $_GET['action'];
    $transactionId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

    // View transaction details
    if ($action === 'view' && $transactionId > 0) {
        try {
            // Get transaction details
            $stmt = $conn->prepare("SELECT ct.*, tu.username FROM card_transactions ct
                                   JOIN team_user tu ON ct.user_id = tu.id
                                   WHERE ct.id = :id");
            $stmt->bindParam(':id', $transactionId, PDO::PARAM_INT);
            $stmt->execute();
            $transactionDetails = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$transactionDetails) {
                $message = 'Không tìm thấy giao dịch.';
                $messageType = 'danger';
            }
        } catch (PDOException $e) {
            $message = 'Lỗi khi lấy thông tin giao dịch: ' . $e->getMessage();
            $messageType = 'danger';
        }
    }

    // Approve transaction
    if ($action === 'approve' && $transactionId > 0) {
        try {
            // Begin transaction
            $conn->beginTransaction();

            // Get transaction details
            $stmt = $conn->prepare("SELECT * FROM card_transactions WHERE id = :id");
            $stmt->bindParam(':id', $transactionId, PDO::PARAM_INT);
            $stmt->execute();
            $transaction = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$transaction) {
                throw new Exception('Không tìm thấy giao dịch.');
            }

            if ($transaction['status'] !== 'pending') {
                throw new Exception('Giao dịch này đã được xử lý trước đó.');
            }

            // Update transaction status
            $stmt = $conn->prepare("UPDATE card_transactions SET status = 'approved', notes = CONCAT(IFNULL(notes, ''), ' | Duyệt bởi admin: ', :adminUsername, ' vào lúc: ', NOW()) WHERE id = :id");
            $stmt->bindParam(':id', $transactionId, PDO::PARAM_INT);
            $stmt->bindParam(':adminUsername', $username, PDO::PARAM_STR);
            $stmt->execute();

            // Update user balance
            $stmt = $conn->prepare("SELECT * FROM user_balance WHERE user_id = :userId");
            $stmt->bindParam(':userId', $transaction['user_id'], PDO::PARAM_INT);
            $stmt->execute();
            $balance = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($balance) {
                // Update existing balance
                $newBalance = $balance['balance'] + $transaction['amount'];
                $stmt = $conn->prepare("UPDATE user_balance SET balance = :balance, last_updated = NOW() WHERE user_id = :userId");
                $stmt->bindParam(':balance', $newBalance, PDO::PARAM_STR);
                $stmt->bindParam(':userId', $transaction['user_id'], PDO::PARAM_INT);
                $stmt->execute();
            } else {
                // Create new balance record
                $stmt = $conn->prepare("INSERT INTO user_balance (user_id, balance, last_updated) VALUES (:userId, :balance, NOW())");
                $stmt->bindParam(':userId', $transaction['user_id'], PDO::PARAM_INT);
                $stmt->bindParam(':balance', $transaction['amount'], PDO::PARAM_STR);
                $stmt->execute();
            }

            // Commit transaction
            $conn->commit();

            $message = 'Đã duyệt giao dịch thành công.';
            $messageType = 'success';
        } catch (Exception $e) {
            // Rollback transaction on error
            $conn->rollBack();

            $message = 'Lỗi khi duyệt giao dịch: ' . $e->getMessage();
            $messageType = 'danger';
        }
    }

    // Reject transaction
    if ($action === 'reject' && $transactionId > 0) {
        try {
            // Get transaction details
            $stmt = $conn->prepare("SELECT * FROM card_transactions WHERE id = :id");
            $stmt->bindParam(':id', $transactionId, PDO::PARAM_INT);
            $stmt->execute();
            $transaction = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$transaction) {
                throw new Exception('Không tìm thấy giao dịch.');
            }

            if ($transaction['status'] !== 'pending') {
                throw new Exception('Giao dịch này đã được xử lý trước đó.');
            }

            // Update transaction status
            $stmt = $conn->prepare("UPDATE card_transactions SET status = 'rejected' WHERE id = :id");
            $stmt->bindParam(':id', $transactionId, PDO::PARAM_INT);
            $stmt->execute();

            $message = 'Đã từ chối giao dịch thành công.';
            $messageType = 'success';
        } catch (Exception $e) {
            $message = 'Lỗi khi từ chối giao dịch: ' . $e->getMessage();
            $messageType = 'danger';
        }
    }
}

// Get transactions with search and pagination
$transactions = [];
$totalTransactions = 0;

try {
    // Build query based on search term
    $query = "SELECT ct.*, tu.username FROM card_transactions ct JOIN team_user tu ON ct.user_id = tu.id";
    $countQuery = "SELECT COUNT(*) FROM card_transactions ct JOIN team_user tu ON ct.user_id = tu.id";
    $params = [];

    if (!empty($searchTerm)) {
        $query .= " WHERE tu.username LIKE :search OR ct.card_number LIKE :search OR ct.card_type LIKE :search";
        $countQuery .= " WHERE tu.username LIKE :search OR ct.card_number LIKE :search OR ct.card_type LIKE :search";
        $params[':search'] = "%$searchTerm%";
    }

    $query .= " ORDER BY ct.transaction_date DESC LIMIT :offset, :perPage";

    // Get total count for pagination
    $stmt = $conn->prepare($countQuery);
    if (!empty($searchTerm)) {
        $stmt->bindParam(':search', $params[':search'], PDO::PARAM_STR);
    }
    $stmt->execute();
    $totalTransactions = $stmt->fetchColumn();

    // Get transactions for current page
    $stmt = $conn->prepare($query);
    if (!empty($searchTerm)) {
        $stmt->bindParam(':search', $params[':search'], PDO::PARAM_STR);
    }
    $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
    $stmt->bindParam(':perPage', $perPage, PDO::PARAM_INT);
    $stmt->execute();
    $transactions = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $message = 'Lỗi khi lấy danh sách giao dịch: ' . $e->getMessage();
    $messageType = 'danger';
}

// Calculate pagination
$totalPages = ceil($totalTransactions / $perPage);
?>

<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quản lý nạp thẻ - KPAH</title>
    <link href="../assets/homepage/css/bootstrap.min.css" rel="stylesheet" type="text/css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <script src="../assets/homepage/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/homepage/js/jquery-1.9.1.min.js"></script>
    <style>
        body {
            background-color: #1a1a1a;
            color: #f9d686;
            font-family: Arial, sans-serif;
        }

        .admin-container {
            padding: 20px;
        }

        .admin-header {
            background-color: #000;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border: 1px solid #f9d686;
        }

        .admin-title {
            margin: 0;
            color: #f9d686;
            font-weight: bold;
        }

        .admin-user {
            color: #fff;
        }

        .admin-card {
            background-color: #2a2a2a;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #f9d686;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .admin-card h2 {
            color: #f9d686;
            font-size: 1.5rem;
            margin-bottom: 15px;
            border-bottom: 1px solid #f9d686;
            padding-bottom: 10px;
        }

        .admin-table {
            width: 100%;
            margin-top: 15px;
        }

        .admin-table th {
            background-color: #333;
            color: #f9d686;
            padding: 10px;
            text-align: left;
        }

        .admin-table td {
            padding: 10px;
            border-bottom: 1px solid #444;
        }

        .admin-table tr:hover td {
            background-color: #3a3a3a;
        }

        .btn-admin {
            background-color: #f9d686;
            color: #000;
            border: none;
            font-weight: bold;
            padding: 8px 16px;
            margin-right: 5px;
            margin-bottom: 5px;
        }

        .btn-admin:hover {
            background-color: #e5c677;
            color: #000;
        }

        .btn-danger {
            background-color: #dc3545;
            color: #fff;
        }

        .btn-danger:hover {
            background-color: #c82333;
            color: #fff;
        }

        .btn-success {
            background-color: #28a745;
            color: #fff;
        }

        .btn-success:hover {
            background-color: #218838;
            color: #fff;
        }

        .admin-menu {
            background-color: #2a2a2a;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid #f9d686;
        }

        .admin-menu ul {
            list-style: none;
            padding: 0;
            margin: 0;
            display: flex;
            flex-wrap: wrap;
        }

        .admin-menu li {
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .admin-menu a {
            display: block;
            padding: 8px 15px;
            background-color: #333;
            color: #f9d686;
            text-decoration: none;
            border-radius: 3px;
            border: 1px solid rgba(249, 214, 134, 0.3);
            transition: all 0.3s ease;
        }

        .admin-menu a:hover,
        .admin-menu a.active {
            background-color: #f9d686;
            color: #000;
        }

        .admin-menu a i {
            margin-right: 5px;
        }

        .search-form {
            margin-bottom: 20px;
        }

        .search-form .form-control {
            background-color: #333;
            border: 1px solid #f9d686;
            color: #fff;
        }

        .search-form .form-control:focus {
            box-shadow: 0 0 0 0.2rem rgba(249, 214, 134, 0.25);
        }

        .pagination {
            margin-top: 20px;
            justify-content: center;
        }

        .pagination .page-item .page-link {
            background-color: #333;
            border-color: #f9d686;
            color: #f9d686;
        }

        .pagination .page-item.active .page-link {
            background-color: #f9d686;
            border-color: #f9d686;
            color: #000;
        }

        .pagination .page-item .page-link:hover {
            background-color: #444;
        }

        .transaction-details {
            background-color: #333;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .transaction-details h3 {
            color: #f9d686;
            margin-bottom: 15px;
            border-bottom: 1px solid #f9d686;
            padding-bottom: 10px;
        }

        .transaction-details .info-item {
            margin-bottom: 10px;
        }

        .transaction-details .info-label {
            font-weight: bold;
            color: #f9d686;
            width: 150px;
            display: inline-block;
        }

        .status-pending {
            color: #ffc107;
            font-weight: bold;
        }

        .status-approved {
            color: #28a745;
            font-weight: bold;
        }

        .status-rejected {
            color: #dc3545;
            font-weight: bold;
        }

        @media (max-width: 768px) {
            .admin-header {
                flex-direction: column;
                text-align: center;
            }

            .admin-user {
                margin-top: 10px;
            }

            .admin-menu ul {
                flex-direction: column;
            }

            .admin-menu li {
                margin-right: 0;
                margin-bottom: 5px;
            }

            .btn-admin {
                display: block;
                width: 100%;
                margin-bottom: 5px;
            }
        }
    </style>
</head>

<body>
    <div class="container admin-container">
        <div class="admin-header">
            <h1 class="admin-title">KPAH - Quản lý nạp thẻ</h1>
            <div class="admin-user">
                Xin chào, <strong><?php echo htmlspecialchars($username); ?></strong> |
                <a href="../Views/Account.php" class="text-light">Tài khoản</a> |
                <a href="../handle/logout.php" class="text-light">Đăng xuất</a>
            </div>
        </div>

        <div class="admin-menu">
            <ul>
                <li><a href="index.php"><i class="fas fa-tachometer-alt"></i> Tổng quan</a></li>
                <li><a href="users.php"><i class="fas fa-users"></i> Quản lý người dùng</a></li>
                <li><a href="bank_topups.php"><i class="fas fa-university"></i> Quản lý nạp ATM</a></li>
                <li><a href="bank_accounts.php"><i class="fas fa-credit-card"></i> Quản lý tài khoản ngân hàng</a></li>
                <li><a href="activations.php"><i class="fas fa-check-circle"></i> Kích hoạt tài khoản</a></li>
                <li><a href="giftcodes.php"><i class="fas fa-gift"></i> Quản lý Giftcode</a></li>
                <li><a href="api_management.php"><i class="fas fa-plug"></i> Quản lý API</a></li>
                <li><a href="password_decoder.php"><i class="fas fa-key"></i> Giải mã mật khẩu</a></li>
                <li><a href="site_settings.php"><i class="fas fa-cogs"></i> Cấu hình trang web</a></li>
                <li><a href="topups.php" class="active"><i class="fas fa-credit-card"></i> Quản lý nạp thẻ</a></li>
                <li><a href="manual_topup.php"><i class="fas fa-plus-circle"></i> Nạp thẻ thủ công</a></li>

                <li><a href="../index.php"><i class="fas fa-home"></i> Về trang chủ</a></li>
            </ul>
        </div>

        <?php if (!empty($message)): ?>
            <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                <?php echo $message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($transactionDetails)): ?>
            <div class="admin-card">
                <h2><i class="fas fa-credit-card"></i> Chi tiết giao dịch</h2>
                <div class="transaction-details">
                    <div class="info-item">
                        <span class="info-label">ID giao dịch:</span>
                        <?php echo $transactionDetails['id']; ?>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Người dùng:</span>
                        <?php echo htmlspecialchars($transactionDetails['username']); ?>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Loại thẻ:</span>
                        <?php echo htmlspecialchars($transactionDetails['card_type']); ?>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Mã thẻ:</span>
                        <?php echo htmlspecialchars($transactionDetails['card_number']); ?>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Mệnh giá:</span>
                        <?php echo number_format($transactionDetails['amount'], 0, ',', '.'); ?> VNĐ
                    </div>
                    <div class="info-item">
                        <span class="info-label">Số xu:</span>
                        <?php echo number_format($transactionDetails['xu_amount']); ?> xu
                    </div>
                    <div class="info-item">
                        <span class="info-label">Ngày giao dịch:</span>
                        <?php echo date('d/m/Y H:i:s', strtotime($transactionDetails['transaction_date'])); ?>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Trạng thái:</span>
                        <?php
                        switch ($transactionDetails['status']) {
                            case 'pending':
                                echo '<span class="status-pending">Đang xử lý</span>';
                                break;
                            case 'approved':
                                echo '<span class="status-approved">Đã duyệt</span>';
                                break;
                            case 'rejected':
                                echo '<span class="status-rejected">Từ chối</span>';
                                break;
                            default:
                                echo $transactionDetails['status'];
                        }
                        ?>
                    </div>

                    <?php if ($transactionDetails['status'] === 'pending'): ?>
                        <div class="mt-4">
                            <a href="topups.php?action=approve&id=<?php echo $transactionDetails['id']; ?>"
                                class="btn btn-success" onclick="return confirm('Bạn có chắc chắn muốn duyệt giao dịch này?')">
                                <i class="fas fa-check"></i> Duyệt giao dịch
                            </a>
                            <a href="topups.php?action=reject&id=<?php echo $transactionDetails['id']; ?>"
                                class="btn btn-danger" onclick="return confirm('Bạn có chắc chắn muốn từ chối giao dịch này?')">
                                <i class="fas fa-times"></i> Từ chối giao dịch
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
                <a href="topups.php" class="btn btn-admin">
                    <i class="fas fa-arrow-left"></i> Quay lại danh sách
                </a>
            </div>
        <?php else: ?>
            <div class="admin-card">
                <h2><i class="fas fa-credit-card"></i> Danh sách giao dịch nạp thẻ</h2>

                <form class="search-form" method="get" action="topups.php">
                    <div class="input-group">
                        <input type="text" class="form-control"
                            placeholder="Tìm kiếm theo tên người dùng, mã thẻ, loại thẻ..." name="search"
                            value="<?php echo htmlspecialchars($searchTerm); ?>">
                        <button class="btn btn-admin" type="submit">
                            <i class="fas fa-search"></i> Tìm kiếm
                        </button>
                    </div>
                </form>

                <?php if (empty($transactions)): ?>
                    <p>Không có giao dịch nào.</p>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="admin-table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Người dùng</th>
                                    <th>Loại thẻ</th>
                                    <th>Mệnh giá</th>
                                    <th>Ngày giao dịch</th>
                                    <th>Trạng thái</th>
                                    <th>Thao tác</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($transactions as $transaction): ?>
                                    <tr>
                                        <td><?php echo $transaction['id']; ?></td>
                                        <td><?php echo htmlspecialchars($transaction['username']); ?></td>
                                        <td><?php echo htmlspecialchars($transaction['card_type']); ?></td>
                                        <td><?php echo number_format($transaction['amount'], 0, ',', '.'); ?> VNĐ</td>
                                        <td><?php echo date('d/m/Y H:i', strtotime($transaction['transaction_date'])); ?></td>
                                        <td>
                                            <?php
                                            switch ($transaction['status']) {
                                                case 'pending':
                                                    echo '<span class="status-pending">Đang xử lý</span>';
                                                    break;
                                                case 'approved':
                                                    echo '<span class="status-approved">Đã duyệt</span>';
                                                    break;
                                                case 'rejected':
                                                    echo '<span class="status-rejected">Từ chối</span>';
                                                    break;
                                                default:
                                                    echo $transaction['status'];
                                            }
                                            ?>
                                        </td>
                                        <td>
                                            <a href="topups.php?action=view&id=<?php echo $transaction['id']; ?>"
                                                class="btn btn-sm btn-admin">
                                                <i class="fas fa-eye"></i> Xem
                                            </a>
                                            <?php if ($transaction['status'] === 'pending'): ?>
                                                <a href="topups.php?action=approve&id=<?php echo $transaction['id']; ?>"
                                                    class="btn btn-sm btn-success"
                                                    onclick="return confirm('Bạn có chắc chắn muốn duyệt giao dịch này?')">
                                                    <i class="fas fa-check"></i> Duyệt
                                                </a>
                                                <a href="topups.php?action=reject&id=<?php echo $transaction['id']; ?>"
                                                    class="btn btn-sm btn-danger"
                                                    onclick="return confirm('Bạn có chắc chắn muốn từ chối giao dịch này?')">
                                                    <i class="fas fa-times"></i> Từ chối
                                                </a>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <?php if ($totalPages > 1): ?>
                        <nav aria-label="Page navigation">
                            <ul class="pagination">
                                <?php if ($page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link"
                                            href="topups.php?page=<?php echo ($page - 1); ?>&search=<?php echo urlencode($searchTerm); ?>"
                                            aria-label="Previous">
                                            <span aria-hidden="true">&laquo;</span>
                                        </a>
                                    </li>
                                <?php endif; ?>

                                <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                    <li class="page-item <?php echo ($i === $page) ? 'active' : ''; ?>">
                                        <a class="page-link"
                                            href="topups.php?page=<?php echo $i; ?>&search=<?php echo urlencode($searchTerm); ?>">
                                            <?php echo $i; ?>
                                        </a>
                                    </li>
                                <?php endfor; ?>

                                <?php if ($page < $totalPages): ?>
                                    <li class="page-item">
                                        <a class="page-link"
                                            href="topups.php?page=<?php echo ($page + 1); ?>&search=<?php echo urlencode($searchTerm); ?>"
                                            aria-label="Next">
                                            <span aria-hidden="true">&raquo;</span>
                                        </a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>

    <script>
        $(document).ready(function() {
            // Auto-dismiss alerts after 5 seconds
            setTimeout(function() {
                $('.alert').alert('close');
            }, 5000);
        });
    </script>
</body>

</html>