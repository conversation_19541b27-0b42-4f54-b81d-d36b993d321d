<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Đặt lại mật khẩu - KPAH Game</title>
    <link href="../assets/homepage/css/bootstrap.min.css" rel="stylesheet" type="text/css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <script src="../assets/homepage/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/homepage/js/jquery-1.9.1.min.js"></script>
    <style>
        body {
            background-color: #1a1a1a;
            color: #f9d686;
            font-family: Arial, sans-serif;
            background-image: url('../assets/homepage/images/bg-body.jpg');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
        }

        .reset-container {
            max-width: 500px;
            margin: 50px auto;
            padding: 20px;
            background-color: rgba(0, 0, 0, 0.7);
            border-radius: 10px;
            border: 1px solid #f9d686;
            box-shadow: 0 0 20px rgba(249, 214, 134, 0.3);
        }

        .reset-header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #f9d686;
        }

        .reset-header h2 {
            color: #f9d686;
            font-weight: bold;
        }

        .form-label {
            color: #f9d686;
            font-weight: bold;
        }

        .form-control {
            background-color: rgba(255, 255, 255, 0.9);
            border: 1px solid #f9d686;
            color: #000;
        }

        .btn-primary {
            background-color: #f9d686;
            border-color: #f9d686;
            color: #000;
            font-weight: bold;
            width: 100%;
            margin-top: 10px;
        }

        .btn-primary:hover {
            background-color: #e5c677;
            border-color: #e5c677;
            color: #000;
        }

        .alert {
            border-radius: 5px;
            margin-bottom: 20px;
        }

        .alert-danger {
            background-color: rgba(220, 53, 69, 0.8);
            border-color: #dc3545;
            color: white;
        }

        .alert-success {
            background-color: rgba(40, 167, 69, 0.8);
            border-color: #28a745;
            color: white;
        }

        .password-strength {
            margin-top: 5px;
            font-size: 0.85rem;
        }

        .strength-weak {
            color: #dc3545;
        }

        .strength-medium {
            color: #ffc107;
        }

        .strength-strong {
            color: #28a745;
        }

        .home-link {
            text-align: center;
            margin-top: 20px;
        }

        .home-link a {
            color: #f9d686;
            text-decoration: none;
        }

        .home-link a:hover {
            text-decoration: underline;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="reset-container">
            <div class="reset-header">
                <h2><i class="fas fa-key"></i> Đặt lại mật khẩu</h2>
            </div>

            <?php if (isset($error)): ?>
                <div class="alert alert-danger">
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <?php if (isset($success)): ?>
                <div class="alert alert-success">
                    <?php echo htmlspecialchars($success); ?>
                </div>
                <div class="home-link">
                    <a href="../index.php" class="btn btn-primary">Quay lại trang đăng nhập</a>
                </div>
            <?php elseif ($validToken): ?>
                <form action="reset.php?token=<?php echo htmlspecialchars($token); ?>" method="post" id="resetPasswordForm">
                    <div class="mb-3">
                        <label for="newPassword" class="form-label">Mật khẩu mới</label>
                        <input type="password" class="form-control" id="newPassword" name="new_password" required>
                        <div class="password-strength" id="passwordStrength"></div>
                    </div>

                    <div class="mb-3">
                        <label for="confirmPassword" class="form-label">Xác nhận mật khẩu</label>
                        <input type="password" class="form-control" id="confirmPassword" name="confirm_password" required>
                        <div id="passwordMatch" class="mt-1"></div>
                    </div>

                    <button type="submit" class="btn btn-primary">Đặt lại mật khẩu</button>
                </form>
            <?php elseif ($tokenExpired): ?>
                <div class="alert alert-danger">
                    Liên kết đặt lại mật khẩu đã hết hạn. Vui lòng yêu cầu liên kết mới.
                </div>
                <div class="home-link">
                    <a href="../index.php" class="btn btn-primary">Quay lại trang đăng nhập</a>
                </div>
            <?php else: ?>
                <div class="alert alert-danger">
                    Liên kết đặt lại mật khẩu không hợp lệ hoặc đã được sử dụng.
                </div>
                <div class="home-link">
                    <a href="../index.php" class="btn btn-primary">Quay lại trang đăng nhập</a>
                </div>
            <?php endif; ?>

            <div class="home-link mt-3">
                <a href="../index.php"><i class="fas fa-home"></i> Trang chủ</a>
            </div>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            // Password strength checker
            $('#newPassword').on('input', function() {
                var password = $(this).val();
                var strength = 0;
                var feedback = '';

                if (password.length >= 8) {
                    strength += 1;
                }

                if (password.match(/[a-z]/) && password.match(/[A-Z]/)) {
                    strength += 1;
                }

                if (password.match(/\d/)) {
                    strength += 1;
                }

                if (password.match(/[^a-zA-Z\d]/)) {
                    strength += 1;
                }

                if (password.length === 0) {
                    $('#passwordStrength').html('');
                } else if (strength < 2) {
                    $('#passwordStrength').html('<span class="strength-weak">Yếu</span>');
                } else if (strength < 4) {
                    $('#passwordStrength').html('<span class="strength-medium">Trung bình</span>');
                } else {
                    $('#passwordStrength').html('<span class="strength-strong">Mạnh</span>');
                }
            });

            // Password match checker
            $('#confirmPassword').on('input', function() {
                var password = $('#newPassword').val();
                var confirmPassword = $(this).val();

                if (confirmPassword.length === 0) {
                    $('#passwordMatch').html('');
                } else if (password === confirmPassword) {
                    $('#passwordMatch').html('<span class="strength-strong">Mật khẩu khớp</span>');
                } else {
                    $('#passwordMatch').html('<span class="strength-weak">Mật khẩu không khớp</span>');
                }
            });

            // Form validation
            $('#resetPasswordForm').on('submit', function(e) {
                var password = $('#newPassword').val();
                var confirmPassword = $('#confirmPassword').val();

                if (password.length < <?php echo $app_config['min_password_length']; ?>) {
                    e.preventDefault();
                    alert('Mật khẩu phải có ít nhất <?php echo $app_config['min_password_length']; ?> ký tự.');
                    return false;
                }

                if (password !== confirmPassword) {
                    e.preventDefault();
                    alert('Mật khẩu xác nhận không khớp.');
                    return false;
                }

                return true;
            });
        });
    </script>
</body>

</html>