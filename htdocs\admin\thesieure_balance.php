<?php
// Bật hiển thị lỗi để debug
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Include database configuration
require_once '../Controllers/database.php';

// Include AccountManager class
require_once '../Controllers/Account.php';

// Kiểm tra đăng nhập admin
session_start();
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

// Kết nối đến database
$conn = connectPDO($config1);

// Lấy cấu hình API TheSieuRe.com
$stmt = $conn->prepare("SELECT * FROM card_api_config WHERE api_name = 'THESIEURE' AND is_active = 1 LIMIT 1");
$stmt->execute();
$apiConfig = $stmt->fetch(PDO::FETCH_ASSOC);

// Khởi tạo AccountManager
$accountManager = new AccountManager($conn);

// X<PERSON> lý yêu cầu kiểm tra số dư
$balanceResult = null;
if (isset($_POST['check_balance'])) {
    if ($apiConfig) {
        $balanceResult = $accountManager->getBalance($apiConfig);
    } else {
        $balanceResult = ['success' => false, 'message' => 'Không tìm thấy cấu hình API TheSieuRe.com'];
    }
}

// Xử lý yêu cầu lấy danh sách sản phẩm
$productResult = null;
if (isset($_POST['get_products'])) {
    if ($apiConfig) {
        $productResult = $accountManager->getProductList($apiConfig);
    } else {
        $productResult = ['success' => false, 'message' => 'Không tìm thấy cấu hình API TheSieuRe.com'];
    }
}

// Xử lý yêu cầu kiểm tra trạng thái giao dịch
$statusResult = null;
if (isset($_POST['check_status']) && !empty($_POST['transaction_id'])) {
    if ($apiConfig) {
        $transactionId = $_POST['transaction_id'];
        $orderCode = isset($_POST['order_code']) ? $_POST['order_code'] : '';
        $statusResult = $accountManager->checkCardStatus($apiConfig, $transactionId, $orderCode);
    } else {
        $statusResult = ['success' => false, 'message' => 'Không tìm thấy cấu hình API TheSieuRe.com'];
    }
}

// Lấy danh sách giao dịch gần đây
$stmt = $conn->query("SELECT * FROM card_transactions ORDER BY created_at DESC LIMIT 10");
$recentTransactions = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quản lý TheSieuRe.com - KPAH</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            background-color: #f8f9fa;
            padding-top: 20px;
        }

        .card {
            margin-bottom: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .card-header {
            background-color: #343a40;
            color: white;
            border-radius: 10px 10px 0 0 !important;
        }

        .btn-primary {
            background-color: #343a40;
            border-color: #343a40;
        }

        .btn-primary:hover {
            background-color: #23272b;
            border-color: #23272b;
        }

        .table-responsive {
            overflow-x: auto;
        }

        .status-pending {
            color: #ffc107;
            font-weight: bold;
        }

        .status-success {
            color: #28a745;
            font-weight: bold;
        }

        .status-failed {
            color: #dc3545;
            font-weight: bold;
        }

        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #dee2e6;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1 class="mb-4">Quản lý TheSieuRe.com</h1>

        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Kiểm tra số dư tài khoản</h5>
                    </div>
                    <div class="card-body">
                        <?php if (!$apiConfig): ?>
                            <div class="alert alert-warning">
                                Chưa cấu hình API TheSieuRe.com. Vui lòng cấu hình trước khi sử dụng.
                            </div>
                        <?php else: ?>
                            <form method="post" action="">
                                <div class="mb-3">
                                    <label class="form-label">Partner ID</label>
                                    <input type="text" class="form-control" value="<?php echo htmlspecialchars($apiConfig['merchant_id']); ?>" readonly>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">API URL</label>
                                    <input type="text" class="form-control" value="<?php echo htmlspecialchars($apiConfig['api_url']); ?>" readonly>
                                </div>
                                <button type="submit" name="check_balance" class="btn btn-primary">Kiểm tra số dư</button>
                            </form>

                            <?php if ($balanceResult): ?>
                                <hr>
                                <h6>Kết quả:</h6>
                                <?php if ($balanceResult['success']): ?>
                                    <div class="alert alert-success">
                                        <p><strong>Số dư:</strong> <?php echo number_format($balanceResult['data']['balance']); ?> <?php echo $balanceResult['data']['currency']; ?></p>
                                    </div>
                                <?php else: ?>
                                    <div class="alert alert-danger">
                                        <p><strong>Lỗi:</strong> <?php echo htmlspecialchars($balanceResult['message']); ?></p>
                                    </div>
                                <?php endif; ?>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Kiểm tra trạng thái giao dịch</h5>
                    </div>
                    <div class="card-body">
                        <?php if (!$apiConfig): ?>
                            <div class="alert alert-warning">
                                Chưa cấu hình API TheSieuRe.com. Vui lòng cấu hình trước khi sử dụng.
                            </div>
                        <?php else: ?>
                            <form method="post" action="">
                                <div class="mb-3">
                                    <label for="transaction_id" class="form-label">Mã giao dịch</label>
                                    <input type="text" class="form-control" id="transaction_id" name="transaction_id" required>
                                </div>
                                <div class="mb-3">
                                    <label for="order_code" class="form-label">Mã đơn hàng (nếu có)</label>
                                    <input type="text" class="form-control" id="order_code" name="order_code">
                                </div>
                                <button type="submit" name="check_status" class="btn btn-primary">Kiểm tra trạng thái</button>
                            </form>

                            <?php if ($statusResult): ?>
                                <hr>
                                <h6>Kết quả:</h6>
                                <?php if ($statusResult['success']): ?>
                                    <div class="alert alert-success">
                                        <p><strong>Trạng thái:</strong> <?php echo htmlspecialchars($statusResult['data']['status']); ?></p>
                                        <p><strong>Mệnh giá:</strong> <?php echo number_format($statusResult['data']['amount']); ?> VNĐ</p>
                                        <?php if (isset($statusResult['data']['account']) && isset($statusResult['data']['account']['phone'])): ?>
                                            <p><strong>Số điện thoại:</strong> <?php echo htmlspecialchars($statusResult['data']['account']['phone']); ?></p>
                                        <?php endif; ?>
                                    </div>
                                <?php else: ?>
                                    <div class="alert alert-danger">
                                        <p><strong>Lỗi:</strong> <?php echo htmlspecialchars($statusResult['message']); ?></p>
                                    </div>
                                <?php endif; ?>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Danh sách giao dịch gần đây</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Mã giao dịch</th>
                                <th>Người dùng</th>
                                <th>Loại thẻ</th>
                                <th>Mệnh giá</th>
                                <th>Xu</th>
                                <th>Trạng thái</th>
                                <th>Ngày tạo</th>
                                <th>Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($recentTransactions as $transaction): ?>
                                <?php
                                // Lấy tên người dùng
                                $stmt = $conn->prepare("SELECT username FROM team_user WHERE id = :user_id");
                                $stmt->bindParam(':user_id', $transaction['user_id'], PDO::PARAM_INT);
                                $stmt->execute();
                                $user = $stmt->fetch(PDO::FETCH_ASSOC);
                                $username = $user ? $user['username'] : 'Unknown';

                                // Xác định class cho trạng thái
                                $statusClass = '';
                                switch ($transaction['status']) {
                                    case 'pending':
                                        $statusClass = 'status-pending';
                                        $statusText = 'Đang xử lý';
                                        break;
                                    case 'success':
                                        $statusClass = 'status-success';
                                        $statusText = 'Thành công';
                                        break;
                                    default:
                                        $statusClass = 'status-failed';
                                        $statusText = 'Thất bại';
                                }
                                ?>
                                <tr>
                                    <td><?php echo $transaction['id']; ?></td>
                                    <td><?php echo htmlspecialchars($transaction['transaction_id']); ?></td>
                                    <td><?php echo htmlspecialchars($username); ?></td>
                                    <td><?php echo htmlspecialchars($transaction['card_type']); ?></td>
                                    <td><?php echo number_format($transaction['amount']); ?> VNĐ</td>
                                    <td><?php echo number_format($transaction['xu_amount']); ?></td>
                                    <td class="<?php echo $statusClass; ?>"><?php echo $statusText; ?></td>
                                    <td><?php echo date('d/m/Y H:i', strtotime($transaction['created_at'])); ?></td>
                                    <td>
                                        <form method="post" action="" style="display: inline;">
                                            <input type="hidden" name="transaction_id" value="<?php echo htmlspecialchars($transaction['transaction_id']); ?>">
                                            <button type="submit" name="check_status" class="btn btn-sm btn-primary">
                                                <i class="fas fa-search"></i> Kiểm tra
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                            <?php if (empty($recentTransactions)): ?>
                                <tr>
                                    <td colspan="9" class="text-center">Không có giao dịch nào</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Danh sách sản phẩm</h5>
            </div>
            <div class="card-body">
                <?php if (!$apiConfig): ?>
                    <div class="alert alert-warning">
                        Chưa cấu hình API TheSieuRe.com. Vui lòng cấu hình trước khi sử dụng.
                    </div>
                <?php else: ?>
                    <form method="post" action="" class="mb-3">
                        <button type="submit" name="get_products" class="btn btn-primary">Lấy danh sách sản phẩm</button>
                    </form>

                    <?php if ($productResult): ?>
                        <?php if ($productResult['success']): ?>
                            <div class="accordion" id="productAccordion">
                                <?php foreach ($productResult['data'] as $index => $product): ?>
                                    <div class="accordion-item">
                                        <h2 class="accordion-header" id="heading<?php echo $index; ?>">
                                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse<?php echo $index; ?>" aria-expanded="false" aria-controls="collapse<?php echo $index; ?>">
                                                <?php echo htmlspecialchars($product['name']); ?> (<?php echo htmlspecialchars($product['service_code']); ?>)
                                            </button>
                                        </h2>
                                        <div id="collapse<?php echo $index; ?>" class="accordion-collapse collapse" aria-labelledby="heading<?php echo $index; ?>" data-bs-parent="#productAccordion">
                                            <div class="accordion-body">
                                                <?php if (isset($product['image'])): ?>
                                                    <img src="<?php echo htmlspecialchars($product['image']); ?>" alt="<?php echo htmlspecialchars($product['name']); ?>" class="img-fluid mb-3" style="max-height: 100px;">
                                                <?php endif; ?>

                                                <h6>Mệnh giá:</h6>
                                                <div class="table-responsive">
                                                    <table class="table table-sm table-bordered">
                                                        <thead>
                                                            <tr>
                                                                <th>Tên</th>
                                                                <th>Giá trị</th>
                                                                <th>Giá</th>
                                                                <th>Chiết khấu</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            <?php foreach ($product['items'] as $item): ?>
                                                                <tr>
                                                                    <td><?php echo htmlspecialchars($item['name']); ?></td>
                                                                    <td><?php echo number_format($item['value']); ?> <?php echo htmlspecialchars($item['currency_code']); ?></td>
                                                                    <td><?php echo number_format($item['price']); ?> <?php echo htmlspecialchars($item['currency_code']); ?></td>
                                                                    <td><?php echo htmlspecialchars($item['discount']); ?>%</td>
                                                                </tr>
                                                            <?php endforeach; ?>
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-danger">
                                <p><strong>Lỗi:</strong> <?php echo htmlspecialchars($productResult['message']); ?></p>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>

</html>