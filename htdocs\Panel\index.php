<?php
// Start the session at the beginning of the file
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['team_user']) || empty($_SESSION['team_user']) || !isset($_SESSION['is_admin']) || !$_SESSION['is_admin']) {
    header("Location: ../index.php?error=" . urlencode('Bạn không có quyền truy cập trang này.'));
    exit();
}

// Include database configuration
require_once '../Controllers/database.php';

// Connect to databases
try {
    // Connect to account database
    $conn = connectPDO($config1);

    // Connect to game_db database
    $conn1 = connectPDO($config2);
} catch (PDOException $e) {
    die("Lỗi kết nối cơ sở dữ liệu: " . $e->getMessage());
}

// Get user information
$userId = $_SESSION['id'];
$username = $_SESSION['team_user'];

// Get statistics
$userCount = 0;
$pendingTopups = 0;
$activeUsers = 0;

try {
    // Count total users
    $stmt = $conn->query("SELECT COUNT(*) FROM team_user");
    $userCount = $stmt->fetchColumn();

    // Count pending top-ups
    $stmt = $conn->query("SELECT COUNT(*) FROM card_transactions WHERE status = 'pending'");
    $pendingTopups = $stmt->fetchColumn();

    // Count active users
    $stmt = $conn1->query("SELECT COUNT(*) FROM 5h_active WHERE time_end = -1");
    $activeUsers = $stmt->fetchColumn();
} catch (PDOException $e) {
    // Handle error silently
}

// Get recent transactions
$recentTransactions = [];
try {
    $stmt = $conn->query("SELECT ct.*, tu.username FROM card_transactions ct
                         JOIN team_user tu ON ct.user_id = tu.id
                         ORDER BY ct.transaction_date DESC LIMIT 5");
    $recentTransactions = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    // Handle error silently
}

// Get recent users
$recentUsers = [];
try {
    $stmt = $conn->query("SELECT * FROM team_user ORDER BY regdate DESC LIMIT 5");
    $recentUsers = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    // Handle error silently
}
?>

<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quản trị - KPAH</title>
    <link href="../assets/homepage/css/bootstrap.min.css" rel="stylesheet" type="text/css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <script src="../assets/homepage/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/homepage/js/jquery-1.9.1.min.js"></script>
    <style>
        body {
            background-color: #1a1a1a;
            color: #f9d686;
            font-family: Arial, sans-serif;
        }

        .admin-container {
            padding: 20px;
        }

        .admin-header {
            background-color: #000;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border: 1px solid #f9d686;
        }

        .admin-title {
            margin: 0;
            color: #f9d686;
            font-weight: bold;
        }

        .admin-user {
            color: #fff;
        }

        .admin-card {
            background-color: #2a2a2a;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #f9d686;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .admin-card h2 {
            color: #f9d686;
            font-size: 1.5rem;
            margin-bottom: 15px;
            border-bottom: 1px solid #f9d686;
            padding-bottom: 10px;
        }

        .stat-card {
            background-color: #333;
            border-radius: 5px;
            padding: 15px;
            text-align: center;
            margin-bottom: 15px;
            border: 1px solid rgba(249, 214, 134, 0.3);
        }

        .stat-card .stat-icon {
            font-size: 2rem;
            margin-bottom: 10px;
            color: #f9d686;
        }

        .stat-card .stat-value {
            font-size: 1.8rem;
            font-weight: bold;
            color: #fff;
        }

        .stat-card .stat-label {
            color: #aaa;
            font-size: 0.9rem;
        }

        .admin-table {
            width: 100%;
            margin-top: 15px;
        }

        .admin-table th {
            background-color: #333;
            color: #f9d686;
            padding: 10px;
            text-align: left;
        }

        .admin-table td {
            padding: 10px;
            border-bottom: 1px solid #444;
        }

        .admin-table tr:hover td {
            background-color: #3a3a3a;
        }

        .status-pending {
            color: #ffc107;
            font-weight: bold;
        }

        .status-approved {
            color: #28a745;
            font-weight: bold;
        }

        .status-rejected {
            color: #dc3545;
            font-weight: bold;
        }

        .btn-admin {
            background-color: #f9d686;
            color: #000;
            border: none;
            font-weight: bold;
            padding: 8px 16px;
            margin-right: 5px;
        }

        .btn-admin:hover {
            background-color: #e5c677;
            color: #000;
        }

        .btn-danger {
            background-color: #dc3545;
            color: #fff;
        }

        .btn-danger:hover {
            background-color: #c82333;
            color: #fff;
        }

        .admin-menu {
            background-color: #2a2a2a;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid #f9d686;
        }

        .admin-menu ul {
            list-style: none;
            padding: 0;
            margin: 0;
            display: flex;
            flex-wrap: wrap;
        }

        .admin-menu li {
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .admin-menu a {
            display: block;
            padding: 8px 15px;
            background-color: #333;
            color: #f9d686;
            text-decoration: none;
            border-radius: 3px;
            border: 1px solid rgba(249, 214, 134, 0.3);
            transition: all 0.3s ease;
        }

        .admin-menu a:hover,
        .admin-menu a.active {
            background-color: #f9d686;
            color: #000;
        }

        .admin-menu a i {
            margin-right: 5px;
        }

        @media (max-width: 768px) {
            .admin-header {
                flex-direction: column;
                text-align: center;
            }

            .admin-user {
                margin-top: 10px;
            }

            .admin-menu ul {
                flex-direction: column;
            }

            .admin-menu li {
                margin-right: 0;
                margin-bottom: 5px;
            }
        }
    </style>
</head>

<body>
    <div class="container admin-container">
        <div class="admin-header">
            <h1 class="admin-title">KPAH - Trang Quản Trị</h1>
            <div class="admin-user">
                Xin chào, <strong><?php echo htmlspecialchars($username); ?></strong> |
                <a href="../Views/Account.php" class="text-light">Tài khoản</a> |
                <a href="../handle/logout.php" class="text-light">Đăng xuất</a>
            </div>
        </div>

        <div class="admin-menu">
            <ul>
                <li><a href="index.php" class="active"><i class="fas fa-tachometer-alt"></i> Tổng quan</a></li>
                <li><a href="users.php"><i class="fas fa-users"></i> Quản lý người dùng</a></li>
                <li><a href="bank_topups.php"><i class="fas fa-university"></i> Quản lý nạp ATM</a></li>
                <li><a href="bank_accounts.php"><i class="fas fa-credit-card"></i> Quản lý tài khoản ngân hàng</a></li>
                <li><a href="activations.php"><i class="fas fa-check-circle"></i> Kích hoạt tài khoản</a></li>
                <li><a href="giftcodes.php"><i class="fas fa-gift"></i> Quản lý Giftcode</a></li>
                <li><a href="api_management.php"><i class="fas fa-plug"></i> Quản lý API</a></li>
                <li><a href="password_decoder.php"><i class="fas fa-key"></i> Giải mã mật khẩu</a></li>
                <li><a href="site_settings.php"><i class="fas fa-cogs"></i> Cấu hình trang web</a></li>

                <li><a href="../index.php"><i class="fas fa-home"></i> Về trang chủ</a></li>
            </ul>
        </div>

        <div class="row">
            <div class="col-md-4">
                <div class="stat-card">
                    <div class="stat-icon"><i class="fas fa-users"></i></div>
                    <div class="stat-value"><?php echo number_format($userCount); ?></div>
                    <div class="stat-label">Tổng số người dùng</div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-card">
                    <div class="stat-icon"><i class="fas fa-credit-card"></i></div>
                    <div class="stat-value"><?php echo number_format($pendingTopups); ?></div>
                    <div class="stat-label">Yêu cầu nạp thẻ đang chờ</div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-card">
                    <div class="stat-icon"><i class="fas fa-check-circle"></i></div>
                    <div class="stat-value"><?php echo number_format($activeUsers); ?></div>
                    <div class="stat-label">Tài khoản đã kích hoạt</div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="admin-card">
                    <h2><i class="fas fa-credit-card"></i> Giao dịch gần đây</h2>
                    <?php if (empty($recentTransactions)): ?>
                        <p>Không có giao dịch nào.</p>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="admin-table">
                                <thead>
                                    <tr>
                                        <th>Người dùng</th>
                                        <th>Loại thẻ</th>
                                        <th>Mệnh giá</th>
                                        <th>Trạng thái</th>
                                        <th>Thao tác</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recentTransactions as $transaction): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($transaction['username']); ?></td>
                                            <td><?php echo htmlspecialchars($transaction['card_type']); ?></td>
                                            <td><?php echo number_format($transaction['amount'], 0, ',', '.'); ?> VNĐ</td>
                                            <td>
                                                <?php
                                                switch ($transaction['status']) {
                                                    case 'pending':
                                                        echo '<span class="status-pending">Đang xử lý</span>';
                                                        break;
                                                    case 'approved':
                                                        echo '<span class="status-approved">Đã duyệt</span>';
                                                        break;
                                                    case 'rejected':
                                                        echo '<span class="status-rejected">Từ chối</span>';
                                                        break;
                                                    default:
                                                        echo $transaction['status'];
                                                }
                                                ?>
                                            </td>
                                            <td>
                                                <?php if ($transaction['status'] == 'pending'): ?>
                                                    <a href="topups.php?action=view&id=<?php echo $transaction['id']; ?>"
                                                        class="btn btn-sm btn-admin">Xem</a>
                                                <?php else: ?>
                                                    <a href="topups.php?action=view&id=<?php echo $transaction['id']; ?>"
                                                        class="btn btn-sm btn-admin">Chi tiết</a>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <div class="mt-3">
                            <a href="topups.php" class="btn btn-admin">Xem tất cả</a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <div class="col-md-6">
                <div class="admin-card">
                    <h2><i class="fas fa-users"></i> Người dùng mới đăng ký</h2>
                    <?php if (empty($recentUsers)): ?>
                        <p>Không có người dùng mới.</p>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="admin-table">
                                <thead>
                                    <tr>
                                        <th>Tên đăng nhập</th>
                                        <th>Email</th>
                                        <th>Ngày đăng ký</th>
                                        <th>Thao tác</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recentUsers as $user): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($user['username']); ?></td>
                                            <td><?php echo htmlspecialchars($user['email'] ?? 'Chưa có'); ?></td>
                                            <td><?php echo date('d/m/Y', strtotime($user['regdate'])); ?></td>
                                            <td>
                                                <a href="users.php?action=view&id=<?php echo $user['id']; ?>"
                                                    class="btn btn-sm btn-admin">Xem</a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <div class="mt-3">
                            <a href="users.php" class="btn btn-admin">Xem tất cả</a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            // Add any JavaScript functionality here
        });
    </script>
</body>

</html>