<?php
// Include database configuration
require_once 'Controllers/database.php';

// <PERSON><PERSON>m tra xem có tham số ngrok_url không
if (isset($_POST['ngrok_url'])) {
    $ngrokUrl = trim($_POST['ngrok_url']);

    // Đảm bảo URL không có dấu / ở cuối
    $ngrokUrl = rtrim($ngrokUrl, '/');

    // Tạo callback URL mới
    $callbackUrl = $ngrokUrl . '/api/card_callback.php';

    try {
        // Kết nối đến database
        $conn = connectPDO($config1);

        // Cập nhật callback URL trong bảng card_api_config
        $stmt = $conn->prepare("UPDATE card_api_config SET callback_url = :callback_url WHERE api_name = 'THESIEURE'");
        $stmt->bindParam(':callback_url', $callbackUrl, PDO::PARAM_STR);
        $result = $stmt->execute();

        if ($result) {
            echo "<div style='color: green; margin-bottom: 10px;'>Callback URL đã được cập nhật thành công!</div>";
            echo "<div>Callback URL mới: <strong>" . htmlspecialchars($callbackUrl) . "</strong></div>";
        } else {
            echo "<div style='color: red; margin-bottom: 10px;'>Không thể cập nhật Callback URL.</div>";
        }
    } catch (PDOException $e) {
        echo "<div style='color: red; margin-bottom: 10px;'>Lỗi: " . $e->getMessage() . "</div>";
    }
}

// Lấy callback URL hiện tại
try {
    $conn = connectPDO($config1);
    $stmt = $conn->prepare("SELECT callback_url FROM card_api_config WHERE api_name = 'THESIEURE'");
    $stmt->execute();
    $config = $stmt->fetch(PDO::FETCH_ASSOC);

    $currentCallbackUrl = $config ? $config['callback_url'] : 'Chưa thiết lập';
} catch (PDOException $e) {
    $currentCallbackUrl = 'Lỗi: ' . $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cập nhật Callback URL</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        h1 {
            color: #333;
        }

        .form-group {
            margin-bottom: 15px;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        input[type="text"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }

        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        button:hover {
            background-color: #45a049;
        }

        .info-box {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .current-url {
            font-weight: bold;
            color: #007bff;
        }

        .steps {
            margin-top: 30px;
        }

        .step {
            margin-bottom: 15px;
        }

        .step-number {
            display: inline-block;
            width: 25px;
            height: 25px;
            background-color: #007bff;
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 25px;
            margin-right: 10px;
        }
    </style>
</head>

<body>
    <h1>Cập nhật Callback URL cho TheSieuRe.com</h1>

    <div class="info-box">
        <p>Callback URL hiện tại: <span class="current-url"><?php echo htmlspecialchars($currentCallbackUrl); ?></span>
        </p>
    </div>

    <form method="post" action="">
        <div class="form-group">
            <label for="ngrok_url">Nhập URL Ngrok của bạn:</label>
            <input type="text" id="ngrok_url" name="ngrok_url" placeholder="Ví dụ: https://a1b2c3d4e5f6.ngrok.io"
                required>
            <small>Lưu ý: Chỉ nhập URL gốc, không bao gồm đường dẫn /api/card_callback.php</small>
        </div>

        <button type="submit">Cập nhật Callback URL</button>
    </form>

    <div class="steps">
        <h2>Hướng dẫn sử dụng Ngrok</h2>

        <div class="step">
            <span class="step-number">1</span>
            <span>Tải và cài đặt Ngrok từ <a href="https://ngrok.com/download"
                    target="_blank">ngrok.com/download</a></span>
        </div>

        <div class="step">
            <span class="step-number">2</span>
            <span>Đăng ký tài khoản và lấy auth token từ <a
                    href="https://dashboard.ngrok.com/get-started/your-authtoken"
                    target="_blank">dashboard.ngrok.com</a></span>
        </div>

        <div class="step">
            <span class="step-number">3</span>
            <span>Xác thực Ngrok bằng lệnh: <code>ngrok authtoken YOUR_AUTH_TOKEN</code></span>
        </div>

        <div class="step">
            <span class="step-number">4</span>
            <span>Chạy XAMPP và bắt đầu Apache</span>
        </div>

        <div class="step">
            <span class="step-number">5</span>
            <span>Chạy lệnh để tạo tunnel: <code>ngrok http 80</code> (hoặc cổng khác nếu Apache của bạn không chạy trên
                cổng 80)</span>
        </div>

        <div class="step">
            <span class="step-number">6</span>
            <span>Sao chép URL Forwarding (ví dụ: https://a1b2c3d4e5f6.ngrok.io) và dán vào form trên</span>
        </div>

        <div class="step">
            <span class="step-number">7</span>
            <span>Nhấp vào nút "Cập nhật Callback URL"</span>
        </div>

        <div class="step">
            <span class="step-number">8</span>
            <span>Thông báo cho TheSieuRe.com về URL callback mới của bạn (nếu cần)</span>
        </div>
    </div>
</body>

</html>