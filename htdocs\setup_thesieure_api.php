<?php
// Include database configuration
require_once 'Controllers/database.php';

// Connect to database
try {
    $conn = connectPDO($config1);
    echo "Connected to database successfully.<br>";
    
    // Create card_api_config table if not exists
    $sql = "CREATE TABLE IF NOT EXISTS card_api_config (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        api_name VARCHAR(50) NOT NULL,
        api_url VARCHAR(255) NOT NULL,
        merchant_id VARCHAR(100),
        api_key VARCHAR(255),
        secret_key VARCHAR(255),
        callback_url VARCHAR(255),
        request_method VARCHAR(10) DEFAULT 'POST',
        is_active TINYINT(1) NOT NULL DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $conn->exec($sql);
    echo "Table card_api_config created successfully.<br>";
    
    // Check if TheSieuRe API config already exists
    $stmt = $conn->prepare("SELECT * FROM card_api_config WHERE api_name = 'THESIEURE'");
    $stmt->execute();
    $config = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // TheSieuRe API configuration
    $apiName = 'THESIEURE';
    $apiUrl = 'https://thesieure.com/chargingws/v2';
    $merchantId = '56020675516';
    $apiKey = '';  // Not used for TheSieuRe
    $secretKey = '7e0ef817372974de30481c7aa264fcd2';
    $callbackUrl = 'http://4.216.177.205/api/card_callback.php';
    $requestMethod = 'GET';
    
    if ($config) {
        // Update existing config
        $stmt = $conn->prepare("UPDATE card_api_config SET 
            api_url = :api_url,
            merchant_id = :merchant_id,
            api_key = :api_key,
            secret_key = :secret_key,
            callback_url = :callback_url,
            request_method = :request_method,
            is_active = 1
            WHERE api_name = :api_name");
        
        $stmt->bindParam(':api_url', $apiUrl, PDO::PARAM_STR);
        $stmt->bindParam(':merchant_id', $merchantId, PDO::PARAM_STR);
        $stmt->bindParam(':api_key', $apiKey, PDO::PARAM_STR);
        $stmt->bindParam(':secret_key', $secretKey, PDO::PARAM_STR);
        $stmt->bindParam(':callback_url', $callbackUrl, PDO::PARAM_STR);
        $stmt->bindParam(':request_method', $requestMethod, PDO::PARAM_STR);
        $stmt->bindParam(':api_name', $apiName, PDO::PARAM_STR);
        
        $stmt->execute();
        echo "TheSieuRe API configuration updated successfully.<br>";
    } else {
        // Insert new config
        $stmt = $conn->prepare("INSERT INTO card_api_config (
            api_name, api_url, merchant_id, api_key, secret_key, callback_url, request_method, is_active
        ) VALUES (
            :api_name, :api_url, :merchant_id, :api_key, :secret_key, :callback_url, :request_method, 1
        )");
        
        $stmt->bindParam(':api_name', $apiName, PDO::PARAM_STR);
        $stmt->bindParam(':api_url', $apiUrl, PDO::PARAM_STR);
        $stmt->bindParam(':merchant_id', $merchantId, PDO::PARAM_STR);
        $stmt->bindParam(':api_key', $apiKey, PDO::PARAM_STR);
        $stmt->bindParam(':secret_key', $secretKey, PDO::PARAM_STR);
        $stmt->bindParam(':callback_url', $callbackUrl, PDO::PARAM_STR);
        $stmt->bindParam(':request_method', $requestMethod, PDO::PARAM_STR);
        
        $stmt->execute();
        echo "TheSieuRe API configuration inserted successfully.<br>";
    }
    
    // Verify the configuration
    $stmt = $conn->prepare("SELECT * FROM card_api_config WHERE api_name = 'THESIEURE'");
    $stmt->execute();
    $config = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<pre>";
    print_r($config);
    echo "</pre>";
    
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage();
}
?>
