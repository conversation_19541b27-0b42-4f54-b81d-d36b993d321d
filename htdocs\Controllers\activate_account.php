<?php
// B<PERSON>t hiển thị lỗi để debug
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Hàm ghi log debug
if (!function_exists('debugLog')) {
    function debugLog($message) {
        // Sử dụng error_log thay vì ghi vào file riêng
        error_log("[ACTIVATE] " . $message);
    }
}

// Khởi động session nếu chưa được khởi động
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Kiểm tra quyền admin
if (!isset($_SESSION['is_admin']) || !$_SESSION['is_admin']) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Bạn không có quyền thực hiện hành động này.']);
    exit();
}

// Lấy dữ liệu từ request
$data = json_decode(file_get_contents('php://input'), true);

if (!isset($data['userId']) || !is_numeric($data['userId'])) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Dữ liệu không hợp lệ.']);
    exit();
}

// Kết nối đến cơ sở dữ liệu
require_once __DIR__ . '/database.php';

try {
    // Kết nối đến cơ sở dữ liệu account
    $conn = connectPDO($config1);
    
    // Kết nối đến cơ sở dữ liệu game_db
    $conn1 = connectPDO($config2);
    
    // Lấy thông tin người dùng
    $stmt = $conn->prepare("SELECT username FROM team_user WHERE id = :userId");
    $stmt->bindParam(':userId', $data['userId'], PDO::PARAM_INT);
    $stmt->execute();
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        header('Content-Type: application/json');
        echo json_encode(['success' => false, 'message' => 'Không tìm thấy người dùng.']);
        exit();
    }
    
    // Kiểm tra xem đã có bản ghi trong bảng 5h_active chưa
    $stmt = $conn1->prepare("SELECT * FROM 5h_active WHERE userID = :userId OR username = :username");
    $stmt->bindParam(':userId', $data['userId'], PDO::PARAM_INT);
    $stmt->bindParam(':username', $user['username'], PDO::PARAM_STR);
    $stmt->execute();
    $activation = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($activation) {
        // Cập nhật bản ghi hiện có
        $stmt = $conn1->prepare("UPDATE 5h_active SET time_end = -1 WHERE userID = :userId OR username = :username");
        $stmt->bindParam(':userId', $data['userId'], PDO::PARAM_INT);
        $stmt->bindParam(':username', $user['username'], PDO::PARAM_STR);
        $result = $stmt->execute();
    } else {
        // Thêm bản ghi mới
        $stmt = $conn1->prepare("INSERT INTO 5h_active (userID, username, time_end) VALUES (:userId, :username, -1)");
        $stmt->bindParam(':userId', $data['userId'], PDO::PARAM_INT);
        $stmt->bindParam(':username', $user['username'], PDO::PARAM_STR);
        $result = $stmt->execute();
    }
    
    if ($result) {
        debugLog("Account activated successfully for user ID: " . $data['userId']);
        header('Content-Type: application/json');
        echo json_encode(['success' => true, 'message' => 'Kích hoạt tài khoản thành công.']);
    } else {
        debugLog("Failed to activate account for user ID: " . $data['userId']);
        header('Content-Type: application/json');
        echo json_encode(['success' => false, 'message' => 'Kích hoạt tài khoản thất bại.']);
    }
} catch (PDOException $e) {
    debugLog("Error activating account: " . $e->getMessage());
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => 'Lỗi kết nối cơ sở dữ liệu: ' . $e->getMessage()]);
}
?>
