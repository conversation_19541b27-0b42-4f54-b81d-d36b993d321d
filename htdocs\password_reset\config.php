<?php

/**
 * Configuration file for password reset functionality
 */

// Database configuration
$db_config = [
    'host' => 'localhost',
    'username' => 'root',
    'password' => '',
    'database' => 'account'
];

// Email configuration
$email_config = [
    'host' => 'host212.vietnix.vn',
    'username' => '<EMAIL>',
    'password' => 'Tuan2025',
    'port' => 465,
    'encryption' => 'ssl',
    'from_email' => '<EMAIL>',
    'from_name' => 'KPAH Game',
    'reply_to' => '<EMAIL>',
    'reply_to_name' => 'KPAH Game Support'
];

// Application configuration
$app_config = [
    'site_url' => isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://',
    'token_expiry' => 24, // Token expiry in hours
    'min_password_length' => 6
];

// Complete the site URL
$app_config['site_url'] .= $_SERVER['HTTP_HOST'];

// If running on localhost, use the actual domain for emails
if ($_SERVER['HTTP_HOST'] === 'localhost' || strpos($_SERVER['HTTP_HOST'], 'localhost:') === 0) {
    $app_config['site_url'] = 'https://shopnickroblox.com';
}
