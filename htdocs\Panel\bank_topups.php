<?php
session_start();
require_once '../Controllers/database.php';
require_once '../Controllers/Account.php';

// Kiểm tra đăng nhập và quyền admin
if (!isset($_SESSION['team_user']) || !isset($_SESSION['is_admin']) || !$_SESSION['is_admin']) {
    header("Location: ../index.php?error=" . urlencode('Bạn không có quyền truy cập trang này.'));
    exit();
}

// Kết nối database
try {
    $conn = connectPDO($config1);
    $conn1 = connectPDO($config2);

    // Khởi tạo AccountManager
    $accountManager = new AccountManager($conn, $conn1);
} catch (PDOException $e) {
    die("Lỗi kết nối cơ sở dữ liệu: " . $e->getMessage());
}

$username = $_SESSION['team_user'];
$message = '';
$messageType = '';

// Xử lý duyệt giao dịch
if (isset($_POST['approve_transaction'])) {
    $transactionId = $_POST['transaction_id'] ?? 0;

    try {
        // Ghi log thông tin giao dịch
        error_log("Bắt đầu duyệt giao dịch ATM với ID: $transactionId");

        // Bắt đầu transaction
        $conn->beginTransaction();

        // Lấy thông tin giao dịch
        $stmt = $conn->prepare("SELECT bt.*, tu.username FROM bank_transactions bt LEFT JOIN team_user tu ON bt.user_id = tu.id WHERE bt.id = :id AND bt.status = 'pending'");
        $stmt->bindParam(':id', $transactionId, PDO::PARAM_INT);
        $stmt->execute();
        $transaction = $stmt->fetch(PDO::FETCH_ASSOC);

        // Ghi log thông tin giao dịch
        if ($transaction) {
            error_log("Thông tin giao dịch: ID={$transaction['id']}, UserID={$transaction['user_id']}, Username={$transaction['username']}, Amount={$transaction['amount']}, XuAmount={$transaction['xu_amount']}");
        } else {
            error_log("Không tìm thấy giao dịch với ID: $transactionId hoặc giao dịch đã được xử lý");
        }

        if (!$transaction) {
            throw new Exception('Không tìm thấy giao dịch hoặc giao dịch đã được xử lý.');
        }

        // Update transaction status
        $stmt = $conn->prepare("UPDATE bank_transactions SET status = 'approved', notes = CONCAT(IFNULL(notes, ''), ' | Duyệt bởi admin: ', :adminUsername, ' vào lúc: ', NOW()) WHERE id = :id");
        $stmt->bindParam(':id', $transactionId, PDO::PARAM_INT);
        $stmt->bindParam(':adminUsername', $username, PDO::PARAM_STR);
        $stmt->execute();

        // Kiểm tra xem người dùng có tồn tại trong bảng team_user không
        $stmt = $conn->prepare("SELECT * FROM team_user WHERE id = :userId");
        $stmt->bindParam(':userId', $transaction['user_id'], PDO::PARAM_INT);
        $stmt->execute();
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$user) {
            // Ghi log lỗi
            error_log("Không tìm thấy người dùng với ID: " . $transaction['user_id']);

            // Lấy thông tin người dùng từ bảng bank_transactions
            $username = $transaction['username'];

            // Thêm người dùng vào bảng team_user nếu chưa tồn tại
            try {
                $stmt = $conn->prepare("INSERT INTO team_user (id, username, password) VALUES (:id, :username, :password)");
                $stmt->bindParam(':id', $transaction['user_id'], PDO::PARAM_INT);
                $stmt->bindParam(':username', $username, PDO::PARAM_STR);
                $password = password_hash('default_password', PASSWORD_DEFAULT);
                $stmt->bindParam(':password', $password, PDO::PARAM_STR);
                $stmt->execute();

                error_log("Đã thêm người dùng mới với ID: " . $transaction['user_id'] . ", Username: " . $username);
            } catch (Exception $e) {
                error_log("Lỗi khi thêm người dùng: " . $e->getMessage());
                throw new Exception("Không thể thêm người dùng: " . $e->getMessage());
            }
        }

        // Update user balance
        $stmt = $conn->prepare("SELECT * FROM user_balance WHERE user_id = :userId");
        $stmt->bindParam(':userId', $transaction['user_id'], PDO::PARAM_INT);
        $stmt->execute();
        $balance = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($balance) {
            // Update existing balance
            $newBalance = $balance['balance'] + $transaction['xu_amount'];
            error_log("Cập nhật số dư hiện tại: UserID={$transaction['user_id']}, OldBalance={$balance['balance']}, XuAmount={$transaction['xu_amount']}, NewBalance=$newBalance");

            $stmt = $conn->prepare("UPDATE user_balance SET balance = :balance, last_updated = NOW() WHERE user_id = :userId");
            $stmt->bindParam(':balance', $newBalance, PDO::PARAM_INT);
            $stmt->bindParam(':userId', $transaction['user_id'], PDO::PARAM_INT);
            $stmt->execute();

            error_log("Cập nhật số dư thành công: UserID={$transaction['user_id']}, NewBalance=$newBalance");
        } else {
            // Create new balance record
            error_log("Tạo mới số dư: UserID={$transaction['user_id']}, XuAmount={$transaction['xu_amount']}");

            $stmt = $conn->prepare("INSERT INTO user_balance (user_id, balance, last_updated) VALUES (:userId, :balance, NOW())");
            $stmt->bindParam(':userId', $transaction['user_id'], PDO::PARAM_INT);
            $stmt->bindParam(':balance', $transaction['xu_amount'], PDO::PARAM_INT);
            $stmt->execute();

            error_log("Tạo mới số dư thành công: UserID={$transaction['user_id']}, Balance={$transaction['xu_amount']}");
        }

        // Commit transaction
        $conn->commit();

        error_log("Duyệt giao dịch thành công: ID={$transaction['id']}, UserID={$transaction['user_id']}, Username={$transaction['username']}, XuAmount={$transaction['xu_amount']}");

        $message = 'Giao dịch đã được duyệt thành công.';
        $messageType = 'success';
    } catch (Exception $e) {
        // Rollback transaction on error
        if ($conn->inTransaction()) {
            $conn->rollBack();
        }

        // Ghi log lỗi
        error_log("Lỗi khi duyệt giao dịch: " . $e->getMessage());
        error_log("Stack trace: " . $e->getTraceAsString());

        $message = 'Lỗi khi duyệt giao dịch: ' . $e->getMessage();
        $messageType = 'danger';
    }
}

// Xử lý từ chối giao dịch
if (isset($_POST['reject_transaction'])) {
    $transactionId = $_POST['transaction_id'] ?? 0;
    $rejectReason = $_POST['reject_reason'] ?? '';

    try {
        // Update transaction status
        $stmt = $conn->prepare("UPDATE bank_transactions SET status = 'rejected', notes = CONCAT(IFNULL(notes, ''), ' | Từ chối bởi admin: ', :adminUsername, ' vào lúc: ', NOW(), ' | Lý do: ', :rejectReason) WHERE id = :id AND status = 'pending'");
        $stmt->bindParam(':id', $transactionId, PDO::PARAM_INT);
        $stmt->bindParam(':adminUsername', $username, PDO::PARAM_STR);
        $stmt->bindParam(':rejectReason', $rejectReason, PDO::PARAM_STR);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            $message = 'Giao dịch đã được từ chối.';
            $messageType = 'success';
        } else {
            $message = 'Không tìm thấy giao dịch hoặc giao dịch đã được xử lý.';
            $messageType = 'danger';
        }
    } catch (PDOException $e) {
        $message = 'Lỗi khi từ chối giao dịch: ' . $e->getMessage();
        $messageType = 'danger';
    }
}

// Lấy danh sách giao dịch
$searchTerm = trim($_GET['search'] ?? '');
$statusFilter = $_GET['status'] ?? '';
$page = (int)($_GET['page'] ?? 1);
$perPage = 10;
$offset = ($page - 1) * $perPage;

try {
    // Xây dựng câu truy vấn
    $query = "SELECT bt.*, tu.username, ba.bank_name, ba.account_number
              FROM bank_transactions bt
              JOIN team_user tu ON bt.user_id = tu.id
              JOIN bank_accounts ba ON bt.bank_account_id = ba.id
              WHERE 1=1";
    $countQuery = "SELECT COUNT(*) FROM bank_transactions bt
                  JOIN team_user tu ON bt.user_id = tu.id
                  JOIN bank_accounts ba ON bt.bank_account_id = ba.id
                  WHERE 1=1";
    $params = [];

    // Thêm điều kiện tìm kiếm
    if (!empty($searchTerm)) {
        $query .= " AND (tu.username LIKE :searchTerm OR bt.transaction_code LIKE :searchTerm)";
        $countQuery .= " AND (tu.username LIKE :searchTerm OR bt.transaction_code LIKE :searchTerm)";
        $params[':searchTerm'] = "%$searchTerm%";
    }

    // Thêm điều kiện lọc trạng thái
    if (!empty($statusFilter)) {
        $query .= " AND bt.status = :status";
        $countQuery .= " AND bt.status = :status";
        $params[':status'] = $statusFilter;
    }

    // Sắp xếp và phân trang
    $query .= " ORDER BY bt.transaction_date DESC LIMIT :offset, :perPage";

    // Đếm tổng số giao dịch
    $countStmt = $conn->prepare($countQuery);
    foreach ($params as $key => $value) {
        $countStmt->bindValue($key, $value);
    }
    $countStmt->execute();
    $totalTransactions = $countStmt->fetchColumn();

    // Lấy danh sách giao dịch
    $stmt = $conn->prepare($query);
    foreach ($params as $key => $value) {
        $stmt->bindValue($key, $value);
    }
    $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    $stmt->bindValue(':perPage', $perPage, PDO::PARAM_INT);
    $stmt->execute();
    $transactions = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Tính toán phân trang
    $totalPages = ceil($totalTransactions / $perPage);
} catch (PDOException $e) {
    $message = 'Lỗi khi lấy danh sách giao dịch: ' . $e->getMessage();
    $messageType = 'danger';
    $transactions = [];
    $totalPages = 0;
}
?>

<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Quản lý nạp tiền qua ATM - KPAH</title>
    <link href="../assets/homepage/css/bootstrap.min.css" rel="stylesheet" type="text/css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <script src="../assets/homepage/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/homepage/js/jquery-1.9.1.min.js"></script>
    <style>
        body {
            background-color: #1a1a1a;
            color: #f9d686;
            font-family: Arial, sans-serif;
        }

        .admin-container {
            padding: 20px;
        }

        .admin-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #f9d686;
        }

        .admin-title {
            color: #f9d686;
            margin: 0;
        }

        .admin-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            color: #f9d686;
        }

        .admin-table th,
        .admin-table td {
            padding: 10px;
            border: 1px solid #f9d686;
        }

        .admin-table th {
            background-color: #2a2a2a;
        }

        .admin-table tr:nth-child(even) {
            background-color: #2a2a2a;
        }

        .admin-table tr:hover {
            background-color: #3a3a3a;
        }

        .btn-custom {
            background-color: #f9d686;
            color: #1a1a1a;
            border: none;
        }

        .btn-custom:hover {
            background-color: #e0c070;
            color: #1a1a1a;
        }

        .status-pending {
            color: #ffc107;
        }

        .status-approved {
            color: #28a745;
        }

        .status-rejected {
            color: #dc3545;
        }

        .pagination {
            margin-top: 20px;
        }

        .page-link {
            background-color: #2a2a2a;
            color: #f9d686;
            border-color: #f9d686;
        }

        .page-link:hover {
            background-color: #3a3a3a;
            color: #f9d686;
        }

        .page-item.active .page-link {
            background-color: #f9d686;
            color: #1a1a1a;
            border-color: #f9d686;
        }

        .admin-menu {
            background-color: #2a2a2a;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid #f9d686;
        }

        .admin-menu ul {
            list-style: none;
            padding: 0;
            margin: 0;
            display: flex;
            flex-wrap: wrap;
        }

        .admin-menu li {
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .admin-menu a {
            display: block;
            padding: 8px 15px;
            background-color: #333;
            color: #f9d686;
            text-decoration: none;
            border-radius: 3px;
            border: 1px solid rgba(249, 214, 134, 0.3);
            transition: all 0.3s ease;
        }

        .admin-menu a:hover,
        .admin-menu a.active {
            background-color: #f9d686;
            color: #000;
        }

        .admin-menu a i {
            margin-right: 5px;
        }

        /* Fix cho modal trên thiết bị di động */
        .modal {
            z-index: 1060 !important;
        }

        .modal-backdrop {
            z-index: 1050 !important;
            opacity: 0.7 !important;
        }

        .modal-dialog {
            margin: 10px auto !important;
            max-width: 95% !important;
            z-index: 1070 !important;
        }

        /* Đảm bảo modal hiển thị đúng trên thiết bị di động */
        @media (max-width: 768px) {
            .modal-dialog {
                margin: 0.5rem auto !important;
                max-width: 95% !important;
                transform: none !important;
            }

            .modal-content {
                border: none !important;
                border-radius: 8px !important;
            }

            /* Đảm bảo các nút có kích thước đủ lớn để dễ nhấn */
            .modal-footer .btn {
                padding: 10px 15px;
                font-size: 16px;
                margin: 5px;
                min-height: 44px;
                min-width: 80px;
            }

            /* Tăng kích thước của các phần tử tương tác */
            .modal-body input,
            .modal-body select,
            .modal-body textarea,
            .modal-body .form-control {
                font-size: 16px !important;
                padding: 10px !important;
                height: auto !important;
            }
        }

        /* Sửa lỗi touch events */
        .modal-dialog,
        .modal-content,
        .modal-body,
        .modal-footer {
            touch-action: auto !important;
            -webkit-overflow-scrolling: touch !important;
        }

        /* Fix cho body khi modal mở */
        body.modal-open-fix {
            overflow: hidden;
            position: fixed;
            width: 100%;
            height: 100%;
        }
    </style>
</head>

<body>
    <div class="container admin-container">
        <div class="admin-header">
            <h1 class="admin-title">KPAH - Quản lý nạp tiền qua ATM</h1>
            <div class="admin-user">
                Xin chào, <strong><?php echo htmlspecialchars($username); ?></strong> |
                <a href="../Views/Account.php" class="text-light">Tài khoản</a> |
                <a href="../handle/logout.php" class="text-light">Đăng xuất</a>
            </div>
        </div>

        <div class="admin-menu">
            <ul>
                <li><a href="index.php"><i class="fas fa-tachometer-alt"></i> Tổng quan</a></li>
                <li><a href="users.php"><i class="fas fa-users"></i> Quản lý người dùng</a></li>
                <li><a href="bank_topups.php" class="active"><i class="fas fa-university"></i> Quản lý nạp ATM</a></li>
                <li><a href="bank_accounts.php"><i class="fas fa-credit-card"></i> Quản lý tài khoản ngân hàng</a></li>
                <li><a href="activations.php"><i class="fas fa-check-circle"></i> Kích hoạt tài khoản</a></li>
                <li><a href="giftcodes.php"><i class="fas fa-gift"></i> Quản lý Giftcode</a></li>
                <li><a href="api_management.php"><i class="fas fa-plug"></i> Quản lý API</a></li>
                <li><a href="password_decoder.php"><i class="fas fa-key"></i> Giải mã mật khẩu</a></li>
                <li><a href="site_settings.php"><i class="fas fa-cogs"></i> Cấu hình trang web</a></li>

                <li><a href="../index.php"><i class="fas fa-home"></i> Về trang chủ</a></li>
            </ul>
        </div>

        <?php if (!empty($message)): ?>
            <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                <?php echo $message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <div class="card bg-dark text-light mb-4">
            <div class="card-header">
                <h5 class="mb-0">Tìm kiếm giao dịch</h5>
            </div>
            <div class="card-body">
                <form method="get" action="" class="row g-3">
                    <div class="col-md-4">
                        <input type="text" class="form-control" name="search"
                            placeholder="Tìm theo tên người dùng hoặc mã giao dịch"
                            value="<?php echo htmlspecialchars($searchTerm); ?>">
                    </div>
                    <div class="col-md-3">
                        <select class="form-control" name="status">
                            <option value="">-- Tất cả trạng thái --</option>
                            <option value="pending" <?php echo $statusFilter === 'pending' ? 'selected' : ''; ?>>Đang xử
                                lý</option>
                            <option value="approved" <?php echo $statusFilter === 'approved' ? 'selected' : ''; ?>>Đã
                                duyệt</option>
                            <option value="rejected" <?php echo $statusFilter === 'rejected' ? 'selected' : ''; ?>>Đã từ
                                chối</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-custom w-100">
                            <i class="fas fa-search"></i> Tìm kiếm
                        </button>
                    </div>
                    <div class="col-md-2">
                        <a href="bank_topups.php" class="btn btn-outline-secondary w-100">
                            <i class="fas fa-sync-alt"></i> Làm mới
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <div class="card bg-dark text-light">
            <div class="card-header">
                <h5 class="mb-0">Danh sách giao dịch nạp tiền qua ATM</h5>
            </div>
            <div class="card-body">
                <?php if (empty($transactions)): ?>
                    <p>Không có giao dịch nào.</p>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="admin-table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Người dùng</th>
                                    <th>Ngân hàng</th>
                                    <th>Số tiền</th>
                                    <th>Xu</th>
                                    <th>Mã giao dịch</th>
                                    <th>STK người dùng</th>
                                    <th>Ngày giao dịch</th>
                                    <th>Trạng thái</th>
                                    <th>Thao tác</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($transactions as $transaction): ?>
                                    <tr>
                                        <td><?php echo $transaction['id']; ?></td>
                                        <td><?php echo htmlspecialchars($transaction['username']); ?></td>
                                        <td><?php echo htmlspecialchars($transaction['bank_name']); ?></td>
                                        <td><?php echo number_format($transaction['amount'], 0, ',', '.'); ?> VNĐ</td>
                                        <td><?php echo number_format($transaction['xu_amount'], 0, ',', '.'); ?></td>
                                        <td><?php echo htmlspecialchars($transaction['transaction_code'] ?: 'N/A'); ?></td>
                                        <td><?php echo htmlspecialchars($transaction['user_account'] ?: 'N/A'); ?></td>
                                        <td><?php echo date('d/m/Y H:i', strtotime($transaction['transaction_date'])); ?></td>
                                        <td>
                                            <?php
                                            switch ($transaction['status']) {
                                                case 'pending':
                                                    echo '<span class="status-pending">Đang xử lý</span>';
                                                    break;
                                                case 'approved':
                                                    echo '<span class="status-approved">Đã duyệt</span>';
                                                    break;
                                                case 'rejected':
                                                    echo '<span class="status-rejected">Đã từ chối</span>';
                                                    break;
                                                default:
                                                    echo htmlspecialchars($transaction['status']);
                                            }
                                            ?>
                                        </td>
                                        <td>
                                            <?php if ($transaction['status'] === 'pending'): ?>
                                                <button type="button" class="btn btn-sm btn-success" data-bs-toggle="modal"
                                                    data-bs-target="#approveModal<?php echo $transaction['id']; ?>"
                                                    data-transaction-id="<?php echo $transaction['id']; ?>"
                                                    data-username="<?php echo htmlspecialchars($transaction['username']); ?>"
                                                    data-amount="<?php echo number_format($transaction['amount'], 0, ',', '.'); ?> VNĐ"
                                                    data-xu="<?php echo number_format($transaction['xu_amount'], 0, ',', '.'); ?>">
                                                    <i class="fas fa-check"></i> Duyệt
                                                </button>
                                                <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal"
                                                    data-bs-target="#rejectModal<?php echo $transaction['id']; ?>"
                                                    data-transaction-id="<?php echo $transaction['id']; ?>"
                                                    data-username="<?php echo htmlspecialchars($transaction['username']); ?>">
                                                    <i class="fas fa-times"></i> Từ chối
                                                </button>
                                            <?php else: ?>
                                                <button type="button" class="btn btn-sm btn-info" data-bs-toggle="modal"
                                                    data-bs-target="#detailsModal<?php echo $transaction['id']; ?>"
                                                    data-transaction-id="<?php echo $transaction['id']; ?>"
                                                    data-username="<?php echo htmlspecialchars($transaction['username']); ?>"
                                                    data-amount="<?php echo number_format($transaction['amount'], 0, ',', '.'); ?> VNĐ"
                                                    data-xu="<?php echo number_format($transaction['xu_amount'], 0, ',', '.'); ?>"
                                                    data-code="<?php echo htmlspecialchars($transaction['transaction_code'] ?: 'N/A'); ?>"
                                                    data-status="<?php echo htmlspecialchars($transaction['status']); ?>"
                                                    data-date="<?php echo date('d/m/Y H:i', strtotime($transaction['transaction_date'])); ?>">
                                                    <i class="fas fa-info-circle"></i> Chi tiết
                                                </button>
                                            <?php endif; ?>
                                        </td>
                                    </tr>

                                    <!-- Modal Approve -->
                                    <div class="modal fade" id="approveModal<?php echo $transaction['id']; ?>" tabindex="-1"
                                        aria-labelledby="approveModalLabel<?php echo $transaction['id']; ?>" aria-hidden="true">
                                        <div class="modal-dialog modal-dialog-centered">
                                            <div class="modal-content bg-dark text-light">
                                                <div class="modal-header">
                                                    <h5 class="modal-title"
                                                        id="approveModalLabel<?php echo $transaction['id']; ?>">Xác nhận duyệt
                                                        giao dịch</h5>
                                                    <button type="button" class="btn-close btn-close-white"
                                                        data-bs-dismiss="modal" aria-label="Close"></button>
                                                </div>
                                                <div class="modal-body">
                                                    <p>Bạn có chắc chắn muốn duyệt giao dịch này?</p>
                                                    <p><strong>Người dùng:</strong>
                                                        <?php echo htmlspecialchars($transaction['username']); ?></p>
                                                    <p><strong>Ngân hàng:</strong>
                                                        <?php echo htmlspecialchars($transaction['bank_name']); ?></p>
                                                    <p><strong>Số tài khoản ngân hàng:</strong>
                                                        <?php echo htmlspecialchars($transaction['account_number']); ?></p>
                                                    <p><strong>Số tài khoản người dùng:</strong>
                                                        <?php echo htmlspecialchars($transaction['user_account'] ?: 'N/A'); ?>
                                                    </p>
                                                    <p><strong>Số tiền:</strong>
                                                        <?php echo number_format($transaction['amount'], 0, ',', '.'); ?> VNĐ
                                                    </p>
                                                    <p><strong>Xu:</strong>
                                                        <?php echo number_format($transaction['xu_amount'], 0, ',', '.'); ?></p>
                                                    <p><strong>Mã giao dịch:</strong>
                                                        <?php echo htmlspecialchars($transaction['transaction_code']); ?></p>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary btn-lg"
                                                        data-bs-dismiss="modal">Hủy</button>
                                                    <form action="" method="post" class="d-inline">
                                                        <input type="hidden" name="transaction_id"
                                                            value="<?php echo $transaction['id']; ?>">
                                                        <button type="submit" name="approve_transaction"
                                                            class="btn btn-success btn-lg approve-btn">
                                                            <i class="fas fa-check"></i> Duyệt
                                                        </button>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Modal Reject -->
                                    <div class="modal fade" id="rejectModal<?php echo $transaction['id']; ?>" tabindex="-1"
                                        aria-labelledby="rejectModalLabel<?php echo $transaction['id']; ?>" aria-hidden="true">
                                        <div class="modal-dialog modal-dialog-centered">
                                            <div class="modal-content bg-dark text-light">
                                                <div class="modal-header">
                                                    <h5 class="modal-title"
                                                        id="rejectModalLabel<?php echo $transaction['id']; ?>">Lý do từ chối
                                                        giao dịch</h5>
                                                    <button type="button" class="btn-close btn-close-white"
                                                        data-bs-dismiss="modal" aria-label="Close"></button>
                                                </div>
                                                <div class="modal-body">
                                                    <form action="" method="post" class="reject-form">
                                                        <input type="hidden" name="transaction_id"
                                                            value="<?php echo $transaction['id']; ?>">
                                                        <div class="mb-3">
                                                            <label for="reject_reason<?php echo $transaction['id']; ?>"
                                                                class="form-label">Lý do từ chối</label>
                                                            <textarea class="form-control"
                                                                id="reject_reason<?php echo $transaction['id']; ?>"
                                                                name="reject_reason" rows="3" required></textarea>
                                                        </div>
                                                        <div class="text-center">
                                                            <button type="button" class="btn btn-secondary btn-lg me-2"
                                                                data-bs-dismiss="modal">
                                                                <i class="fas fa-times"></i> Hủy
                                                            </button>
                                                            <button type="submit" name="reject_transaction"
                                                                class="btn btn-danger btn-lg reject-btn">
                                                                <i class="fas fa-times"></i> Từ chối
                                                            </button>
                                                        </div>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Modal Details -->
                                    <div class="modal fade" id="detailsModal<?php echo $transaction['id']; ?>" tabindex="-1"
                                        aria-labelledby="detailsModalLabel<?php echo $transaction['id']; ?>" aria-hidden="true">
                                        <div class="modal-dialog modal-dialog-centered">
                                            <div class="modal-content bg-dark text-light">
                                                <div class="modal-header">
                                                    <h5 class="modal-title"
                                                        id="detailsModalLabel<?php echo $transaction['id']; ?>">Chi tiết giao
                                                        dịch</h5>
                                                    <button type="button" class="btn-close btn-close-white"
                                                        data-bs-dismiss="modal" aria-label="Close"></button>
                                                </div>
                                                <div class="modal-body">
                                                    <p><strong>Người dùng:</strong>
                                                        <?php echo htmlspecialchars($transaction['username']); ?></p>
                                                    <p><strong>Ngân hàng:</strong>
                                                        <?php echo htmlspecialchars($transaction['bank_name']); ?></p>
                                                    <p><strong>Số tài khoản ngân hàng:</strong>
                                                        <?php echo htmlspecialchars($transaction['account_number']); ?></p>
                                                    <p><strong>Số tài khoản người dùng:</strong>
                                                        <?php echo htmlspecialchars($transaction['user_account'] ?: 'N/A'); ?>
                                                    </p>
                                                    <p><strong>Số tiền:</strong>
                                                        <?php echo number_format($transaction['amount'], 0, ',', '.'); ?> VNĐ
                                                    </p>
                                                    <p><strong>Xu:</strong>
                                                        <?php echo number_format($transaction['xu_amount'], 0, ',', '.'); ?></p>
                                                    <p><strong>Mã giao dịch:</strong>
                                                        <?php echo htmlspecialchars($transaction['transaction_code']); ?></p>
                                                    <p><strong>Ngày giao dịch:</strong>
                                                        <?php echo date('d/m/Y H:i', strtotime($transaction['transaction_date'])); ?>
                                                    </p>
                                                    <p><strong>Trạng thái:</strong>
                                                        <?php echo htmlspecialchars($transaction['status']); ?></p>
                                                    <?php if (!empty($transaction['notes'])): ?>
                                                        <p><strong>Ghi chú:</strong>
                                                            <?php echo nl2br(htmlspecialchars($transaction['notes'])); ?></p>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary btn-lg"
                                                        data-bs-dismiss="modal">
                                                        <i class="fas fa-times"></i> Đóng
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <div class="pagination">
            <?php if ($totalPages > 1): ?>
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center">
                        <?php if ($page > 1): ?>
                            <li class="page-item">
                                <a class="page-link"
                                    href="?search=<?php echo urlencode($searchTerm); ?>&status=<?php echo urlencode($statusFilter); ?>&page=<?php echo $page - 1; ?>"
                                    aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                        <?php endif; ?>
                        <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                            <li class="page-item<?php echo $i === $page ? ' active' : ''; ?>">
                                <a class="page-link"
                                    href="?search=<?php echo urlencode($searchTerm); ?>&status=<?php echo urlencode($statusFilter); ?>&page=<?php echo $i; ?>"><?php echo $i; ?></a>
                            </li>
                        <?php endfor; ?>
                        <?php if ($page < $totalPages): ?>
                            <li class="page-item">
                                <a class="page-link"
                                    href="?search=<?php echo urlencode($searchTerm); ?>&status=<?php echo urlencode($statusFilter); ?>&page=<?php echo $page + 1; ?>"
                                    aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                        <?php endif; ?>
                    </ul>
                </nav>
            <?php endif; ?>
        </div>
    </div>
    <!-- Script để sửa lỗi modal trên thiết bị di động -->
    <script src="../Panel/mobile-fix.js"></script>
    <script>
        $(document).ready(function() {
            // Khở tạo Bootstrap tooltips
            $('[data-bs-toggle="tooltip"]').tooltip();

            // Xử lý sự kiện submit form
            $('form').on('submit', function() {
                // Đảm bảo không có phần tử nào đang focus
                if (document.activeElement) {
                    document.activeElement.blur();
                }
            });
        });
    </script>
</body>

</html>