<?php
// Start the session at the beginning of the file
session_start();

// Check if user is logged in, redirect to login page if not
if (!isset($_SESSION['team_user']) || empty($_SESSION['team_user'])) {
    header("Location: ../index.php?error=" . urlencode('Vui lòng đăng nhập để truy cập trang này.'));
    exit();
}

// Include database configuration
require_once '../Controllers/database.php';

// Create AccountManager class if it doesn't exist
if (!class_exists('AccountManager')) {
    require_once '../Controllers/Account.php';
}

// Connect to databases
try {
    // Connect to account database
    $conn = connectPDO($config1);

    // Connect to game_db database
    $conn1 = connectPDO($config2);

    // Create necessary tables if they don't exist
    createTables($conn, $conn1);
} catch (PDOException $e) {
    die("Lỗi kết nối cơ sở dữ liệu: " . $e->getMessage());
}

// Thêm hàm ghi log nếu chưa có
if (!function_exists('debugLog')) {
    function debugLog($message)
    {
        // Sử dụng error_log thay vì ghi vào file riêng
        error_log("[ACCOUNT_VIEW] " . $message);
    }
}

// Ghi log thông tin session
debugLog("Session data: " . json_encode($_SESSION));

// Get user information
if (isset($_SESSION['id'])) {
    $userId = $_SESSION['id'];
    debugLog("User ID from session: $userId");
} else {
    // Nếu không có id trong session, thử lấy từ database dựa vào username
    if (isset($_SESSION['team_user'])) {
        $stmt = $conn->prepare("SELECT id FROM team_user WHERE username = :username");
        $stmt->bindParam(':username', $_SESSION['team_user'], PDO::PARAM_STR);
        $stmt->execute();
        $userResult = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($userResult) {
            $userId = $userResult['id'];
            // Lưu user_id vào session để sử dụng sau này
            $_SESSION['id'] = $userId;
            debugLog("User ID retrieved from database: $userId");
        } else {
            debugLog("User not found in database for username: {$_SESSION['team_user']}");
            header("Location: ../index.php?error=" . urlencode('Không tìm thấy thông tin người dùng. Vui lòng đăng nhập lại.'));
            exit();
        }
    } else {
        debugLog("No user information in session");
        header("Location: ../index.php?error=" . urlencode('Vui lòng đăng nhập để truy cập trang này.'));
        exit();
    }
}

$username = $_SESSION['team_user'];
$isAdmin = isset($_SESSION['is_admin']) && $_SESSION['is_admin'];

debugLog("User information: ID=$userId, Username=$username, IsAdmin=$isAdmin");

// Initialize AccountManager
$accountManager = new AccountManager($conn, $conn1);
$userDetails = $accountManager->getUserDetails($userId);

// Process card top-up form submission
$topupMessage = '';
$topupSuccess = false;
$emailMessage = '';
$emailSuccess = false;
$activationMessage = '';
$activationSuccess = false;
$passwordMessage = '';
$passwordSuccess = false;

// Xử lý cập nhật email
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['submit_email'])) {
    $newEmail = $_POST['email'] ?? '';

    if (empty($newEmail)) {
        $emailMessage = 'Vui lòng nhập địa chỉ email.';
    } elseif (!filter_var($newEmail, FILTER_VALIDATE_EMAIL)) {
        $emailMessage = 'Địa chỉ email không hợp lệ.';
    } else {
        // Cập nhật email
        $result = $accountManager->updateEmail($userId, $newEmail);
        if ($result) {
            $emailSuccess = true;
            $emailMessage = 'Cập nhật email thành công.';
            // Cập nhật thông tin người dùng
            $userDetails = $accountManager->getUserDetails($userId);
        } else {
            $emailMessage = 'Có lỗi xảy ra khi cập nhật email. Vui lòng thử lại sau.';
        }
    }
}

// Xử lý đổi mật khẩu
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['submit_password'])) {
    $currentPassword = $_POST['current_password'] ?? '';
    $newPassword = $_POST['new_password'] ?? '';
    $confirmPassword = $_POST['confirm_password'] ?? '';

    debugLog("Password change request received for user ID: $userId");

    if (empty($currentPassword) || empty($newPassword) || empty($confirmPassword)) {
        $passwordMessage = 'Vui lòng điền đầy đủ thông tin mật khẩu.';
        debugLog("Password change validation failed: Empty fields");
    } elseif ($newPassword !== $confirmPassword) {
        $passwordMessage = 'Mật khẩu mới và mật khẩu xác nhận không khớp.';
        debugLog("Password change validation failed: Passwords don't match");
    } elseif (strlen($newPassword) < 6) {
        $passwordMessage = 'Mật khẩu mới phải có ít nhất 6 ký tự.';
        debugLog("Password change validation failed: Password too short");
    } else {
        // Gọi hàm đổi mật khẩu
        $result = $accountManager->changePassword($userId, $currentPassword, $newPassword);
        $passwordSuccess = $result['success'];
        $passwordMessage = $result['message'];
        debugLog("Password change result: " . ($passwordSuccess ? "Success" : "Failed") . " - " . $passwordMessage);
    }
}

// Xử lý nạp thẻ
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['submit_card'])) {
    $cardType = isset($_POST['card_type']) ? trim($_POST['card_type']) : '';
    $cardNumber = isset($_POST['card_number']) ? trim($_POST['card_number']) : '';
    $cardSerial = isset($_POST['card_serial']) ? trim($_POST['card_serial']) : '';
    $cardAmount = isset($_POST['card_amount']) ? (float)$_POST['card_amount'] : 0;

    debugLog("Card topup form submitted: Type=$cardType, Amount=$cardAmount");

    if (empty($cardType) || empty($cardNumber) || empty($cardSerial) || empty($cardAmount)) {
        $topupMessage = 'Vui lòng điền đầy đủ thông tin thẻ.';
        debugLog("Card topup validation failed: Empty fields");
    } else {
        // Kiểm tra định dạng số thẻ và serial
        if (strlen($cardNumber) < 8 || strlen($cardSerial) < 8) {
            $topupMessage = 'Mã thẻ hoặc số serial không hợp lệ. Vui lòng kiểm tra lại.';
            debugLog("Card topup validation failed: Invalid card number or serial");
        } else {
            // Process card top-up
            $result = $accountManager->processCardTopup($userId, $cardType, $cardNumber, $cardSerial, $cardAmount);
            $topupSuccess = $result['success'];
            $topupMessage = $result['message'];

            debugLog("Card topup result: " . ($topupSuccess ? "Success" : "Failed") . " - " . $topupMessage);

            if ($topupSuccess) {
                // Cập nhật thông tin người dùng và số dư
                $userDetails = $accountManager->getUserDetails($userId);
                $balance = $accountManager->getUserBalance($userId);
            }
        }
    }
}

// Xử lý kích hoạt tài khoản
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['submit_activation'])) {
    debugLog("Account activation request received for user ID: $userId");

    // Gọi hàm kích hoạt tài khoản với chi phí 20 xu
    $result = $accountManager->activateAccountWithXu($userId);

    $activationSuccess = $result['success'];
    $activationMessage = $result['message'];

    // Cập nhật thông tin người dùng sau khi kích hoạt
    if ($activationSuccess) {
        $userDetails = $accountManager->getUserDetails($userId);
        $balance = $accountManager->getUserBalance($userId);
    }

    debugLog("Activation result: " . ($activationSuccess ? "Success" : "Failed") . " - " . $activationMessage);
}

// Lấy bảng giá quy đổi xu
$xuRates = $accountManager->getXuRates();

// Get transaction history
$transactions = $accountManager->getTransactionHistory($userId);

// Get card transaction history
$cardTransactions = $accountManager->getCardTransactionHistory($userId);

// Get user balance
$balance = $accountManager->getUserBalance($userId);

// Process bank top-up form submission
$bankTopupMessage = '';
$bankTopupSuccess = false;
if (isset($_POST['submit_bank'])) {
    debugLog("Bank top-up form submitted");
    debugLog("POST data: " . json_encode($_POST));

    $bankAccountId = $_POST['bank_account_id'] ?? '';
    $bankAmount = $_POST['bank_amount'] ?? '';
    $transactionCode = $_POST['transaction_code'] ?? '';
    $userAccount = $_POST['user_account'] ?? '';

    debugLog("Form values: bankAccountId=$bankAccountId, bankAmount=$bankAmount, transactionCode=$transactionCode, userAccount=$userAccount");

    if (empty($bankAccountId) || empty($bankAmount) || empty($userAccount)) {
        $bankTopupMessage = 'Vui lòng chọn tài khoản ngân hàng, số tiền nạp và nhập số tài khoản của bạn.';
        debugLog("Validation failed: Empty bank account ID, amount or user account");
    } else {
        try {
            // Sử dụng user_id đã được lấy từ session ở đầu file
            // $userId đã được định nghĩa ở dòng 34: $userId = $_SESSION['id'];

            if (!$userId) {
                debugLog("User ID not found in session. Session data: " . json_encode($_SESSION));
                throw new Exception("Không thể xác định ID người dùng. Vui lòng đăng nhập lại.");
            }

            debugLog("Processing bank topup with user ID: $userId");

            // Kiểm tra xem bankAccountId có phải là số không
            if (!is_numeric($bankAccountId)) {
                debugLog("Invalid bank account ID: $bankAccountId");
                throw new Exception("ID tài khoản ngân hàng không hợp lệ.");
            }

            // Kiểm tra xem bankAmount có phải là số không
            if (!is_numeric($bankAmount)) {
                debugLog("Invalid amount: $bankAmount");
                throw new Exception("Số tiền nạp không hợp lệ.");
            }

            // Process bank top-up
            debugLog("Calling processBankTopup with parameters: userId=$userId, bankAccountId=$bankAccountId, bankAmount=$bankAmount, transactionCode=$transactionCode, userAccount=$userAccount");
            $result = $accountManager->processBankTopup($userId, $bankAccountId, $bankAmount, $transactionCode, $userAccount);
            debugLog("processBankTopup result: " . ($result ? 'Success' : 'Failed'));

            if ($result) {
                $bankTopupSuccess = true;
                $bankTopupMessage = 'Gửi yêu cầu nạp tiền qua ATM thành công. Vui lòng chờ xác nhận từ quản trị viên.';
                debugLog("Bank topup successful");
            } else {
                $bankTopupMessage = 'Có lỗi xảy ra khi xử lý yêu cầu nạp tiền qua ATM. Vui lòng kiểm tra lại thông tin và thử lại sau.';
                debugLog("Bank topup failed in Views/Account.php. User ID: $userId, Bank: $bankAccountId, Amount: $bankAmount");
            }
        } catch (Exception $e) {
            debugLog("Exception in bank topup: " . $e->getMessage() . "\nTrace: " . $e->getTraceAsString());
            $bankTopupMessage = 'Lỗi hệ thống: ' . $e->getMessage();
        }
    }
}

// Get bank accounts and transaction history
$bankAccounts = $accountManager->getBankAccounts($userId);
debugLog("Bank accounts: " . json_encode($bankAccounts));

$bankTransactions = $accountManager->getBankTransactionHistory($userId);
debugLog("Bank transactions: " . json_encode($bankTransactions));
?>

<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tài khoản của tôi - KPAH</title>
    <link href="../assets/homepage/css/bootstrap.min.css" rel="stylesheet" type="text/css">
    <link rel="stylesheet" href="../assets/homepage/css/style.css">
    <link rel="stylesheet" href="../assets/css/main.css">
    <link rel="stylesheet" href="../assets/css/topup.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <script src="../assets/homepage/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/homepage/js/jquery-1.9.1.min.js"></script>
    <style>
        body {
            background-image: url('../assets/homepage/images/bg-page1.jpg');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            color: #f9d686;
            font-family: Arial, sans-serif;
        }

        .account-container {
            background-color: rgba(0, 0, 0, 0.7);
            border-radius: 10px;
            padding: 20px;
            margin-top: 30px;
            border: 1px solid #f9d686;
            box-shadow: 0 0 15px rgba(249, 214, 134, 0.3);
        }

        .account-header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 1px solid #f9d686;
            padding-bottom: 15px;
        }

        .account-header h1 {
            color: #f9d686;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .account-section {
            margin-bottom: 30px;
            padding: 15px;
            background-color: rgba(0, 0, 0, 0.5);
            border-radius: 8px;
            border: 1px solid #f9d686;
        }

        .account-section h2 {
            color: #f9d686;
            font-size: 1.5rem;
            margin-bottom: 15px;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
        }

        .info-item {
            margin-bottom: 10px;
            display: flex;
            flex-wrap: wrap;
        }

        .info-label {
            font-weight: bold;
            width: 150px;
            color: #ffffff;
        }

        .info-value {
            flex: 1;
            color: #f9d686;
        }

        .btn-custom {
            background-color: #f9d686;
            color: #000;
            border: none;
            font-weight: bold;
        }

        .btn-custom:hover {
            background-color: #e5c677;
            color: #000;
        }

        .card-form {
            background-color: rgba(0, 0, 0, 0.5);
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #f9d686;
        }

        .form-control {
            background-color: rgba(255, 255, 255, 0.9);
            border: 1px solid #f9d686;
        }

        .form-label {
            color: #f9d686;
            font-weight: bold;
        }

        .transaction-table {
            width: 100%;
            margin-top: 15px;
        }

        .transaction-table th {
            background-color: rgba(249, 214, 134, 0.2);
            color: #f9d686;
            padding: 10px;
            text-align: left;
        }

        .transaction-table td {
            padding: 10px;
            border-bottom: 1px solid rgba(249, 214, 134, 0.2);
        }

        .status-pending {
            color: #ffc107;
        }

        .status-approved {
            color: #28a745;
        }

        .status-rejected {
            color: #dc3545;
        }

        .back-button {
            margin-top: 20px;
            margin-bottom: 30px;
        }

        /* Responsive styles */
        @media (max-width: 768px) {
            .account-container {
                margin-top: 15px;
                padding: 15px;
            }

            .info-label {
                width: 100%;
                margin-bottom: 5px;
            }

            .info-value {
                width: 100%;
            }

            .account-section {
                padding: 10px;
            }
        }

        /* Override some topup.css styles to match the theme */
        .topup-btn {
            background: linear-gradient(90deg, #f9d686, #e5c677);
            color: #000;
        }

        .topup-btn:hover {
            background: linear-gradient(90deg, #e5c677, #f9d686);
        }

        .topup-method-icon {
            color: #f9d686;
            background: rgba(249, 214, 134, 0.1);
        }

        .topup-method-card:hover .topup-method-icon {
            background: rgba(249, 214, 134, 0.2);
        }

        .topup-modal .modal-title {
            color: #f9d686;
        }

        .topup-form .form-control:focus,
        .topup-form .form-select:focus {
            border-color: rgba(249, 214, 134, 0.5);
            box-shadow: 0 0 0 0.25rem rgba(249, 214, 134, 0.25);
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="account-container">
                    <div class="account-header">
                        <h1>Tài khoản của tôi</h1>
                        <p>Xin chào, <strong><?php echo htmlspecialchars($username); ?></strong>!</p>
                    </div>

                    <?php if (!empty($topupMessage)): ?>
                        <div class="alert <?php echo $topupSuccess ? 'alert-success' : 'alert-danger'; ?> alert-dismissible fade show"
                            role="alert">
                            <?php echo $topupMessage; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($activationMessage)): ?>
                        <div class="alert <?php echo $activationSuccess ? 'alert-success' : 'alert-danger'; ?> alert-dismissible fade show"
                            role="alert">
                            <?php echo $activationMessage; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <div class="row">
                        <div class="col-md-6">
                            <!-- User Information Section -->
                            <div class="account-section">
                                <h2>Thông tin tài khoản</h2>
                                <div class="info-item">
                                    <div class="info-label">Tài khoản:</div>
                                    <div class="info-value"><?php echo htmlspecialchars($username); ?></div>
                                </div>
                                <?php if (isset($userDetails['user']['email'])): ?>
                                    <div class="info-item">
                                        <div class="info-label">Email:</div>
                                        <div class="info-value">
                                            <?php echo htmlspecialchars($userDetails['user']['email']); ?></div>
                                    </div>
                                <?php endif; ?>
                                <?php if (isset($userDetails['user']['phone'])): ?>
                                    <div class="info-item">
                                        <div class="info-label">Số điện thoại:</div>
                                        <div class="info-value">
                                            <?php echo htmlspecialchars($userDetails['user']['phone']); ?></div>
                                    </div>
                                <?php endif; ?>
                                <div class="info-item">
                                    <div class="info-label">Ngày đăng ký:</div>
                                    <div class="info-value">
                                        <?php
                                        if (isset($userDetails['user']['regdate'])) {
                                            echo date('d/m/Y H:i', strtotime($userDetails['user']['regdate']));
                                        } else {
                                            echo 'Không có thông tin';
                                        }
                                        ?>
                                    </div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">Trạng thái:</div>
                                    <div class="info-value">
                                        <?php
                                        if (isset($userDetails['activation']['status'])) {
                                            echo $userDetails['activation']['status'];

                                            // Hiển thị nút kích hoạt nếu tài khoản chưa được kích hoạt
                                            if ($userDetails['activation']['status'] !== 'Đã kích hoạt') {
                                                echo '<div class="mt-2">';
                                                echo '<form method="post" action="" class="d-inline">';
                                                echo '<button type="submit" name="submit_activation" class="btn btn-custom btn-sm" onclick="return confirm(\'Bạn có chắc chắn muốn kích hoạt tài khoản với chi phí 20 xu?\')">';
                                                echo '<i class="fas fa-check-circle"></i> Kích hoạt ngay (20 xu)';
                                                echo '</button>';
                                                echo '</form>';
                                                echo '</div>';
                                            }
                                        } else {
                                            echo 'Chưa kích hoạt';
                                            echo '<div class="mt-2">';
                                            echo '<form method="post" action="" class="d-inline">';
                                            echo '<button type="submit" name="submit_activation" class="btn btn-custom btn-sm" onclick="return confirm(\'Bạn có chắc chắn muốn kích hoạt tài khoản với chi phí 20 xu?\')">';
                                            echo '<i class="fas fa-check-circle"></i> Kích hoạt ngay (20 xu)';
                                            echo '</button>';
                                            echo '</form>';
                                            echo '</div>';
                                        }
                                        ?>
                                    </div>
                                </div>
                                <div class="info-item">
                                    <div class="info-label">Số dư tài khoản:</div>
                                    <div class="info-value"><?php echo number_format($balance, 0, ',', '.'); ?> VNĐ
                                    </div>
                                </div>
                                <!-- Button bảng xếp hạng nạp & cao thủ -->
                                <div class="info-item">
                                    <a href="/balance_ranking" class="btn btn-custom">Bảng xếp hạng nạp</a>
                                    <a href="/top_players" class="btn btn-custom">Bảng xếp hạng cao thủ</a>
                                </div>

                                <!-- Đổi mật khẩu và Giftcode -->
                                <div class="info-item mt-3">
                                    <a href="../Views/ChangePassword.php" class="btn btn-custom me-2">
                                        <i class="fas fa-key"></i> Đổi mật khẩu
                                    </a>
                                    <a href="../Views/giftcode.php" class="btn btn-custom">
                                        <i class="fas fa-gift"></i> Giftcode
                                    </a>
                                </div>
                            </div>

                            <!-- Character Information Section -->
                            <?php if (isset($userDetails['character']) && $userDetails['character']): ?>
                                <div class="account-section">
                                    <h2>Thông tin nhân vật</h2>
                                    <div class="info-item">
                                        <div class="info-label">Tên nhân vật:</div>
                                        <div class="info-value">
                                            <?php echo htmlspecialchars($userDetails['character']['charname']); ?></div>
                                    </div>
                                    <?php if (isset($userDetails['character']['lastLv'])): ?>
                                        <div class="info-item">
                                            <div class="info-label">Cấp độ:</div>
                                            <div class="info-value">
                                                <?php echo htmlspecialchars($userDetails['character']['lastLv']); ?></div>
                                        </div>
                                    <?php endif; ?>
                                    <?php if (isset($userDetails['character']['gold'])): ?>
                                        <div class="info-item">
                                            <div class="info-label">Gold:</div>
                                            <div class="info-value">
                                                <?php echo number_format($userDetails['character']['gold']); ?></div>
                                        </div>
                                    <?php endif; ?>
                                    <?php if (isset($userDetails['character']['topNap'])): ?>
                                        <div class="info-item">
                                            <div class="info-label">Top nạp:</div>
                                            <div class="info-value">
                                                <?php echo number_format($userDetails['character']['topNap']); ?></div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                        </div>

                        <div class="col-md-6">
                            <div class="account-section topup-section">
                                <h2 class="topup-title">Phương thức nạp tiền</h2>

                                <?php if (!empty($topupMessage)): ?>
                                    <div class="alert <?php echo $topupSuccess ? 'alert-success' : 'alert-danger'; ?> alert-dismissible fade show"
                                        role="alert">
                                        <?php echo $topupMessage; ?>
                                        <button type="button" class="btn-close" data-bs-dismiss="alert"
                                            aria-label="Close"></button>
                                    </div>
                                <?php endif; ?>

                                <div class="topup-methods">
                                    <div class="topup-method-card">
                                        <div class="topup-method-header">
                                            <div class="topup-method-icon">
                                                <i class="fas fa-credit-card"></i>
                                            </div>
                                            <h3 class="topup-method-title">Nạp thẻ cào</h3>
                                            <p class="topup-method-description">Nạp tiền qua thẻ cào điện thoại, thẻ game</p>
                                        </div>
                                        <div class="topup-method-body">
                                            <button type="button" class="topup-btn" data-bs-toggle="modal" data-bs-target="#cardTopupModal">
                                                <i class="fas fa-credit-card"></i> Nạp thẻ ngay
                                            </button>
                                        </div>
                                    </div>
                                    <div class="topup-method-card">
                                        <div class="topup-method-header">
                                            <div class="topup-method-icon">
                                                <i class="fas fa-university"></i>
                                            </div>
                                            <h3 class="topup-method-title">Chuyển khoản</h3>
                                            <p class="topup-method-description">Nạp tiền qua ATM/Internet Banking</p>
                                        </div>
                                        <div class="topup-method-body">
                                            <button type="button" class="topup-btn" data-bs-toggle="modal" data-bs-target="#bankTopupModal">
                                                <i class="fas fa-university"></i> Nạp qua ATM
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Modal Nạp thẻ cào -->
                                <div class="modal fade topup-modal" id="cardTopupModal" tabindex="-1" aria-labelledby="cardTopupModalLabel" aria-hidden="true">
                                    <div class="modal-dialog modal-lg">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="cardTopupModalLabel">Nạp thẻ cào</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                <form class="topup-form" method="post" action="" id="cardTopupForm">
                                                    <div class="mb-3">
                                                        <label for="card_type" class="form-label">Loại thẻ</label>
                                                        <select class="form-select" id="card_type" name="card_type" required>
                                                            <option value="">-- Chọn loại thẻ --</option>
                                                            <option value="VIETTEL">Viettel</option>
                                                            <option value="MOBIFONE">Mobifone</option>
                                                            <option value="VINAPHONE">Vinaphone</option>
                                                            <option value="VIETNAMOBILE">Vietnamobile</option>
                                                            <option value="ZING">Zing</option>
                                                            <option value="GATE">Gate</option>
                                                        </select>
                                                    </div>
                                                    <div class="mb-3">
                                                        <label for="card_amount" class="form-label">Mệnh giá</label>
                                                        <select class="form-select" id="card_amount" name="card_amount" required>
                                                            <option value="">-- Chọn mệnh giá --</option>
                                                            <option value="10000">10,000 VNĐ</option>
                                                            <option value="20000">20,000 VNĐ</option>
                                                            <option value="30000">30,000 VNĐ</option>
                                                            <option value="50000">50,000 VNĐ</option>
                                                            <option value="100000">100,000 VNĐ</option>
                                                            <option value="200000">200,000 VNĐ</option>
                                                            <option value="300000">300,000 VNĐ</option>
                                                            <option value="500000">500,000 VNĐ</option>
                                                            <option value="1000000">1,000,000 VNĐ</option>
                                                        </select>
                                                    </div>
                                                    <div class="mb-3">
                                                        <label for="card_serial" class="form-label">Số Serial</label>
                                                        <input type="text" class="form-control" id="card_serial" name="card_serial"
                                                            placeholder="Nhập số serial thẻ" required>
                                                        <div class="form-text">Nhập chính xác số serial in trên thẻ</div>
                                                    </div>
                                                    <div class="mb-3">
                                                        <label for="card_number" class="form-label">Mã thẻ</label>
                                                        <input type="text" class="form-control" id="card_number" name="card_number"
                                                            placeholder="Nhập mã thẻ" required>
                                                        <div class="form-text">Nhập chính xác mã thẻ sau khi cạo</div>
                                                    </div>
                                                    <div class="mb-3">
                                                        <div class="topup-alert topup-alert-warning">
                                                            <strong>Lưu ý:</strong>
                                                            <ul class="mb-0">
                                                                <li>Vui lòng chọn đúng mệnh giá thẻ</li>
                                                                <li>Nếu chọn sai mệnh giá, hệ thống sẽ khấu trừ theo mệnh giá thực tế
                                                                </li>
                                                                <li>Thẻ cào được xử lý tự động qua hệ thống</li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                    <div class="d-grid gap-2">
                                                        <button type="submit" name="submit_card" class="topup-btn"
                                                            id="submitCardBtn">
                                                            <i class="fas fa-credit-card"></i> Nạp thẻ
                                                        </button>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Lịch sử nạp thẻ -->
                            <div class="account-section mt-4 transaction-history">
                                <h2 class="transaction-history-title">Lịch sử nạp thẻ</h2>
                                <?php if (empty($cardTransactions)): ?>
                                    <p>Bạn chưa có giao dịch nạp thẻ nào.</p>
                                <?php else: ?>
                                    <div class="table-responsive">
                                        <table class="transaction-table">
                                            <thead>
                                                <tr>
                                                    <th>Ngày</th>
                                                    <th>Loại thẻ</th>
                                                    <th>Mệnh giá</th>
                                                    <th>Xu</th>
                                                    <th>Trạng thái</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($cardTransactions as $transaction): ?>
                                                    <tr>
                                                        <td><?php echo date('d/m/Y H:i', strtotime($transaction['created_at'])); ?>
                                                        </td>
                                                        <td><?php echo htmlspecialchars($transaction['card_type']); ?></td>
                                                        <td><?php echo number_format($transaction['amount']); ?> VNĐ</td>
                                                        <td><?php echo number_format($transaction['xu_amount']); ?></td>
                                                        <td>
                                                            <?php
                                                            switch ($transaction['status']) {
                                                                case 'pending':
                                                                    echo '<span class="status-pending">Đang xử lý</span>';
                                                                    break;
                                                                case 'success':
                                                                    echo '<span class="status-success">Đã duyệt</span>';
                                                                    break;
                                                                default:
                                                                    echo '<span class="status-failed">Đã từ chối</span>';
                                                                    if (!empty($transaction['message'])) {
                                                                        echo '<br><small>' . htmlspecialchars($transaction['message']) . '</small>';
                                                                    }
                                                            }
                                                            ?>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <!-- Modal Nạp qua ATM -->
                        <div class="modal fade topup-modal" id="bankTopupModal" tabindex="-1" aria-labelledby="bankTopupModalLabel" aria-hidden="true">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title" id="bankTopupModalLabel">Nạp tiền qua ATM/Chuyển khoản</h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                    </div>
                                    <div class="modal-body">
                                        <?php if (!empty($bankTopupMessage)): ?>
                                            <div class="alert <?php echo $bankTopupSuccess ? 'alert-success' : 'alert-danger'; ?> alert-dismissible fade show"
                                                role="alert">
                                                <?php echo $bankTopupMessage; ?>
                                                <button type="button" class="btn-close" data-bs-dismiss="alert"
                                                    aria-label="Close"></button>
                                            </div>
                                        <?php endif; ?>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="topup-form">
                                                    <form method="post" action="">
                                                        <div class="mb-3">
                                                            <label for="bank_account_id" class="form-label">Chọn tài khoản ngân
                                                                hàng</label>
                                                            <select class="form-select" id="bank_account_id" name="bank_account_id"
                                                                required>
                                                                <option value="">-- Chọn ngân hàng --</option>
                                                                <?php
                                                                echo "<script>console.log('Bank accounts for select: ' + JSON.stringify(" . json_encode($bankAccounts) . "));</script>";
                                                                if (empty($bankAccounts)) {
                                                                    echo "<script>console.error('No bank accounts available');</script>";
                                                                }
                                                                foreach ($bankAccounts as $bank): ?>
                                                                    <option value="<?php echo $bank['id']; ?>">
                                                                        <?php echo htmlspecialchars($bank['bank_name'] . ' - ' . $bank['account_name'] . ' - ' . $bank['account_number']); ?>
                                                                    </option>
                                                                <?php endforeach; ?>
                                                            </select>
                                                        </div>

                                                        <div class="mb-3">
                                                            <label for="bank_amount" class="form-label">Số tiền (VNĐ)</label>
                                                            <select class="form-select" id="bank_amount" name="bank_amount"
                                                                required>
                                                                <option value="">-- Chọn số tiền --</option>
                                                                <?php
                                                                echo "<script>console.log('Xu rates for select: ' + JSON.stringify(" . json_encode($xuRates) . "));</script>";
                                                                if (empty($xuRates)) {
                                                                    echo "<script>console.error('No xu rates available');</script>";
                                                                }
                                                                foreach ($xuRates as $amount => $rate): ?>
                                                                    <option value="<?php echo $amount; ?>">
                                                                        <?php echo number_format($amount) . ' VNĐ (' . number_format($rate['xu']) . ' xu)'; ?>
                                                                    </option>
                                                                <?php endforeach; ?>
                                                            </select>
                                                        </div>

                                                        <div class="mb-3">
                                                            <label for="transaction_code" class="form-label">Mã giao dịch (nếu
                                                                có)</label>
                                                            <input type="text" class="form-control" id="transaction_code"
                                                                name="transaction_code"
                                                                placeholder="Nhập mã giao dịch hoặc nội dung chuyển khoản">
                                                        </div>

                                                        <div class="mb-3">
                                                            <label for="user_account" class="form-label">Số tài khoản của
                                                                bạn</label>
                                                            <input type="text" class="form-control" id="user_account"
                                                                name="user_account"
                                                                placeholder="Nhập số tài khoản ngân hàng của bạn" required>
                                                            <small class="form-text">Số tài khoản này giúp admin dễ dàng
                                                                kiểm tra giao dịch của bạn.</small>
                                                        </div>

                                                        <div class="d-grid gap-2">
                                                            <button type="submit" name="submit_bank" class="topup-btn">
                                                                <i class="fas fa-university"></i> Gửi yêu cầu nạp tiền
                                                            </button>
                                                        </div>
                                                    </form>
                                                </div>
                                            </div>

                                            <div class="col-md-6">
                                                <div id="bank-info" class="bank-info">
                                                    <h5 class="bank-info-title">Thông tin tài khoản</h5>
                                                    <div id="bank-details" class="bank-details">
                                                        <p>Vui lòng chọn ngân hàng để xem thông tin chi tiết</p>
                                                    </div>
                                                    <div id="qr-code" class="qr-code" style="display: none;">
                                                        <img src="" alt="QR Code" class="img-fluid">
                                                    </div>
                                                    <div class="bank-note">
                                                        <p><strong>Lưu ý:</strong> Khi chuyển khoản, vui lòng ghi rõ nội dung:
                                                            <span
                                                                class="username"><?php echo htmlspecialchars($username); ?></span>
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Lịch sử giao dịch ATM -->
                        <div class="account-section mt-4 transaction-history">
                            <h2 class="transaction-history-title">Lịch sử giao dịch ATM/Chuyển khoản</h2>
                            <?php if (empty($bankTransactions)): ?>
                                <p>Bạn chưa có giao dịch nạp tiền qua ATM nào.</p>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="transaction-table">
                                        <thead>
                                            <tr>
                                                <th>Ngày</th>
                                                <th>Ngân hàng</th>
                                                <th>Số tiền</th>
                                                <th>Xu</th>
                                                <th>Mã giao dịch</th>
                                                <th>Trạng thái</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($bankTransactions as $transaction): ?>
                                                <tr>
                                                    <td><?php echo date('d/m/Y H:i', strtotime($transaction['transaction_date'])); ?>
                                                    </td>
                                                    <td><?php echo htmlspecialchars($transaction['bank_name']); ?></td>
                                                    <td><?php echo number_format($transaction['amount']); ?> VNĐ</td>
                                                    <td><?php echo number_format($transaction['xu_amount']); ?></td>
                                                    <td><?php echo htmlspecialchars($transaction['transaction_code'] ?: 'N/A'); ?>
                                                    </td>
                                                    <td>
                                                        <?php
                                                        switch ($transaction['status']) {
                                                            case 'pending':
                                                                echo '<span class="status-pending">Đang xử lý</span>';
                                                                break;
                                                            case 'approved':
                                                                echo '<span class="status-success">Đã duyệt</span>';
                                                                break;
                                                            default:
                                                                echo '<span class="status-failed">Đã từ chối</span>';
                                                        }
                                                        ?>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Transaction History Section -->


                    <?php if ($isAdmin): ?>
                        <!-- Admin Controls Section -->
                        <div class="account-section">
                            <h2>Quản trị viên</h2>
                            <p>Bạn có quyền quản trị viên.</p>
                            <a href="../Panel/index.php" class="btn btn-custom">Truy cập trang quản trị</a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <div class="text-center back-button">
                <a href="../index.php" class="btn btn-custom">Quay lại trang chủ</a>
            </div>
        </div>
    </div>
    </div>
    </div>

    <script>
        $(document).ready(function() {
            // Auto-dismiss alerts after 5 seconds
            setTimeout(function() {
                $('.alert').alert('close');
            }, 5000);
        });
    </script>

    <!-- Thêm script để hiển thị thông tin ngân hàng khi chọn -->
    <script>
        $(document).ready(function() {
            $('#bank_account_id').change(function() {
                var bankId = $(this).val();
                if (bankId) {
                    // Lấy thông tin ngân hàng từ danh sách
                    <?php foreach ($bankAccounts as $bank): ?>
                        if (bankId == <?php echo $bank['id']; ?>) {
                            var bankInfo =
                                '<p><strong>Ngân hàng:</strong> <?php echo htmlspecialchars($bank['bank_name']); ?></p>' +
                                '<p><strong>Tên tài khoản:</strong> <?php echo htmlspecialchars($bank['account_name']); ?></p>' +
                                '<p><strong>Số tài khoản:</strong> <?php echo htmlspecialchars($bank['account_number']); ?></p>' +
                                '<p><strong>Nội dung chuyển khoản:</strong> <?php echo htmlspecialchars($username); ?></p>';
                            $('#bank-details').html(bankInfo);

                            <?php if (!empty($bank['qr_code_path'])): ?>
                                $('#qr-code').show().find('img').attr('src',
                                    '<?php echo htmlspecialchars($bank['qr_code_path']); ?>');
                            <?php else: ?>
                                $('#qr-code').hide();
                            <?php endif; ?>
                        }
                    <?php endforeach; ?>
                } else {
                    $('#bank-details').html('<p>Vui lòng chọn ngân hàng để xem thông tin chi tiết</p>');
                    $('#qr-code').hide();
                }
            });
        });
    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const bankSelect = document.getElementById('bank_account_id');
            const bankDetails = document.getElementById('bank-details');
            const qrCode = document.getElementById('qr-code');

            if (bankSelect) {
                bankSelect.addEventListener('change', function() {
                    const selectedBankId = this.value;
                    if (!selectedBankId) {
                        bankDetails.innerHTML = '<p>Vui lòng chọn ngân hàng để xem thông tin chi tiết</p>';
                        qrCode.style.display = 'none';
                        return;
                    }

                    // Find the selected option text
                    const selectedOption = this.options[this.selectedIndex];
                    const bankInfo = selectedOption.text;
                    const parts = bankInfo.split(' - ');

                    if (parts.length >= 3) {
                        const bankName = parts[0];
                        const accountName = parts[1];
                        const accountNumber = parts[2];

                        bankDetails.innerHTML = `
                            <p><strong>Ngân hàng:</strong> ${bankName}</p>
                            <p><strong>Chủ tài khoản:</strong> ${accountName}</p>
                            <p><strong>Số tài khoản:</strong> ${accountNumber}</p>
                        `;

                        // Show QR code if available (you can add this feature later)
                        // qrCode.querySelector('img').src = 'path/to/qr/' + selectedBankId + '.png';
                        // qrCode.style.display = 'block';
                    }
                });
            }
        });
    </script>
</body>

</html>