<?php
/**
 * Database connection and helper functions for password reset
 */

require_once __DIR__ . '/config.php';

/**
 * Connect to the database using mysqli
 * 
 * @return mysqli Database connection
 */
function connectDB() {
    global $db_config;
    
    $conn = new mysqli(
        $db_config['host'],
        $db_config['username'],
        $db_config['password'],
        $db_config['database']
    );
    
    // Check connection
    if ($conn->connect_error) {
        throw new Exception("Database connection failed: " . $conn->connect_error);
    }
    
    // Set charset to utf8mb4
    $conn->set_charset("utf8mb4");
    
    return $conn;
}

/**
 * Ensure the password_reset_tokens table exists
 * 
 * @param mysqli $conn Database connection
 * @return bool True if table exists or was created successfully
 */
function ensureResetTokensTable($conn) {
    // Check if table exists
    $tableCheck = $conn->query("SHOW TABLES LIKE 'password_reset_tokens'");
    
    if ($tableCheck->num_rows == 0) {
        // Table doesn't exist, create it
        $sql = "CREATE TABLE IF NOT EXISTS password_reset_tokens (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            token VARCHAR(255) NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            expires_at DATETIME NOT NULL,
            used TINYINT(1) DEFAULT 0,
            INDEX (user_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        if (!$conn->query($sql)) {
            throw new Exception("Error creating password_reset_tokens table: " . $conn->error);
        }
    }
    
    return true;
}

/**
 * Find a user by email
 * 
 * @param mysqli $conn Database connection
 * @param string $email User email
 * @return array|null User data or null if not found
 */
function findUserByEmail($conn, $email) {
    $stmt = $conn->prepare("SELECT id, username, email FROM team_user WHERE email = ?");
    $stmt->bind_param("s", $email);
    $stmt->execute();
    
    $result = $stmt->get_result();
    $user = $result->fetch_assoc();
    
    $stmt->close();
    
    return $user;
}

/**
 * Create a password reset token for a user
 * 
 * @param mysqli $conn Database connection
 * @param int $userId User ID
 * @param string $token Reset token
 * @param int $expiryHours Token expiry in hours
 * @return bool True if token was created successfully
 */
function createResetToken($conn, $userId, $token, $expiryHours = 24) {
    // Delete any existing tokens for this user
    $stmt = $conn->prepare("DELETE FROM password_reset_tokens WHERE user_id = ?");
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $stmt->close();
    
    // Create expiry date
    $expiresAt = date('Y-m-d H:i:s', strtotime("+{$expiryHours} hours"));
    
    // Insert new token
    $stmt = $conn->prepare("INSERT INTO password_reset_tokens (user_id, token, expires_at) VALUES (?, ?, ?)");
    $stmt->bind_param("iss", $userId, $token, $expiresAt);
    $result = $stmt->execute();
    $stmt->close();
    
    return $result;
}

/**
 * Verify a password reset token
 * 
 * @param mysqli $conn Database connection
 * @param string $token Reset token
 * @return array|null Token data or null if invalid
 */
function verifyResetToken($conn, $token) {
    $stmt = $conn->prepare("SELECT * FROM password_reset_tokens WHERE token = ? AND used = 0 AND expires_at > NOW()");
    $stmt->bind_param("s", $token);
    $stmt->execute();
    
    $result = $stmt->get_result();
    $tokenData = $result->fetch_assoc();
    
    $stmt->close();
    
    return $tokenData;
}

/**
 * Mark a token as used
 * 
 * @param mysqli $conn Database connection
 * @param int $tokenId Token ID
 * @return bool True if token was marked as used
 */
function markTokenAsUsed($conn, $tokenId) {
    $stmt = $conn->prepare("UPDATE password_reset_tokens SET used = 1 WHERE id = ?");
    $stmt->bind_param("i", $tokenId);
    $result = $stmt->execute();
    $stmt->close();
    
    return $result;
}

/**
 * Update user password
 * 
 * @param mysqli $conn Database connection
 * @param int $userId User ID
 * @param string $hashedPassword Hashed password
 * @return bool True if password was updated
 */
function updateUserPassword($conn, $userId, $hashedPassword) {
    $stmt = $conn->prepare("UPDATE team_user SET password = ? WHERE id = ?");
    $stmt->bind_param("si", $hashedPassword, $userId);
    $result = $stmt->execute();
    $stmt->close();
    
    return $result;
}

/**
 * Hash password using MySQL PASSWORD() function format
 * 
 * @param string $password Plain text password
 * @return string Hashed password
 */
function hashPassword($password) {
    return '*' . strtoupper(sha1(sha1($password, true)));
}
