<?php
// Start the session at the beginning of the file
session_start();

// Load configuration
require_once 'config.php';

// Initialize checkreg session variable if not set
if (!isset($_SESSION['checkreg'])) {
    $_SESSION['checkreg'] = md5(rand(10000, 99999));
}

// Check if user is logged in
$isLoggedIn = isset($_SESSION['team_user']) && !empty($_SESSION['team_user']);
$username = $isLoggedIn ? $_SESSION['team_user'] : '';
?>
<!DOCTYPE html>
<html lang="vi" xmlns="http://www.w3.org/1999/xhtml">
<meta http-equiv="content-type" content="text/html;charset=UTF-8" />

<head>
    <title><?php echo $site_config['site_title']; ?></title>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="<?php echo $site_config['site_description']; ?>" />
    <meta name="keywords" content="<?php echo $site_config['site_keywords']; ?>">
    <meta name="facebook-domain-verification" content="z152vdvd9mty2sa7pbue1amhfqjpn7" />
    <meta property="og:type" content="article" />
    <meta property="og:site_name" content="<?php echo $site_config['site_name']; ?>">
    <meta property="og:title" content="<?php echo $site_config['site_title']; ?>">
    <meta property="og:image" content="<?php echo $site_config['og_image']; ?>">
    <meta property="og:url" content="<?php echo $site_config['site_url']; ?>">
    <meta property="og:description" content="<?php echo $site_config['site_description']; ?>">
    <link rel="icon" href='<?php echo $site_config['favicon_url']; ?>' type="image/x-icon" />
    <link href="assets/homepage/css/bootstrap.min.css" rel="stylesheet" type="text/css">
    <link rel="stylesheet" href="assets/homepage/css/style.css">
    <link rel="stylesheet" href="assets/css/main.css">
    <link rel="stylesheet" href="assets/css/dynamic-theme.php">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <script src="assets/homepage/js/bootstrap.bundle.min.js"></script>
    <script src="assets/homepage/js/jquery-1.9.1.min.js"></script>
    <style>
        /* Modal dialog custom responsive */
        .modal-dialog {
            max-width: 90%;
            /* Chiều rộng tối đa cho màn hình nhỏ */
            width: 1066px;
            /* Chiều rộng modal cho màn hình lớn */
            margin: auto;
            /* Căn giữa */
            /*top: 50%; !* Đưa modal xuống giữa màn hình *!*/
            transform: translateY(-50%);
            /* Kéo lên để căn giữa hoàn hảo */
        }

        /* Modal content custom responsive */
        .modal-content {
            width: 100%;
            /* Đảm bảo modal co giãn theo modal-dialog */
            max-width: 100%;
            /* Modal content không vượt quá kích thước dialog */
            height: auto;
            /* Tự động điều chỉnh chiều cao */
            aspect-ratio: 16 / 9;
            /* Duy trì tỉ lệ 16:9 cho ảnh nền */
            background-image: url('assets/homepage/images/pop.png');
            /* Đường dẫn ảnh nền */
            background-size: cover;
            /* Đảm bảo ảnh phủ toàn bộ modal */
            background-position: center;
            /* Căn giữa ảnh nền */
            background-color: transparent;
            border-radius: 10px;
            /* Bo góc modal */
            border: none;
            /* Xóa viền mặc định */
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            /* Hiệu ứng đổ bóng */
            overflow: hidden;
            /* Cắt nội dung vượt ra ngoài modal */
        }

        .modal-header,
        .modal-footer {
            background-color: rgba(0, 0, 0, 0.5);
            /* Nền mờ cho header và footer */
            color: white;
            /* Chữ màu trắng */
            border: none;
            /* Xóa viền */
        }

        .modal-body {
            background-color: rgba(0, 0, 0, 0.3);
            /* Nền mờ cho body */
            color: white;
            /* Chữ màu trắng */
            padding: 20px;
            text-align: center;
        }

        .nut-pop {
            margin-top: 470px;
            margin-left: 370px;
        }

        /* Account Header Styles */
        .account-header {
            display: inline-block;
            position: relative;
        }

        .account-btn {
            position: relative;
            display: inline-block;
        }

        .username-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-weight: bold;
            font-size: 16px;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
            white-space: nowrap;
        }

        .dropdown-menu {
            background-color: #2a2a2a;
            border: 1px solid #f9d686;
            min-width: 200px;
            z-index: 1000;
        }

        .dropdown-item {
            color: #f9d686;
            font-size: 16px;
            padding: 8px 16px;
        }

        .dropdown-item:hover {
            background-color: #3a3a3a;
            color: white;
        }

        .dropdown-divider {
            border-top: 1px solid #f9d686;
            opacity: 0.5;
        }

        /* Login and Register Modal Styles */
        #loginModal .modal-dialog,
        #registerModal .modal-dialog,
        #imageModal .modal-dialog {
            max-width: 400px;
            margin: 1.75rem auto;
            transform: none !important;
        }

        #loginModal .modal-content,
        #registerModal .modal-content {
            background-image: url('assets/homepage/images/bg-modal.jpg');
            background-size: cover;
            background-position: center;
            border-radius: 10px;
            border: none;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
            aspect-ratio: auto;
        }

        #loginModal .modal-header,
        #registerModal .modal-header,
        #loginModal .modal-footer,
        #registerModal .modal-footer {
            background-color: rgba(0, 0, 0, 0.6);
            border: none;
            color: white;
        }

        #loginModal .modal-body,
        #registerModal .modal-body {
            background-color: rgba(0, 0, 0, 0.4);
            color: white;
            padding: 20px;
        }

        #loginModal .form-label,
        #registerModal .form-label {
            color: #f9d686;
            font-weight: bold;
        }

        #loginModal .form-control,
        #registerModal .form-control {
            background-color: rgba(255, 255, 255, 0.9);
            border: 1px solid #f9d686;
        }

        #loginModal .btn-primary,
        #registerModal .btn-primary {
            background-color: #f9d686;
            border-color: #f9d686;
            color: #000;
            font-weight: bold;
        }

        #loginModal .btn-secondary,
        #registerModal .btn-secondary {
            background-color: rgba(0, 0, 0, 0.6);
            border-color: #f9d686;
            color: #f9d686;
        }

        /* Alert styles */
        .alert {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 9999;
            min-width: 300px;
            max-width: 80%;
            text-align: center;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            border-radius: 5px;
            padding: 15px 20px;
        }

        .alert-success {
            background-color: rgba(40, 167, 69, 0.9);
            border-color: #218838;
            color: white;
        }

        .alert-danger {
            background-color: rgba(220, 53, 69, 0.9);
            border-color: #c82333;
            color: white;
        }

        .alert .btn-close {
            color: white;
            opacity: 0.8;
        }

        .alert .btn-close:hover {
            opacity: 1;
        }

        /* Responsive for smaller screens */
        @media (max-width: 768px) {
            .modal-dialog {
                top: 30%;
                /* Đưa modal xuống giữa màn hình */
                max-width: 90%;
                /* Chiều rộng tối đa cho màn hình nhỏ */
                margin: 0.5rem auto;
            }

            .modal-content {
                aspect-ratio: 4 / 3;
                /* Đổi tỉ lệ ảnh nền trên thiết bị nhỏ */
            }

            .modal-header h5 {
                font-size: 18px;
                /* Giảm kích thước chữ */
            }

            .modal-body {
                font-size: 14px;
                /* Giảm kích thước chữ nội dung */
                padding: 15px;
            }

            .nut-pop {
                margin-top: 200px;
                margin-left: 80px;
                max-width: 50%;
            }

            #loginModal .modal-dialog,
            #registerModal .modal-dialog {
                max-width: 90%;
                margin: 0.5rem auto;
            }

            .g-recaptcha {
                transform: scale(0.85);
                transform-origin: 0 0;
            }

            .username-text {
                font-size: 14px;
            }

            /* Mobile header improvements */
            .header {
                padding: 10px 5px;
            }

            .btn-group1, .btn-group2 {
                display: flex;
                flex-wrap: wrap;
                justify-content: center;
                gap: 5px;
            }

            .btn-group-download {
                flex-direction: column;
                align-items: center;
                gap: 10px;
            }

            .btn-group-download .btn-group1,
            .btn-group-download .btn-group2 {
                flex-direction: row;
                flex-wrap: wrap;
                justify-content: center;
            }
        }

        /* Extra small screens */
        @media (max-width: 576px) {
            .modal-dialog {
                margin: 0.25rem;
                max-width: calc(100% - 0.5rem);
            }

            .username-text {
                font-size: 12px;
                max-width: 80px;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            .btn-group-download a img {
                max-width: 80px;
                height: auto;
            }

            .header .zoom-50 img {
                max-width: 60px;
                height: auto;
            }
        }

        /* Landscape orientation on mobile */
        @media (max-width: 768px) and (orientation: landscape) {
            .modal-dialog {
                max-height: 90vh;
                overflow-y: auto;
            }

            .btn-group-download {
                flex-direction: row;
                flex-wrap: wrap;
                justify-content: center;
            }
        }
    </style>

</head>

<body>
    <!-- -----------------Mobile------------------- -->
    <div class="wrapper">
        <div id="header" class="header d-flex justify-content-center d-block d-lg-none">
            <div class="btn-group1 px-1">
                <a class="zoom-50" target="_blank" href="<?php echo $site_config['download_ios']; ?>"
                    title="<?php echo $site_config['site_name']; ?>"><img class="img-fluid brightness"
                        src="assets/homepage/images/btn-download-ios-header.png" alt="" /></a>
                <a class="zoom-50" href="#" data-bs-toggle="modal" data-bs-target="#imageModal"
                    title="<?php echo $site_config['site_name']; ?>"><img class="img-fluid brightness"
                        src="assets/homepage/images/btn-download-ggplay-header.png" alt="" /></a>
                <a class="zoom-50" target="_blank" href="<?php echo $site_config['top_cao_thu']; ?>"
                    title="<?php echo $site_config['site_name']; ?>"><img class="img-fluid brightness"
                        src="assets/homepage/images/btn-topct-header.png" alt="" /></a>
            </div>
            <div class="btn-group2 px-1">
                <a class="zoom-50" target="_blank" href="<?php echo $site_config['download_jar']; ?>"
                    title="<?php echo $site_config['site_name']; ?>"><img class="img-fluid brightness"
                        src="assets/homepage/images/btn-download-jar-header.png" alt="" /></a>
                <a class="zoom-50" target="_blank" href="<?php echo $site_config['download_pc']; ?>"
                    title="<?php echo $site_config['site_name']; ?>"><img class="img-fluid brightness"
                        src="assets/homepage/images/btn-download-pc-header.png" alt="" /></a>
                <a class="zoom-50" target="_blank" href="<?php echo $site_config['top_nap']; ?>"
                    title="<?php echo $site_config['site_name']; ?>"><img class="img-fluid brightness"
                        src="assets/homepage/images/btn-topnap-header.png" alt="" /></a>
            </div>
            <?php if ($isLoggedIn): ?>
                <div class="account-header position-relative">
                    <a class="px-1 zoom-50 account-btn" href="#" data-bs-toggle="dropdown" aria-expanded="false"
                        title="Tài khoản">
                        <img class="img-fluid brightness" src="assets/homepage/images/btn-account-header.png?t=1" alt="" />
                        <span class="username-text"><?php echo htmlspecialchars($username); ?></span>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="Views/Account">Tài khoản của tôi</a></li>

                        <!-- <li><a class="dropdown-item" href="balance_ranking">Bảng xếp hạng số dư</a></li>
                        <li><a class="dropdown-item" href="top_players">Bảng xếp hạng cao thủ</a></li> -->
                        <li>
                            <hr class="dropdown-divider">
                        </li>
                        <li><a class="dropdown-item" href="handle/logout">Đăng xuất</a></li>
                    </ul>
                </div>
            <?php else: ?>
                <a class="px-1 zoom-50" href="#" data-bs-toggle="modal" data-bs-target="#loginModal" title="Đăng nhập"><img
                        class="img-fluid brightness" src="assets/homepage/images/btn-download-login-header.png?t=1"
                        alt="" /></a>
                <a class="px-1 zoom-50" href="#" data-bs-toggle="modal" data-bs-target="#registerModal" title="Đăng ký"><img
                        class="img-fluid brightness" src="assets/homepage/images/btn-napthe-header.png?t=1" alt="" /></a>
            <?php endif; ?>
        </div>
        <!-- ---------------------Kết Thúc Mobile--------------------- -->


        <!-- Giao Diện Desktop -->
        <section id="section1" class="section section1">
            <div class="section-background">
                <img class="img-fluid d-none d-lg-block" src="assets/homepage/images/bg-page1.jpg" alt="" />
                <img class="img-fluid d-block d-lg-none" src="assets/homepage/images/bg-page1-m.jpg" alt="" />
            </div>
            <div class="section-content">
                <h1 class="logo"><img class="img-fluid" src="<?php echo $site_config['logo_url']; ?>"
                        alt="<?php echo $site_config['site_name']; ?>" /></h1>
                <div class="art d-none d-lg-block">
                    <img class="img-fluid" src="assets/homepage/images/art-page1zz.png" alt="" />
                </div>
                <div class="art d-block d-lg-none">
                    <img class="img-fluid" src="assets/homepage/images/art-page1-mz.png?t=1" alt="" />
                </div>
                <div class="slogan">
                    <img class="img-fluid d-none d-lg-block mx-auto pulse" src="<?php echo $site_config['slogan_desktop_url']; ?>"
                        alt="<?php echo $site_config['site_name']; ?>" />
                    <img class="img-fluid d-block d-lg-none pulse" src="<?php echo $site_config['slogan_mobile_url']; ?>"
                        alt="<?php echo $site_config['site_name']; ?>" />
                </div>
                <div class="btn-group-download d-flex justify-content-center align-items-center">
                    <a class="px-1 zoom-50" href="index.html" title="Khí Phách Anh Hùng Z"><img
                            class="img-fluid brightness" src="assets/homepage/images/icon-kpahz.png" alt="" /></a>
                    <div class="btn-group1 px-1">
                        <a class="zoom-50" target="_blank" href="<?php echo $site_config['download_ios']; ?>"
                            title="<?php echo $site_config['site_name']; ?>"><img class="img-fluid brightness"
                                src="assets/homepage/images/btn-download-ios.png" alt="" /></a>
                        <a class="zoom-50" href="#" data-bs-toggle="modal" data-bs-target="#imageModal"
                            title="<?php echo $site_config['site_name']; ?>"><img class="img-fluid brightness"
                                src="assets/homepage/images/btn-download-ggplay-page1.png" alt="" /></a>
                        <a class="zoom-50" target="_blank" href="<?php echo $site_config['top_cao_thu']; ?>"
                            title="<?php echo $site_config['site_name']; ?>"><img class="img-fluid brightness"
                                src="assets/homepage/images/btn-topct-page1.png" alt="" /></a>
                    </div>
                    <div class="btn-group2 px-1">
                        <a class="zoom-50" target="_blank" href="<?php echo $site_config['download_jar']; ?>"
                            title="<?php echo $site_config['site_name']; ?>"><img class="img-fluid brightness"
                                src="assets/homepage/images/btn-download-jar-page1.png" alt="" /></a>
                        <a class="zoom-50" target="_blank" href="<?php echo $site_config['download_pc']; ?>"
                            title="<?php echo $site_config['site_name']; ?>"><img class="img-fluid brightness"
                                src="assets/homepage/images/btn-download-pc-page1.png" alt="" /></a>
                        <a class="zoom-50" target="_blank" href="<?php echo $site_config['top_nap']; ?>"
                            title="<?php echo $site_config['site_name']; ?>"><img class="img-fluid brightness"
                                src="assets/homepage/images/btn-topnap-page1.png" alt="" /></a>
                    </div>
                    <?php if ($isLoggedIn): ?>
                        <div class="account-header position-relative">
                            <a class="px-1 zoom-50 account-btn" href="#" data-bs-toggle="dropdown" aria-expanded="false"
                                title="Tài khoản">
                                <img class="img-fluid brightness" src="assets/homepage/images/btn-account-page1.png?t=1"
                                    alt="" />
                                <span class="username-text"
                                    style="font-size: 18px; color:rgb(139, 102, 0);"><?php echo htmlspecialchars($username); ?></span>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item" href="Views/Account">Tài khoản của tôi</a></li>

                                <!-- <li><a class="dropdown-item" href="balance_ranking">Bảng xếp hạng số dư</a></li>
                            <li><a class="dropdown-item" href="top_players">Bảng xếp hạng cao thủ</a></li> -->
                                <li>
                                    <hr class="dropdown-divider">
                                </li>
                                <li><a class="dropdown-item" href="handle/logout">Đăng xuất</a></li>
                            </ul>
                        </div>
                    <?php else: ?>
                        <a class="px-1 zoom-50" href="#" data-bs-toggle="modal" data-bs-target="#loginModal"
                            title="Đăng nhập"><img class="img-fluid brightness"
                                src="assets/homepage/images/btn-login.png?t=1" alt="" /></a>
                        <a class="px-1 zoom-50" href="#" data-bs-toggle="modal" data-bs-target="#registerModal"
                            title="Đăng ký"><img class="img-fluid brightness" src="assets/homepage/images/btn-reg.png?t=1"
                                alt="" /></a>
                    <?php endif; ?>
                </div>
            </div>
            <!-- Kết Thúc Desktop -->
            <div class="light">
                <img src="assets/homepage/images/light.png" alt="ảnh animation">
                <img src="assets/homepage/images/light.png" alt="ảnh animation">
            </div>
        </section>
        <section id="section2" class="section section2">
            <div class="section-background">
                <img class="img-fluid d-none d-lg-block" src="assets/homepage/images/bg-page2.png" alt="" />
                <img class="img-fluid d-block d-lg-none" src="assets/homepage/images/bg-page2-m.jpg" alt="" />
            </div>
            <div class="section-content">
                <h2 class="title-page2"><img class="img-fluid" src="assets/homepage/images/title-page2.png" alt="" />
                </h2>
                <div class="content-hoatdong">
                    <br />
                    <div class="d-flex justify-content-center align-items-end item item1">
                        <div class="left">
                            <h2 class="title">
                                <a href="#" title="">
                                    Thứ 2
                                </a>
                            </h2>
                            <p class="status">
                                chiếm thành KPAH<br />
                                chiếm thành SPECIAL<br />
                                Vận tiêu trận<br />
                                trấn yêu trận</p>
                        </div>
                        <div class="center px-5 d-flex flex-column">
                            <img src="assets/homepage/images/ic-square.png" class="img-fluid py-1" alt="" />
                            <img src="assets/homepage/images/ic-square.png" class="img-fluid py-2" alt="" />
                            <img src="assets/homepage/images/ic-square.png" class="img-fluid py-2" alt="" />
                        </div>
                        <div class="right">
                            22h- 23h<br />
                            21h- 22h<br />
                            9h00 - 21h00<br />
                            22h00-23h00
                        </div>
                    </div>

                    <br />
                    <div class="d-flex justify-content-center align-items-end item item2">
                        <div class="left">
                            <h2 class="title">
                                <a href="#" title="">
                                    Thứ 4
                                </a>
                            </h2>
                            <p class="status">
                                săn mãnh thú <br />
                                chiếm thành KPAH<br />
                                chiếm thành SPECIAL<br />
                                Vận tiêu trận<br />
                                trấn yêu trận</p>
                        </div>
                        <div class="center px-5 d-flex flex-column">
                            <img src="assets/homepage/images/ic-square.png" class="img-fluid py-1" alt="" />
                            <img src="assets/homepage/images/ic-square.png" class="img-fluid py-2" alt="" />
                            <img src="assets/homepage/images/ic-square.png" class="img-fluid py-2" alt="" />
                        </div>
                        <div class="right">
                            20h- 21h<br />
                            22h- 23h<br />
                            21h- 22h<br />
                            9h00 - 21h00<br />
                            22h00-23h00
                        </div>
                    </div>

                    <br />
                    <div class="d-flex justify-content-center align-items-end item item3">
                        <div class="left">
                            <h2 class="title">
                                <a href="#" title="">
                                    Thứ 6
                                </a>
                            </h2>
                            <p class="status">
                                chiếm thành KPAH<br />
                                chiếm thành SPECIAL<br />
                                Vận tiêu trận<br />
                                trấn yêu trận</p>
                        </div>
                        <div class="center px-5 d-flex flex-column">
                            <img src="assets/homepage/images/ic-square.png" class="img-fluid py-1" alt="" />
                            <img src="assets/homepage/images/ic-square.png" class="img-fluid py-2" alt="" />
                            <img src="assets/homepage/images/ic-square.png" class="img-fluid py-2" alt="" />
                        </div>
                        <div class="right">
                            22h- 23h<br />
                            21h- 22h<br />
                            9h00 - 21h00<br />
                            22h00-23h00
                        </div>
                    </div>

                    <br />
                    <div class="d-flex justify-content-center align-items-end item item3">
                        <div class="left">
                            <h2 class="title">
                                <a href="#" title="">
                                    Thứ 3,5,7
                                </a>
                            </h2>
                            <p class="status">
                                quốc chiến<br />
                                Vận tiêu trận<br />
                                trấn yêu trận</p>
                        </div>
                        <div class="center px-5 d-flex flex-column">
                            <img src="assets/homepage/images/ic-square.png" class="img-fluid py-1" alt="" />
                            <img src="assets/homepage/images/ic-square.png" class="img-fluid py-2" alt="" />
                            <img src="assets/homepage/images/ic-square.png" class="img-fluid py-2" alt="" />
                        </div>
                        <div class="right">
                            20h- 21h<br />
                            9h00 - 21h00<br />
                            22h00-23h00
                        </div>
                    </div>

                </div>
            </div>
            <div class="art-page2">
                <img src="<?php echo $site_config['art_page2_url']; ?>" class="img-fluid"
                    alt="<?php echo $site_config['site_name']; ?>" />
            </div>
        </section>
        <section id="section3" class="section section3">
            <div class="section-background">
                <img class="img-fluid d-block d-lg-none" src="assets/homepage/images/bg-page3-m.jpg" alt="" />
            </div>
            <div class="section-content">
                <div class="footer-content">
                    <!--div class="vtc-logo text-center">
				<img src="assets/homepage/images/logo-white.png" class="img-fluid px-3" alt="">
                </div-->
                    <div class="footer-text font-size-16">
                        <p class="mb-2"><?php echo $site_config['copyright_text']; ?></p>

                    </div>
                </div>



            </div>
        </section>
        <div class="bg-bottom">
            <img src="assets/homepage/images/bg-page.png" class="img-fluid" alt="">
        </div>
    </div>

    <!-- Menu float right -->
    <div class="download">
        <a class="download-btn-logo zoom-50" href="index.html" role="button"
            title="<?php echo $site_config['site_name']; ?>">
            <img src="<?php echo $site_config['logo_small_url']; ?>" class="img-fluid brightness"
                alt="<?php echo $site_config['site_name']; ?>" />
        </a>

        <a class="btn-img download-btn-jar zoom-50" target="_blank" href="<?php echo $site_config['download_ios']; ?>"
            role="button" title="Tải Game IOS">
            <span class="visually-hidden">Tải Game IOS</span>
            <img src="assets/homepage/images/btn-download-ios.png" class="img-fluid brightness" alt="Tải Game IOS" />
        </a>

        <a class="btn-img download-btn-jar zoom-50" target="_blank" href="<?php echo $site_config['download_jar']; ?>"
            role="button" title="Tải Game Jar">
            <span class="visually-hidden">Tải Game Jar</span>
            <img src="assets/homepage/images/btn-download-jar-right.png" class="img-fluid brightness"
                alt="Tải Game Jar" />
        </a>
        <a class="btn-img download-btn-apk zoom-50" target="_blank" href="<?php echo $site_config['download_apk']; ?>"
            role="button" title="Tải APK">
            <span class="visually-hidden">Tải Game APK</span>
            <img src="assets/homepage/images/btn-download-apk-right.png" class="img-fluid brightness" alt="Tải APK" />
        </a>
        <a class="btn-img download-btn-pc zoom-50" target="_blank" href="<?php echo $site_config['download_pc']; ?>"
            role="button" title="Tải Game PC">
            <span class="visually-hidden">Tải Game PC</span>
            <img src="assets/homepage/images/btn-download-pc-right.png" class="img-fluid brightness"
                alt="Tải Game PC" />
        </a>
        <div class="d-flex justify-content-between btn-group-social">
            <?php if ($isLoggedIn): ?>
                <a class="px-1 zoom-50" href="handle/logout" title="Đăng xuất">
                    <span class="visually-hidden">Đăng xuất</span>
                    <img src="assets/homepage/images/btn-napthe-right.png" class="img-fluid brightness" alt="Đăng xuất" />
                </a>
            <?php else: ?>
                <a class="px-1 zoom-50" href="#" data-bs-toggle="modal" data-bs-target="#loginModal" title="Đăng nhập">
                    <span class="visually-hidden">Đăng nhập</span>
                    <img src="assets/homepage/images/btn-napthe-right.png" class="img-fluid brightness" alt="Đăng nhập" />
                </a>
            <?php endif; ?>
        </div>
    </div>


    <script src="./assets/homepage/js/res-1.js?t=40851628"></script>

    <!-- Login Modal -->
    <div class="modal fade" id="loginModal" tabindex="-1" aria-labelledby="loginModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" style="max-width: 400px; transform: none;">
            <div class="modal-content"
                style="background-image: url('assets/homepage/images/bg-modal.jpg'); aspect-ratio: auto;">
                <div class="modal-header">
                    <h5 class="modal-title" id="loginModalLabel">Đăng Nhập</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form action="Controllers/Login" method="post">
                        <?php if (!empty($_GET['error'])) { ?>
                            <div class="alert alert-danger"><?php echo htmlspecialchars($_GET['error']); ?></div>
                        <?php } ?>
                        <?php if (!empty($_GET['success'])) { ?>
                            <div class="alert alert-success"><?php echo htmlspecialchars($_GET['success']); ?></div>
                        <?php } ?>
                        <div class="mb-3">
                            <label for="loginUsername" class="form-label">Tên đăng nhập</label>
                            <input type="text" class="form-control" id="loginUsername" name="user" required>
                        </div>
                        <div class="mb-3">
                            <label for="loginPassword" class="form-label">Mật khẩu</label>
                            <input type="password" class="form-control" id="loginPassword" name="pass" required>
                        </div>
                        <div class="mb-3 text-end">
                            <a href="#" data-bs-toggle="modal" data-bs-target="#forgotPasswordModal"
                                data-bs-dismiss="modal" style="color: #f9d686; text-decoration: none;">Quên mật
                                khẩu?</a>
                        </div>
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">Đăng Nhập</button>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal"
                        data-bs-target="#registerModal">Đăng Ký</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Register Modal -->
    <div class="modal fade" id="registerModal" tabindex="-1" aria-labelledby="registerModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" style="max-width: 400px; transform: none;">
            <div class="modal-content"
                style="background-image: url('assets/homepage/images/bg-modal.jpg'); aspect-ratio: auto;">
                <div class="modal-header">
                    <h5 class="modal-title" id="registerModalLabel">Đăng Ký</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form action="Controllers/Register" method="post">
                        <?php if (!empty($_GET['error'])) { ?>
                            <div class="alert alert-danger"><?php echo htmlspecialchars($_GET['error']); ?></div>
                        <?php } ?>
                        <?php if (!empty($_GET['success'])) { ?>
                            <div class="alert alert-success"><?php echo htmlspecialchars($_GET['success']); ?></div>
                        <?php } ?>
                        <input type="hidden" name="checkreg"
                            value="<?php echo $_SESSION['checkreg'] ?? md5(rand(10000, 99999)); ?>">
                        <div class="mb-3">
                            <label for="registerUsername" class="form-label">Tên đăng nhập</label>
                            <input type="text" class="form-control" id="registerUsername" name="username" required>
                        </div>
                        <div class="mb-3">
                            <label for="registerEmail" class="form-label">Email</label>
                            <input type="email" class="form-control" id="registerEmail" name="email" required>
                            <small class="form-text text-light">Email sẽ được sử dụng để đặt lại mật khẩu khi cần
                                thiết.</small>
                        </div>
                        <div class="mb-3">
                            <label for="registerPassword" class="form-label">Mật khẩu</label>
                            <input type="password" class="form-control" id="registerPassword" name="password" required>
                        </div>
                        <div class="mb-3">
                            <label for="registerRePassword" class="form-label">Nhập lại mật khẩu</label>
                            <input type="password" class="form-control" id="registerRePassword" name="RePasswordword"
                                required>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">Đăng Ký</button>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal"
                        data-bs-target="#loginModal">Đăng Nhập</button>
                </div>
            </div>
        </div>
    </div>
    <!-- Account Modal -->
    <div class="modal fade" id="accountModal" tabindex="-1" aria-labelledby="accountModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content" style="background-image: url('assets/homepage/images/bg-modal.jpg');">
                <div class="modal-header">
                    <h5 class="modal-title" id="accountModalLabel">Thông tin tài khoản</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <?php
                    if ($isLoggedIn) {
                        require_once 'Controllers/Account.php';
                        $accountManager = new AccountManager($conn, $conn1);
                        $userDetails = $accountManager->getUserDetails($_SESSION['id']);
                        if ($userDetails):
                    ?>
                            <div class="account-info">
                                <h6>Thông tin cơ bản</h6>
                                <p>Tài khoản: <?php echo htmlspecialchars($userDetails['user']['username']); ?></p>
                                <p>Email: <?php echo htmlspecialchars($userDetails['user']['email']); ?></p>
                                <p>Trạng thái: <?php echo $userDetails['activation']['status']; ?></p>

                                <?php if ($userDetails['character']): ?>
                                    <h6 class="mt-3">Thông tin nhân vật</h6>
                                    <p>Tên nhân vật: <?php echo htmlspecialchars($userDetails['character']['charname']); ?></p>
                                    <p>Cấp độ: <?php echo htmlspecialchars($userDetails['character']['lastLv']); ?></p>
                                    <p>Gold: <?php echo number_format($userDetails['character']['gold']); ?></p>
                                    <p>Top nạp: <?php echo number_format($userDetails['character']['topNap']); ?></p>
                                <?php endif; ?>

                                <?php if ($accountManager->isAdmin()): ?>
                                    <div class="admin-controls mt-3">
                                        <h6>Quản trị</h6>
                                        <button class="btn btn-warning btn-sm"
                                            onclick="activateAccount(<?php echo $_SESSION['id']; ?>)">
                                            Kích hoạt tài khoản
                                        </button>
                                    </div>
                                <?php endif; ?>
                            </div>
                    <?php
                        endif;
                    }
                    ?>
                </div>
            </div>
        </div>
    </div>

    <script>
        function activateAccount(userId) {
            fetch('Controllers/activate_account.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        userId: userId
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Kích hoạt tài khoản thành công');
                        location.reload();
                    } else {
                        alert(data.message || 'Kích hoạt thất bại');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Đã xảy ra lỗi');
                });
        }
    </script>
    <!-- Image Modal -->
    <div class="modal fade" id="imageModal" tabindex="-1" aria-labelledby="imageModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="imageModalLabel">Tải Game</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="nut-pop">
                        <a href="<?php echo $site_config['download_apk']; ?>" class="btn btn-primary">Tải Game APK</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Forgot Password Modal -->
    <div class="modal fade" id="forgotPasswordModal" tabindex="-1" aria-labelledby="forgotPasswordModalLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" style="max-width: 400px; transform: none;">
            <div class="modal-content"
                style="background-image: url('assets/homepage/images/bg-modal.jpg'); aspect-ratio: auto;">
                <div class="modal-header">
                    <h5 class="modal-title" id="forgotPasswordModalLabel"><i class="fas fa-key"></i> Quên mật khẩu</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="forgotPasswordAlert" class="alert alert-info mb-3" role="alert">
                        <i class="fas fa-info-circle"></i> Vui lòng nhập địa chỉ email đã đăng ký với tài khoản của bạn.
                        Chúng tôi sẽ gửi hướng dẫn đặt lại mật khẩu đến email của bạn.
                    </div>
                    <form action="password_reset/forgot" method="post" id="forgotPasswordForm">
                        <div class="mb-3">
                            <label for="forgotPasswordEmail" class="form-label">Email</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                <input type="email" class="form-control" id="forgotPasswordEmail" name="email"
                                    placeholder="Nhập email của bạn" required>
                            </div>
                        </div>
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane"></i> Gửi yêu cầu
                            </button>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal"
                        data-bs-target="#loginModal">Đăng nhập</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            // Only apply click handler to imageModal content
            $("#imageModal .modal-content").click(function() {
                $('#imageModal').modal("hide");
            });

            // Fix for modals not showing
            $('.modal').appendTo('body');

            // Handle modal switching
            $('#loginModal').on('hidden.bs.modal', function() {
                // Clear form fields when modal is closed
                $('#loginUsername').val('');
                $('#loginPassword').val('');
            });

            $('#registerModal').on('hidden.bs.modal', function() {
                // Clear form fields when modal is closed
                $('#registerUsername').val('');
                $('#registerEmail').val('');
                $('#registerPassword').val('');
                $('#registerRePassword').val('');
            });

            $('#forgotPasswordModal').on('hidden.bs.modal', function() {
                // Clear form fields when modal is closed
                $('#forgotPasswordEmail').val('');
            });

            // Handle forgot password form submission
            $('#forgotPasswordForm').on('submit', function(e) {
                e.preventDefault();
                var email = $('#forgotPasswordEmail').val();

                if (!email) {
                    alert('Vui lòng nhập địa chỉ email');
                    return false;
                }

                $.ajax({
                    type: 'POST',
                    url: 'password_reset/forgot',
                    data: {
                        email: email
                    },
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            // Hide modal
                            $('#forgotPasswordModal').modal('hide');

                            // Show success message
                            showAlert(response.message, 'success');
                        } else {
                            // Show error message
                            showAlert(response.message, 'danger');
                        }
                    },
                    error: function() {
                        // Redirect to non-AJAX version if AJAX fails
                        $('#forgotPasswordForm').off('submit').submit();
                    }
                });

                return false;
            });

            // Debug modal opening
            $('[data-bs-toggle="modal"]').click(function() {
                var targetModal = $(this).data('bs-target');
                console.log('Attempting to open modal:', targetModal);
            });

            // Handle URL parameters for error and success messages
            function getUrlParameter(name) {
                name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
                var regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
                var results = regex.exec(location.search);
                return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
            }

            // Function to show alerts
            function showAlert(message, type) {
                // Create alert div
                var alertDiv = $('<div class="alert alert-' + type +
                        ' alert-dismissible fade show" role="alert"></div>')
                    .text(message)
                    .append(
                        '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>'
                    );

                // Add to page
                $('body').prepend(alertDiv);

                // Auto dismiss after 5 seconds
                setTimeout(function() {
                    alertDiv.alert('close');
                }, 5000);
            }

            var error = getUrlParameter('error');
            var success = getUrlParameter('success');

            if (error) {
                showAlert(error, 'danger');
            }

            if (success) {
                showAlert(success, 'success');
            }
        });
    </script>
    <script src="https://www.google.com/recaptcha/api.js" async defer></script>
</body>

</html>