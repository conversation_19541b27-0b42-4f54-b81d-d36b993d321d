<?php
// Include database configuration
require_once 'Controllers/database.php';
require_once 'Controllers/Account.php';

// Hàm debug log
function debugLog($message) {
    error_log("[" . date('Y-m-d H:i:s') . "] TEST_CARD_TOPUP: " . $message);
    echo $message . "<br>";
}

// Kết nối đến database
try {
    $conn = connectPDO($config1);
    echo "<h2>Kiểm tra tính năng nạp thẻ cào tự động</h2>";
    
    // Tạo đối tượng AccountManager
    $accountManager = new AccountManager($conn);
    
    // Kiểm tra xem có tham số action không
    $action = isset($_GET['action']) ? $_GET['action'] : '';
    
    if ($action == 'create_transaction') {
        // Lấy thông tin từ form
        $userId = isset($_POST['user_id']) ? (int)$_POST['user_id'] : 0;
        $cardType = isset($_POST['card_type']) ? $_POST['card_type'] : '';
        $cardNumber = isset($_POST['card_number']) ? $_POST['card_number'] : '';
        $cardSerial = isset($_POST['card_serial']) ? $_POST['card_serial'] : '';
        $amount = isset($_POST['amount']) ? (int)$_POST['amount'] : 0;
        
        if ($userId <= 0 || empty($cardType) || empty($cardNumber) || empty($cardSerial) || $amount <= 0) {
            echo "<div style='color: red; margin-bottom: 10px;'>Vui lòng điền đầy đủ thông tin!</div>";
        } else {
            // Tạo giao dịch nạp thẻ
            $result = $accountManager->createCardTransaction($userId, $cardType, $cardNumber, $cardSerial, $amount);
            
            if ($result['success']) {
                echo "<div style='color: green; margin-bottom: 10px;'>Tạo giao dịch thành công!</div>";
                echo "<div>Transaction ID: <strong>" . htmlspecialchars($result['transaction_id']) . "</strong></div>";
                echo "<div>Bạn có thể sử dụng Transaction ID này để mô phỏng callback từ TheSieuRe.com.</div>";
                
                // Hiển thị form mô phỏng callback
                echo "<h3>Mô phỏng callback từ TheSieuRe.com</h3>";
                echo "<form action='api/card_callback.php' method='get'>";
                echo "<input type='hidden' name='request_id' value='" . htmlspecialchars($result['transaction_id']) . "'>";
                
                echo "<div style='margin-bottom: 10px;'>";
                echo "<label for='status_code'>Status Code:</label><br>";
                echo "<select name='status_code' id='status_code'>";
                echo "<option value='1'>1 - Success (Thẻ đúng)</option>";
                echo "<option value='2'>2 - Failed (Thẻ sai)</option>";
                echo "<option value='3'>3 - Failed (Thẻ đã được sử dụng)</option>";
                echo "<option value='4'>4 - Failed (Thẻ bị khóa)</option>";
                echo "<option value='99'>99 - Pending (Đang xử lý)</option>";
                echo "</select>";
                echo "</div>";
                
                echo "<div style='margin-bottom: 10px;'>";
                echo "<label for='value'>Real Amount (VNĐ):</label><br>";
                echo "<input type='number' name='value' id='value' value='$amount'>";
                echo "</div>";
                
                echo "<div style='margin-bottom: 10px;'>";
                echo "<label for='message'>Message:</label><br>";
                echo "<input type='text' name='message' id='message' value='Test callback'>";
                echo "</div>";
                
                echo "<div style='margin-bottom: 10px;'>";
                echo "<button type='submit'>Send Callback</button>";
                echo "</div>";
                echo "</form>";
            } else {
                echo "<div style='color: red; margin-bottom: 10px;'>Lỗi: " . $result['message'] . "</div>";
            }
        }
    } elseif ($action == 'view_transaction') {
        // Xem thông tin giao dịch
        $transactionId = isset($_GET['transaction_id']) ? $_GET['transaction_id'] : '';
        
        if (empty($transactionId)) {
            echo "<div style='color: red; margin-bottom: 10px;'>Transaction ID không hợp lệ!</div>";
        } else {
            // Lấy thông tin giao dịch
            $stmt = $conn->prepare("SELECT * FROM card_transactions WHERE transaction_id = :transaction_id");
            $stmt->bindParam(':transaction_id', $transactionId, PDO::PARAM_STR);
            $stmt->execute();
            $transaction = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($transaction) {
                echo "<h3>Thông tin giao dịch</h3>";
                echo "<table border='1' cellpadding='5' cellspacing='0'>";
                echo "<tr><th>Field</th><th>Value</th></tr>";
                
                foreach ($transaction as $key => $value) {
                    echo "<tr>";
                    echo "<td>" . htmlspecialchars($key) . "</td>";
                    echo "<td>" . htmlspecialchars($value) . "</td>";
                    echo "</tr>";
                }
                
                echo "</table>";
                
                // Hiển thị form mô phỏng callback
                echo "<h3>Mô phỏng callback từ TheSieuRe.com</h3>";
                echo "<form action='api/card_callback.php' method='get'>";
                echo "<input type='hidden' name='request_id' value='" . htmlspecialchars($transactionId) . "'>";
                
                echo "<div style='margin-bottom: 10px;'>";
                echo "<label for='status_code'>Status Code:</label><br>";
                echo "<select name='status_code' id='status_code'>";
                echo "<option value='1'>1 - Success (Thẻ đúng)</option>";
                echo "<option value='2'>2 - Failed (Thẻ sai)</option>";
                echo "<option value='3'>3 - Failed (Thẻ đã được sử dụng)</option>";
                echo "<option value='4'>4 - Failed (Thẻ bị khóa)</option>";
                echo "<option value='99'>99 - Pending (Đang xử lý)</option>";
                echo "</select>";
                echo "</div>";
                
                echo "<div style='margin-bottom: 10px;'>";
                echo "<label for='value'>Real Amount (VNĐ):</label><br>";
                echo "<input type='number' name='value' id='value' value='" . $transaction['amount'] . "'>";
                echo "</div>";
                
                echo "<div style='margin-bottom: 10px;'>";
                echo "<label for='message'>Message:</label><br>";
                echo "<input type='text' name='message' id='message' value='Test callback'>";
                echo "</div>";
                
                echo "<div style='margin-bottom: 10px;'>";
                echo "<button type='submit'>Send Callback</button>";
                echo "</div>";
                echo "</form>";
            } else {
                echo "<div style='color: red; margin-bottom: 10px;'>Không tìm thấy giao dịch với ID: " . htmlspecialchars($transactionId) . "</div>";
            }
        }
    } else {
        // Hiển thị form tạo giao dịch
        echo "<h3>Tạo giao dịch nạp thẻ mới</h3>";
        
        // Lấy danh sách người dùng
        $stmt = $conn->query("SELECT id, username FROM team_user ORDER BY id DESC LIMIT 20");
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<form method='post' action='?action=create_transaction'>";
        
        echo "<div style='margin-bottom: 10px;'>";
        echo "<label for='user_id'>Người dùng:</label><br>";
        echo "<select name='user_id' id='user_id' required>";
        echo "<option value=''>-- Chọn người dùng --</option>";
        
        foreach ($users as $user) {
            echo "<option value='" . $user['id'] . "'>" . htmlspecialchars($user['username']) . " (ID: " . $user['id'] . ")</option>";
        }
        
        echo "</select>";
        echo "</div>";
        
        echo "<div style='margin-bottom: 10px;'>";
        echo "<label for='card_type'>Loại thẻ:</label><br>";
        echo "<select name='card_type' id='card_type' required>";
        echo "<option value=''>-- Chọn loại thẻ --</option>";
        echo "<option value='VIETTEL'>Viettel</option>";
        echo "<option value='MOBIFONE'>Mobifone</option>";
        echo "<option value='VINAPHONE'>Vinaphone</option>";
        echo "<option value='VIETNAMOBILE'>Vietnamobile</option>";
        echo "<option value='ZING'>Zing</option>";
        echo "<option value='GATE'>Gate</option>";
        echo "</select>";
        echo "</div>";
        
        echo "<div style='margin-bottom: 10px;'>";
        echo "<label for='card_number'>Mã thẻ:</label><br>";
        echo "<input type='text' name='card_number' id='card_number' value='1234567890123456' required>";
        echo "</div>";
        
        echo "<div style='margin-bottom: 10px;'>";
        echo "<label for='card_serial'>Serial thẻ:</label><br>";
        echo "<input type='text' name='card_serial' id='card_serial' value='98765432109876' required>";
        echo "</div>";
        
        echo "<div style='margin-bottom: 10px;'>";
        echo "<label for='amount'>Mệnh giá (VNĐ):</label><br>";
        echo "<select name='amount' id='amount' required>";
        echo "<option value=''>-- Chọn mệnh giá --</option>";
        echo "<option value='10000'>10,000 VNĐ</option>";
        echo "<option value='20000'>20,000 VNĐ</option>";
        echo "<option value='30000'>30,000 VNĐ</option>";
        echo "<option value='50000'>50,000 VNĐ</option>";
        echo "<option value='100000'>100,000 VNĐ</option>";
        echo "<option value='200000'>200,000 VNĐ</option>";
        echo "<option value='300000'>300,000 VNĐ</option>";
        echo "<option value='500000'>500,000 VNĐ</option>";
        echo "<option value='1000000'>1,000,000 VNĐ</option>";
        echo "</select>";
        echo "</div>";
        
        echo "<div style='margin-bottom: 10px;'>";
        echo "<button type='submit'>Tạo giao dịch</button>";
        echo "</div>";
        echo "</form>";
        
        // Hiển thị danh sách giao dịch gần đây
        echo "<h3>Giao dịch gần đây</h3>";
        
        $stmt = $conn->query("SELECT ct.*, tu.username 
                             FROM card_transactions ct 
                             JOIN team_user tu ON ct.user_id = tu.id 
                             ORDER BY ct.created_at DESC LIMIT 10");
        $transactions = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($transactions) > 0) {
            echo "<table border='1' cellpadding='5' cellspacing='0'>";
            echo "<tr><th>ID</th><th>Transaction ID</th><th>User</th><th>Card Type</th><th>Amount</th><th>Status</th><th>Created At</th><th>Action</th></tr>";
            
            foreach ($transactions as $transaction) {
                echo "<tr>";
                echo "<td>" . $transaction['id'] . "</td>";
                echo "<td>" . htmlspecialchars($transaction['transaction_id']) . "</td>";
                echo "<td>" . htmlspecialchars($transaction['username']) . "</td>";
                echo "<td>" . htmlspecialchars($transaction['card_type']) . "</td>";
                echo "<td>" . number_format($transaction['amount']) . "</td>";
                echo "<td>" . htmlspecialchars($transaction['status']) . "</td>";
                echo "<td>" . $transaction['created_at'] . "</td>";
                echo "<td><a href='?action=view_transaction&transaction_id=" . urlencode($transaction['transaction_id']) . "'>View</a></td>";
                echo "</tr>";
            }
            
            echo "</table>";
        } else {
            echo "<p>Không có giao dịch nào.</p>";
        }
    }
    
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage();
}
?>