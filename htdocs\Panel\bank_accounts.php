<?php
session_start();
require_once '../Controllers/database.php';
require_once '../Controllers/Account.php';

// Kiểm tra đăng nhập và quyền admin
if (!isset($_SESSION['team_user']) || !isset($_SESSION['is_admin']) || !$_SESSION['is_admin']) {
    header("Location: ../index.php?error=" . urlencode('Bạn không có quyền truy cập trang này.'));
    exit();
}

// Kết nối database
try {
    $conn = connectPDO($config1);
    $conn1 = connectPDO($config2);

    // Khởi tạo AccountManager
    $accountManager = new AccountManager($conn, $conn1);
} catch (PDOException $e) {
    die("Lỗi kết nối cơ sở dữ liệu: " . $e->getMessage());
}

$username = $_SESSION['team_user'];
$message = '';
$messageType = '';

// <PERSON><PERSON> lý thêm tài khoản ngân hàng mới
if (isset($_POST['add_account'])) {
    $bankName = $_POST['bank_name'] ?? '';
    $accountName = $_POST['account_name'] ?? '';
    $accountNumber = $_POST['account_number'] ?? '';
    $isActive = isset($_POST['is_active']) ? 1 : 0;

    // Kiểm tra dữ liệu đầu vào
    if (empty($bankName) || empty($accountName) || empty($accountNumber)) {
        $message = 'Vui lòng điền đầy đủ thông tin tài khoản ngân hàng.';
        $messageType = 'danger';
    } else {
        try {
            // Xử lý tải lên mã QR (nếu có)
            $qrCodePath = '';
            if (isset($_FILES['qr_code']) && $_FILES['qr_code']['error'] == 0) {
                $uploadDir = '../assets/qrcodes/';

                // Tạo thư mục nếu chưa tồn tại
                if (!file_exists($uploadDir)) {
                    mkdir($uploadDir, 0777, true);
                }

                // Tạo tên file duy nhất
                $fileName = basename($_FILES['qr_code']['name']);
                $fileExt = pathinfo($fileName, PATHINFO_EXTENSION);
                $uniqueName = uniqid('qr_') . '.' . $fileExt;
                $uploadFile = $uploadDir . $uniqueName;

                // Kiểm tra loại file
                $allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
                if (!in_array($_FILES['qr_code']['type'], $allowedTypes)) {
                    throw new Exception('Chỉ chấp nhận file ảnh (JPEG, PNG, GIF).');
                }

                // Kiểm tra kích thước file (tối đa 2MB)
                if ($_FILES['qr_code']['size'] > 2 * 1024 * 1024) {
                    throw new Exception('Kích thước file không được vượt quá 2MB.');
                }

                // Di chuyển file tải lên
                if (move_uploaded_file($_FILES['qr_code']['tmp_name'], $uploadFile)) {
                    $qrCodePath = $uploadFile;
                } else {
                    throw new Exception('Không thể tải lên file.');
                }
            }

            // Thêm tài khoản ngân hàng vào cơ sở dữ liệu
            $stmt = $conn->prepare("INSERT INTO bank_accounts (bank_name, account_name, account_number, is_active, qr_code_path) VALUES (:bank_name, :account_name, :account_number, :is_active, :qr_code_path)");
            $stmt->bindParam(':bank_name', $bankName);
            $stmt->bindParam(':account_name', $accountName);
            $stmt->bindParam(':account_number', $accountNumber);
            $stmt->bindParam(':is_active', $isActive);
            $stmt->bindParam(':qr_code_path', $qrCodePath);
            $stmt->execute();

            $message = 'Thêm tài khoản ngân hàng thành công.';
            $messageType = 'success';
        } catch (Exception $e) {
            $message = 'Lỗi: ' . $e->getMessage();
            $messageType = 'danger';
        }
    }
}

// Xử lý cập nhật tài khoản ngân hàng
if (isset($_POST['update_account'])) {
    $accountId = $_POST['account_id'] ?? 0;
    $bankName = $_POST['bank_name'] ?? '';
    $accountName = $_POST['account_name'] ?? '';
    $accountNumber = $_POST['account_number'] ?? '';
    $isActive = isset($_POST['is_active']) ? 1 : 0;

    // Kiểm tra dữ liệu đầu vào
    if (empty($bankName) || empty($accountName) || empty($accountNumber) || $accountId <= 0) {
        $message = 'Vui lòng điền đầy đủ thông tin tài khoản ngân hàng.';
        $messageType = 'danger';
    } else {
        try {
            // Lấy thông tin tài khoản hiện tại
            $stmt = $conn->prepare("SELECT * FROM bank_accounts WHERE id = :id");
            $stmt->bindParam(':id', $accountId);
            $stmt->execute();
            $currentAccount = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$currentAccount) {
                throw new Exception('Không tìm thấy tài khoản ngân hàng.');
            }

            // Xử lý tải lên mã QR mới (nếu có)
            $qrCodePath = $currentAccount['qr_code_path']; // Giữ nguyên đường dẫn cũ nếu không có file mới

            if (isset($_FILES['qr_code']) && $_FILES['qr_code']['error'] == 0) {
                $uploadDir = '../assets/qrcodes/';

                // Tạo thư mục nếu chưa tồn tại
                if (!file_exists($uploadDir)) {
                    mkdir($uploadDir, 0777, true);
                }

                // Tạo tên file duy nhất
                $fileName = basename($_FILES['qr_code']['name']);
                $fileExt = pathinfo($fileName, PATHINFO_EXTENSION);
                $uniqueName = uniqid('qr_') . '.' . $fileExt;
                $uploadFile = $uploadDir . $uniqueName;

                // Kiểm tra loại file
                $allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
                if (!in_array($_FILES['qr_code']['type'], $allowedTypes)) {
                    throw new Exception('Chỉ chấp nhận file ảnh (JPEG, PNG, GIF).');
                }

                // Kiểm tra kích thước file (tối đa 2MB)
                if ($_FILES['qr_code']['size'] > 2 * 1024 * 1024) {
                    throw new Exception('Kích thước file không được vượt quá 2MB.');
                }

                // Di chuyển file tải lên
                if (move_uploaded_file($_FILES['qr_code']['tmp_name'], $uploadFile)) {
                    // Xóa file cũ nếu có
                    if (!empty($currentAccount['qr_code_path']) && file_exists($currentAccount['qr_code_path'])) {
                        unlink($currentAccount['qr_code_path']);
                    }

                    $qrCodePath = $uploadFile;
                } else {
                    throw new Exception('Không thể tải lên file.');
                }
            }

            // Cập nhật tài khoản ngân hàng
            $stmt = $conn->prepare("UPDATE bank_accounts SET bank_name = :bank_name, account_name = :account_name, account_number = :account_number, is_active = :is_active, qr_code_path = :qr_code_path, updated_at = NOW() WHERE id = :id");
            $stmt->bindParam(':id', $accountId);
            $stmt->bindParam(':bank_name', $bankName);
            $stmt->bindParam(':account_name', $accountName);
            $stmt->bindParam(':account_number', $accountNumber);
            $stmt->bindParam(':is_active', $isActive);
            $stmt->bindParam(':qr_code_path', $qrCodePath);
            $stmt->execute();

            $message = 'Cập nhật tài khoản ngân hàng thành công.';
            $messageType = 'success';
        } catch (Exception $e) {
            $message = 'Lỗi: ' . $e->getMessage();
            $messageType = 'danger';
        }
    }
}

// Xử lý xóa tài khoản ngân hàng
if (isset($_GET['action']) && $_GET['action'] == 'delete' && isset($_GET['id'])) {
    $accountId = (int)$_GET['id'];

    try {
        // Kiểm tra xem tài khoản có đang được sử dụng không
        $stmt = $conn->prepare("SELECT COUNT(*) FROM bank_transactions WHERE bank_account_id = :id");
        $stmt->bindParam(':id', $accountId);
        $stmt->execute();
        $transactionCount = $stmt->fetchColumn();

        if ($transactionCount > 0) {
            $message = 'Không thể xóa tài khoản ngân hàng này vì đã có giao dịch liên quan.';
            $messageType = 'danger';
        } else {
            // Lấy thông tin tài khoản để xóa file QR (nếu có)
            $stmt = $conn->prepare("SELECT qr_code_path FROM bank_accounts WHERE id = :id");
            $stmt->bindParam(':id', $accountId);
            $stmt->execute();
            $account = $stmt->fetch(PDO::FETCH_ASSOC);

            // Xóa file QR nếu có
            if ($account && !empty($account['qr_code_path']) && file_exists($account['qr_code_path'])) {
                unlink($account['qr_code_path']);
            }

            // Xóa tài khoản ngân hàng
            $stmt = $conn->prepare("DELETE FROM bank_accounts WHERE id = :id");
            $stmt->bindParam(':id', $accountId);
            $stmt->execute();

            $message = 'Xóa tài khoản ngân hàng thành công.';
            $messageType = 'success';
        }
    } catch (Exception $e) {
        $message = 'Lỗi: ' . $e->getMessage();
        $messageType = 'danger';
    }
}

// Lấy danh sách tài khoản ngân hàng
try {
    $stmt = $conn->query("SELECT * FROM bank_accounts ORDER BY id DESC");
    $bankAccounts = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $message = 'Lỗi khi lấy danh sách tài khoản ngân hàng: ' . $e->getMessage();
    $messageType = 'danger';
    $bankAccounts = [];
}

// Lấy thông tin tài khoản cần chỉnh sửa (nếu có)
$editAccount = null;
if (isset($_GET['action']) && $_GET['action'] == 'edit' && isset($_GET['id'])) {
    $accountId = (int)$_GET['id'];

    try {
        $stmt = $conn->prepare("SELECT * FROM bank_accounts WHERE id = :id");
        $stmt->bindParam(':id', $accountId);
        $stmt->execute();
        $editAccount = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$editAccount) {
            $message = 'Không tìm thấy tài khoản ngân hàng.';
            $messageType = 'danger';
        }
    } catch (PDOException $e) {
        $message = 'Lỗi khi lấy thông tin tài khoản ngân hàng: ' . $e->getMessage();
        $messageType = 'danger';
    }
}
?>

<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quản lý tài khoản ngân hàng - KPAH</title>
    <link href="../assets/homepage/css/bootstrap.min.css" rel="stylesheet" type="text/css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <script src="../assets/homepage/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/homepage/js/jquery-1.9.1.min.js"></script>
    <style>
        body {
            background-color: #1a1a1a;
            color: #f9d686;
            font-family: Arial, sans-serif;
        }

        .admin-container {
            padding: 20px;
        }

        .admin-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #f9d686;
        }

        .admin-title {
            color: #f9d686;
            margin: 0;
        }

        .admin-menu {
            background-color: #2a2a2a;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid #f9d686;
        }

        .admin-menu ul {
            list-style: none;
            padding: 0;
            margin: 0;
            display: flex;
            flex-wrap: wrap;
        }

        .admin-menu li {
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .admin-menu a {
            display: block;
            padding: 8px 15px;
            background-color: #333;
            color: #f9d686;
            text-decoration: none;
            border-radius: 3px;
            border: 1px solid rgba(249, 214, 134, 0.3);
            transition: all 0.3s ease;
        }

        .admin-menu a:hover,
        .admin-menu a.active {
            background-color: #f9d686;
            color: #000;
        }

        .admin-menu a i {
            margin-right: 5px;
        }

        .card {
            background-color: #2a2a2a;
            border: 1px solid #f9d686;
            margin-bottom: 20px;
        }

        .card-header {
            background-color: #3a3a3a;
            border-bottom: 1px solid #f9d686;
            color: #f9d686;
        }

        .btn-custom {
            background-color: #f9d686;
            color: #1a1a1a;
            border: none;
        }

        .btn-custom:hover {
            background-color: #e0c070;
            color: #1a1a1a;
        }

        .admin-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            color: #f9d686;
        }

        .admin-table th,
        .admin-table td {
            padding: 10px;
            border: 1px solid #f9d686;
        }

        .admin-table th {
            background-color: #3a3a3a;
        }

        .admin-table tr:nth-child(even) {
            background-color: #2a2a2a;
        }

        .admin-table tr:hover {
            background-color: #3a3a3a;
        }

        .form-control {
            background-color: #2a2a2a;
            border: 1px solid #f9d686;
            color: #f9d686;
        }

        .form-control:focus {
            background-color: #3a3a3a;
            border-color: #f9d686;
            color: #f9d686;
            box-shadow: 0 0 0 0.25rem rgba(249, 214, 134, 0.25);
        }

        .form-label {
            color: #f9d686;
        }

        .form-check-input {
            background-color: #2a2a2a;
            border: 1px solid #f9d686;
        }

        .form-check-input:checked {
            background-color: #f9d686;
            border-color: #f9d686;
        }

        .qr-preview {
            max-width: 150px;
            max-height: 150px;
            border: 1px solid #f9d686;
            margin-top: 10px;
        }
    </style>
</head>

<body>
    <div class="container admin-container">
        <div class="admin-header">
            <h1 class="admin-title">KPAH - Quản lý tài khoản ngân hàng</h1>
            <div class="admin-user">
                Xin chào, <strong><?php echo htmlspecialchars($username); ?></strong> |
                <a href="../Views/Account.php" class="text-light">Tài khoản</a> |
                <a href="../handle/logout.php" class="text-light">Đăng xuất</a>
            </div>
        </div>

        <div class="admin-menu">
            <ul>
                <li><a href="index.php"><i class="fas fa-tachometer-alt"></i> Tổng quan</a></li>
                <li><a href="users.php"><i class="fas fa-users"></i> Quản lý người dùng</a></li>
                <li><a href="bank_topups.php"><i class="fas fa-university"></i> Quản lý nạp ATM</a></li>
                <li><a href="bank_accounts.php" class="active"><i class="fas fa-credit-card"></i> Quản lý tài khoản ngân hàng</a></li>
                <li><a href="activations.php"><i class="fas fa-check-circle"></i> Kích hoạt tài khoản</a></li>
                <li><a href="giftcodes.php"><i class="fas fa-gift"></i> Quản lý Giftcode</a></li>
                <li><a href="api_management.php"><i class="fas fa-plug"></i> Quản lý API</a></li>
                <li><a href="password_decoder.php"><i class="fas fa-key"></i> Giải mã mật khẩu</a></li>
                <li><a href="site_settings.php"><i class="fas fa-cogs"></i> Cấu hình trang web</a></li>

                <li><a href="../index.php"><i class="fas fa-home"></i> Về trang chủ</a></li>
            </ul>
        </div>

        <?php if (!empty($message)): ?>
            <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                <?php echo $message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <div class="row">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><?php echo $editAccount ? 'Chỉnh sửa tài khoản ngân hàng' : 'Thêm tài khoản ngân hàng mới'; ?></h5>
                    </div>
                    <div class="card-body">
                        <form method="post" action="" enctype="multipart/form-data">
                            <?php if ($editAccount): ?>
                                <input type="hidden" name="account_id" value="<?php echo $editAccount['id']; ?>">
                            <?php endif; ?>

                            <div class="mb-3">
                                <label for="bank_name" class="form-label">Tên ngân hàng</label>
                                <input type="text" class="form-control" id="bank_name" name="bank_name" required
                                    value="<?php echo $editAccount ? htmlspecialchars($editAccount['bank_name']) : ''; ?>">
                            </div>

                            <div class="mb-3">
                                <label for="account_name" class="form-label">Tên chủ tài khoản</label>
                                <input type="text" class="form-control" id="account_name" name="account_name" required
                                    value="<?php echo $editAccount ? htmlspecialchars($editAccount['account_name']) : ''; ?>">
                            </div>

                            <div class="mb-3">
                                <label for="account_number" class="form-label">Số tài khoản</label>
                                <input type="text" class="form-control" id="account_number" name="account_number" required
                                    value="<?php echo $editAccount ? htmlspecialchars($editAccount['account_number']) : ''; ?>">
                            </div>

                            <div class="mb-3">
                                <label for="qr_code" class="form-label">Mã QR</label>
                                <input type="file" class="form-control" id="qr_code" name="qr_code" accept="image/*">
                                <small class="form-text text-muted">Chọn file ảnh mã QR (JPEG, PNG, GIF). Kích thước tối đa: 2MB.</small>

                                <?php if ($editAccount && !empty($editAccount['qr_code_path'])): ?>
                                    <div class="mt-2">
                                        <p>Mã QR hiện tại:</p>
                                        <img src="<?php echo htmlspecialchars($editAccount['qr_code_path']); ?>" alt="QR Code" class="qr-preview">
                                    </div>
                                <?php endif; ?>
                            </div>

                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="is_active" name="is_active"
                                    <?php echo (!$editAccount || $editAccount['is_active']) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="is_active">Kích hoạt</label>
                            </div>

                            <div class="d-grid gap-2">
                                <button type="submit" name="<?php echo $editAccount ? 'update_account' : 'add_account'; ?>" class="btn btn-custom">
                                    <i class="fas fa-save"></i> <?php echo $editAccount ? 'Cập nhật' : 'Thêm mới'; ?>
                                </button>

                                <?php if ($editAccount): ?>
                                    <a href="bank_accounts.php" class="btn btn-secondary">
                                        <i class="fas fa-times"></i> Hủy
                                    </a>
                                <?php endif; ?>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Danh sách tài khoản ngân hàng</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($bankAccounts)): ?>
                            <p>Không có tài khoản ngân hàng nào.</p>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="admin-table">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Ngân hàng</th>
                                            <th>Chủ tài khoản</th>
                                            <th>Số tài khoản</th>
                                            <th>Mã QR</th>
                                            <th>Trạng thái</th>
                                            <th>Thao tác</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($bankAccounts as $account): ?>
                                            <tr>
                                                <td><?php echo $account['id']; ?></td>
                                                <td><?php echo htmlspecialchars($account['bank_name']); ?></td>
                                                <td><?php echo htmlspecialchars($account['account_name']); ?></td>
                                                <td><?php echo htmlspecialchars($account['account_number']); ?></td>
                                                <td>
                                                    <?php if (!empty($account['qr_code_path'])): ?>
                                                        <a href="<?php echo htmlspecialchars($account['qr_code_path']); ?>" target="_blank">
                                                            <img src="<?php echo htmlspecialchars($account['qr_code_path']); ?>" alt="QR Code" class="qr-preview">
                                                        </a>
                                                    <?php else: ?>
                                                        <span class="text-muted">Không có</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if ($account['is_active']): ?>
                                                        <span class="badge bg-success">Đang hoạt động</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-danger">Đã vô hiệu hóa</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <a href="bank_accounts.php?action=edit&id=<?php echo $account['id']; ?>" class="btn btn-sm btn-warning">
                                                        <i class="fas fa-edit"></i> Sửa
                                                    </a>
                                                    <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal<?php echo $account['id']; ?>">
                                                        <i class="fas fa-trash"></i> Xóa
                                                    </button>
                                                </td>
                                            </tr>

                                            <!-- Modal Xóa -->
                                            <div class="modal fade" id="deleteModal<?php echo $account['id']; ?>" tabindex="-1" aria-labelledby="deleteModalLabel<?php echo $account['id']; ?>" aria-hidden="true">
                                                <div class="modal-dialog">
                                                    <div class="modal-content bg-dark text-light">
                                                        <div class="modal-header">
                                                            <h5 class="modal-title" id="deleteModalLabel<?php echo $account['id']; ?>">Xác nhận xóa</h5>
                                                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                                                        </div>
                                                        <div class="modal-body">
                                                            <p>Bạn có chắc chắn muốn xóa tài khoản ngân hàng này?</p>
                                                            <p><strong>Ngân hàng:</strong> <?php echo htmlspecialchars($account['bank_name']); ?></p>
                                                            <p><strong>Chủ tài khoản:</strong> <?php echo htmlspecialchars($account['account_name']); ?></p>
                                                            <p><strong>Số tài khoản:</strong> <?php echo htmlspecialchars($account['account_number']); ?></p>
                                                            <p class="text-danger">Lưu ý: Hành động này không thể hoàn tác!</p>
                                                        </div>
                                                        <div class="modal-footer">
                                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                                                            <a href="bank_accounts.php?action=delete&id=<?php echo $account['id']; ?>" class="btn btn-danger">
                                                                <i class="fas fa-trash"></i> Xóa
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Preview QR code image before upload
        document.getElementById('qr_code').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(event) {
                    // Check if there's an existing preview
                    let preview = document.querySelector('.qr-preview-new');
                    if (!preview) {
                        // Create new preview element
                        preview = document.createElement('img');
                        preview.className = 'qr-preview qr-preview-new';
                        preview.alt = 'QR Code Preview';

                        // Create container with label
                        const container = document.createElement('div');
                        container.className = 'mt-2';
                        const label = document.createElement('p');
                        label.textContent = 'Xem trước mã QR mới:';

                        container.appendChild(label);
                        container.appendChild(preview);

                        // Add after the file input
                        e.target.parentNode.appendChild(container);
                    }

                    // Update preview image
                    preview.src = event.target.result;
                };
                reader.readAsDataURL(file);
            }
        });
    </script>
</body>

</html>