<?php
session_start();

// Kiểm tra quyền admin
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: ../admin/login.php');
    exit();
}

require_once '../config.php';

// Hàm kiểm tra file tồn tại
function checkFileExists($file) {
    return file_exists($file);
}

// Hàm kiểm tra quyền ghi file
function checkFileWritable($file) {
    return is_writable($file);
}

// Hàm kiểm tra thư mục tồn tại
function checkDirectoryExists($dir) {
    return is_dir($dir);
}

// Hàm kiểm tra CSS/JS links
function checkAssetLinks() {
    $assets = [
        '../assets/homepage/css/bootstrap.min.css',
        '../assets/homepage/css/style.css',
        '../assets/css/main.css',
        '../assets/css/dynamic-theme.php',
        '../assets/homepage/js/bootstrap.bundle.min.js',
        '../assets/homepage/js/jquery-1.9.1.min.js'
    ];
    
    $results = [];
    foreach ($assets as $asset) {
        $results[$asset] = checkFileExists($asset);
    }
    return $results;
}

// Hàm kiểm tra cấu hình
function checkConfiguration() {
    global $site_config;
    
    $required_configs = [
        'site_name',
        'site_title',
        'site_description',
        'logo_url',
        'download_ios',
        'download_jar',
        'download_apk',
        'download_pc'
    ];
    
    $results = [];
    foreach ($required_configs as $config) {
        $results[$config] = isset($site_config[$config]) && !empty($site_config[$config]);
    }
    return $results;
}

// Hàm kiểm tra theme configuration
function checkThemeConfiguration() {
    global $site_config;
    
    $theme_configs = [
        'primary_color',
        'secondary_color',
        'text_color',
        'admin_bg_color',
        'admin_card_bg',
        'background_image'
    ];
    
    $results = [];
    foreach ($theme_configs as $config) {
        $results[$config] = isset($site_config['theme'][$config]) && !empty($site_config['theme'][$config]);
    }
    return $results;
}

// Hàm kiểm tra responsive design issues
function checkResponsiveIssues() {
    $issues = [];
    
    // Kiểm tra CSS responsive
    $css_file = '../assets/homepage/css/style.css';
    if (file_exists($css_file)) {
        $css_content = file_get_contents($css_file);
        if (strpos($css_content, '@media') === false) {
            $issues[] = 'CSS không có media queries cho responsive design';
        }
    }
    
    // Kiểm tra viewport meta tag trong index.php
    $index_file = '../index.php';
    if (file_exists($index_file)) {
        $index_content = file_get_contents($index_file);
        if (strpos($index_content, 'viewport') === false) {
            $issues[] = 'Thiếu viewport meta tag trong index.php';
        }
    }
    
    return $issues;
}

// Thực hiện các kiểm tra
$file_checks = checkAssetLinks();
$config_checks = checkConfiguration();
$theme_checks = checkThemeConfiguration();
$responsive_issues = checkResponsiveIssues();

// Kiểm tra quyền ghi file config
$config_writable = checkFileWritable('../config.php');

// Kiểm tra thư mục assets
$assets_dir_exists = checkDirectoryExists('../assets');
$css_dir_exists = checkDirectoryExists('../assets/css');
$js_dir_exists = checkDirectoryExists('../assets/homepage/js');
$images_dir_exists = checkDirectoryExists('../assets/homepage/images');
?>

<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kiểm tra hệ thống - Quản trị viên</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="../assets/css/main.css">
    <link rel="stylesheet" href="../assets/css/dynamic-theme.php">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>

<body class="admin-page">
    <div class="container">
        <div class="admin-header">
            <h1><i class="fas fa-stethoscope"></i> Kiểm tra hệ thống</h1>
            <p class="mb-0">Kiểm tra lỗi và tình trạng website</p>
        </div>

        <div class="mb-3">
            <a href="index.php" class="back-link">
                <i class="fas fa-arrow-left"></i> Quay lại trang quản trị
            </a>
        </div>

        <!-- Kiểm tra file assets -->
        <div class="card mb-4">
            <div class="card-header">
                <h3 class="mb-0"><i class="fas fa-file-code"></i> Kiểm tra file Assets</h3>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>File</th>
                                <th>Trạng thái</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($file_checks as $file => $exists): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($file); ?></td>
                                <td>
                                    <?php if ($exists): ?>
                                        <span class="badge bg-success"><i class="fas fa-check"></i> Tồn tại</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger"><i class="fas fa-times"></i> Không tồn tại</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Kiểm tra cấu hình -->
        <div class="card mb-4">
            <div class="card-header">
                <h3 class="mb-0"><i class="fas fa-cog"></i> Kiểm tra cấu hình</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5>Cấu hình cơ bản</h5>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <tbody>
                                    <?php foreach ($config_checks as $config => $exists): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($config); ?></td>
                                        <td>
                                            <?php if ($exists): ?>
                                                <span class="badge bg-success"><i class="fas fa-check"></i></span>
                                            <?php else: ?>
                                                <span class="badge bg-warning"><i class="fas fa-exclamation"></i></span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h5>Cấu hình Theme</h5>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <tbody>
                                    <?php foreach ($theme_checks as $config => $exists): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($config); ?></td>
                                        <td>
                                            <?php if ($exists): ?>
                                                <span class="badge bg-success"><i class="fas fa-check"></i></span>
                                            <?php else: ?>
                                                <span class="badge bg-warning"><i class="fas fa-exclamation"></i></span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Kiểm tra quyền file -->
        <div class="card mb-4">
            <div class="card-header">
                <h3 class="mb-0"><i class="fas fa-lock"></i> Kiểm tra quyền file</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5>File config.php</h5>
                        <p>
                            Quyền ghi: 
                            <?php if ($config_writable): ?>
                                <span class="badge bg-success"><i class="fas fa-check"></i> Có thể ghi</span>
                            <?php else: ?>
                                <span class="badge bg-danger"><i class="fas fa-times"></i> Không thể ghi</span>
                            <?php endif; ?>
                        </p>
                    </div>
                    <div class="col-md-6">
                        <h5>Thư mục Assets</h5>
                        <ul class="list-unstyled">
                            <li>
                                assets/: 
                                <?php if ($assets_dir_exists): ?>
                                    <span class="badge bg-success"><i class="fas fa-check"></i></span>
                                <?php else: ?>
                                    <span class="badge bg-danger"><i class="fas fa-times"></i></span>
                                <?php endif; ?>
                            </li>
                            <li>
                                assets/css/: 
                                <?php if ($css_dir_exists): ?>
                                    <span class="badge bg-success"><i class="fas fa-check"></i></span>
                                <?php else: ?>
                                    <span class="badge bg-danger"><i class="fas fa-times"></i></span>
                                <?php endif; ?>
                            </li>
                            <li>
                                assets/homepage/js/: 
                                <?php if ($js_dir_exists): ?>
                                    <span class="badge bg-success"><i class="fas fa-check"></i></span>
                                <?php else: ?>
                                    <span class="badge bg-danger"><i class="fas fa-times"></i></span>
                                <?php endif; ?>
                            </li>
                            <li>
                                assets/homepage/images/: 
                                <?php if ($images_dir_exists): ?>
                                    <span class="badge bg-success"><i class="fas fa-check"></i></span>
                                <?php else: ?>
                                    <span class="badge bg-danger"><i class="fas fa-times"></i></span>
                                <?php endif; ?>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Kiểm tra responsive -->
        <div class="card mb-4">
            <div class="card-header">
                <h3 class="mb-0"><i class="fas fa-mobile-alt"></i> Kiểm tra Responsive Design</h3>
            </div>
            <div class="card-body">
                <?php if (empty($responsive_issues)): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i> Không phát hiện vấn đề responsive design
                    </div>
                <?php else: ?>
                    <div class="alert alert-warning">
                        <h5><i class="fas fa-exclamation-triangle"></i> Phát hiện các vấn đề:</h5>
                        <ul class="mb-0">
                            <?php foreach ($responsive_issues as $issue): ?>
                                <li><?php echo htmlspecialchars($issue); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Hành động khuyến nghị -->
        <div class="card">
            <div class="card-header">
                <h3 class="mb-0"><i class="fas fa-tools"></i> Hành động khuyến nghị</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5>Cải thiện giao diện</h5>
                        <ul>
                            <li><a href="theme_settings.php">Tùy chỉnh theme và màu sắc</a></li>
                            <li><a href="site_settings.php">Cập nhật cấu hình trang web</a></li>
                            <li>Kiểm tra và tối ưu hóa hình ảnh</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h5>Bảo trì hệ thống</h5>
                        <ul>
                            <li>Sao lưu dữ liệu định kỳ</li>
                            <li>Cập nhật các thư viện CSS/JS</li>
                            <li>Kiểm tra hiệu suất website</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>

</html>
