<?php
session_start();

// <PERSON><PERSON><PERSON> tra quyền admin
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: ../admin/login.php');
    exit();
}

require_once '../config.php';

// Xử lý khi form được gửi
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $theme_config = [
        'primary_color' => $_POST['primary_color'] ?? '#f9d686',
        'primary_dark' => $_POST['primary_dark'] ?? '#e5c677',
        'secondary_color' => $_POST['secondary_color'] ?? '#343a40',
        'text_color' => $_POST['text_color'] ?? '#f8f9fa',
        'bg_dark' => $_POST['bg_dark'] ?? 'rgba(0, 0, 0, 0.7)',
        'bg_darker' => $_POST['bg_darker'] ?? 'rgba(0, 0, 0, 0.8)',
        'border_color' => $_POST['border_color'] ?? 'rgba(249, 214, 134, 0.3)',
        'success_color' => $_POST['success_color'] ?? '#28a745',
        'warning_color' => $_POST['warning_color'] ?? '#ffc107',
        'danger_color' => $_POST['danger_color'] ?? '#dc3545',
        'info_color' => $_POST['info_color'] ?? '#17a2b8',
        'background_image' => $_POST['background_image'] ?? 'assets/homepage/images/bg-page1.jpg',
        'admin_bg_color' => $_POST['admin_bg_color'] ?? '#1a1a1a',
        'admin_card_bg' => $_POST['admin_card_bg'] ?? '#2a2a2a',
        'admin_header_bg' => $_POST['admin_header_bg'] ?? '#000000',
        'toolbar_color' => $_POST['toolbar_color'] ?? '#333333',
        'button_hover_effect' => $_POST['button_hover_effect'] ?? 'enabled',
        'border_radius' => $_POST['border_radius'] ?? '10px',
        'box_shadow' => $_POST['box_shadow'] ?? '0 4px 6px rgba(0, 0, 0, 0.1)'
    ];

    // Cập nhật cấu hình theme trong site_config
    $site_config['theme'] = $theme_config;

    // Tạo nội dung file config.php mới
    $config_content = "<?php\n// Cấu hình trang web\n\$site_config = " . var_export($site_config, true) . ";";

    // Lưu file config.php
    if (file_put_contents('../config.php', $config_content)) {
        $success_message = 'Cấu hình theme đã được cập nhật thành công!';
    } else {
        $error_message = 'Không thể lưu file cấu hình. Vui lòng kiểm tra quyền ghi file.';
    }
}

// Lấy cấu hình theme hiện tại
$current_theme = $site_config['theme'] ?? [];
?>

<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cấu hình Theme - Quản trị viên</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        body {
            background-color: <?php echo $current_theme['admin_bg_color'] ?? '#1a1a1a'; ?>;
            color: <?php echo $current_theme['primary_color'] ?? '#f9d686'; ?>;
            font-family: Arial, sans-serif;
            background-image: url('../<?php echo $current_theme['background_image'] ?? 'assets/homepage/images/bg-page1.jpg'; ?>');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
        }

        .container {
            max-width: 1200px;
            margin: 30px auto;
            padding: 20px;
        }

        .admin-header {
            background-color: <?php echo $current_theme['admin_header_bg'] ?? '#000000'; ?>;
            padding: 20px;
            border-radius: <?php echo $current_theme['border_radius'] ?? '10px'; ?>;
            margin-bottom: 30px;
            text-align: center;
            border: 2px solid <?php echo $current_theme['primary_color'] ?? '#f9d686'; ?>;
            box-shadow: <?php echo $current_theme['box_shadow'] ?? '0 4px 6px rgba(0, 0, 0, 0.1)'; ?>;
        }

        .admin-header h1 {
            color: <?php echo $current_theme['primary_color'] ?? '#f9d686'; ?>;
            margin: 0;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .card {
            background-color: <?php echo $current_theme['admin_card_bg'] ?? '#2a2a2a'; ?>;
            border: 1px solid <?php echo $current_theme['primary_color'] ?? '#f9d686'; ?>;
            border-radius: <?php echo $current_theme['border_radius'] ?? '10px'; ?>;
            margin-bottom: 20px;
            box-shadow: <?php echo $current_theme['box_shadow'] ?? '0 4px 6px rgba(0, 0, 0, 0.1)'; ?>;
        }

        .card-header {
            background-color: rgba(249, 214, 134, 0.1);
            border-bottom: 1px solid <?php echo $current_theme['primary_color'] ?? '#f9d686'; ?>;
            color: <?php echo $current_theme['primary_color'] ?? '#f9d686'; ?>;
            font-weight: bold;
        }

        .card-body {
            color: <?php echo $current_theme['text_color'] ?? '#f8f9fa'; ?>;
        }

        .form-label {
            color: <?php echo $current_theme['primary_color'] ?? '#f9d686'; ?>;
            font-weight: 600;
        }

        .form-control {
            background-color: rgba(255, 255, 255, 0.1);
            border: 1px solid <?php echo $current_theme['primary_color'] ?? '#f9d686'; ?>;
            color: <?php echo $current_theme['text_color'] ?? '#f8f9fa'; ?>;
            border-radius: 5px;
        }

        .form-control:focus {
            background-color: rgba(255, 255, 255, 0.15);
            border-color: <?php echo $current_theme['primary_color'] ?? '#f9d686'; ?>;
            color: <?php echo $current_theme['text_color'] ?? '#f8f9fa'; ?>;
            box-shadow: 0 0 0 0.2rem rgba(249, 214, 134, 0.25);
        }

        .btn-primary {
            background-color: <?php echo $current_theme['primary_color'] ?? '#f9d686'; ?>;
            border-color: <?php echo $current_theme['primary_color'] ?? '#f9d686'; ?>;
            color: #000;
            font-weight: bold;
            border-radius: 5px;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background-color: <?php echo $current_theme['primary_dark'] ?? '#e5c677'; ?>;
            border-color: <?php echo $current_theme['primary_dark'] ?? '#e5c677'; ?>;
            color: #000;
            <?php if (($current_theme['button_hover_effect'] ?? 'enabled') === 'enabled'): ?>
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            <?php endif; ?>
        }

        .btn-secondary {
            background-color: rgba(0, 0, 0, 0.6);
            border-color: <?php echo $current_theme['primary_color'] ?? '#f9d686'; ?>;
            color: <?php echo $current_theme['primary_color'] ?? '#f9d686'; ?>;
        }

        .btn-secondary:hover {
            background-color: rgba(0, 0, 0, 0.8);
            border-color: <?php echo $current_theme['primary_color'] ?? '#f9d686'; ?>;
            color: #fff;
        }

        .alert-success {
            background-color: rgba(40, 167, 69, 0.8);
            border-color: <?php echo $current_theme['success_color'] ?? '#28a745'; ?>;
            color: white;
        }

        .alert-danger {
            background-color: rgba(220, 53, 69, 0.8);
            border-color: <?php echo $current_theme['danger_color'] ?? '#dc3545'; ?>;
            color: white;
        }

        .color-preview {
            width: 40px;
            height: 40px;
            border-radius: 5px;
            border: 2px solid <?php echo $current_theme['primary_color'] ?? '#f9d686'; ?>;
            display: inline-block;
            margin-left: 10px;
            vertical-align: middle;
        }

        .nav-tabs .nav-link {
            color: <?php echo $current_theme['primary_color'] ?? '#f9d686'; ?>;
            background-color: transparent;
            border: 1px solid transparent;
        }

        .nav-tabs .nav-link.active {
            background-color: <?php echo $current_theme['primary_color'] ?? '#f9d686'; ?>;
            color: #000;
            border-color: <?php echo $current_theme['primary_color'] ?? '#f9d686'; ?>;
        }

        .nav-tabs .nav-link:hover {
            background-color: rgba(249, 214, 134, 0.1);
            border-color: <?php echo $current_theme['primary_color'] ?? '#f9d686'; ?>;
        }

        .back-link {
            color: <?php echo $current_theme['primary_color'] ?? '#f9d686'; ?>;
            text-decoration: none;
            font-weight: bold;
        }

        .back-link:hover {
            color: <?php echo $current_theme['primary_dark'] ?? '#e5c677'; ?>;
        }

        .form-text {
            color: rgba(248, 249, 250, 0.7);
        }

        .preview-section {
            background-color: rgba(0, 0, 0, 0.3);
            border-radius: <?php echo $current_theme['border_radius'] ?? '10px'; ?>;
            padding: 20px;
            margin-top: 20px;
            border: 1px solid <?php echo $current_theme['primary_color'] ?? '#f9d686'; ?>;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="admin-header">
            <h1><i class="fas fa-palette"></i> Cấu hình Theme & Giao diện</h1>
            <p class="mb-0">Tùy chỉnh màu sắc và giao diện website</p>
        </div>

        <div class="mb-3">
            <a href="index.php" class="back-link">
                <i class="fas fa-arrow-left"></i> Quay lại trang quản trị
            </a>
        </div>

        <?php if (isset($success_message)): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($error_message)): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle"></i> <?php echo $error_message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <form method="post" action="">
            <!-- Nav tabs -->
            <ul class="nav nav-tabs" id="themeTab" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="colors-tab" data-bs-toggle="tab" data-bs-target="#colors" type="button" role="tab">
                        <i class="fas fa-palette"></i> Màu sắc chính
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="admin-tab" data-bs-toggle="tab" data-bs-target="#admin" type="button" role="tab">
                        <i class="fas fa-cog"></i> Giao diện Admin
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="effects-tab" data-bs-toggle="tab" data-bs-target="#effects" type="button" role="tab">
                        <i class="fas fa-magic"></i> Hiệu ứng
                    </button>
                </li>
            </ul>

            <!-- Tab panes -->
            <div class="tab-content" id="themeTabContent">
                <!-- Màu sắc chính -->
                <div class="tab-pane fade show active" id="colors" role="tabpanel">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="mb-0"><i class="fas fa-palette"></i> Màu sắc chính</h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="primary_color" class="form-label">Màu chính</label>
                                    <div class="d-flex align-items-center">
                                        <input type="color" class="form-control form-control-color" id="primary_color" name="primary_color" 
                                               value="<?php echo $current_theme['primary_color'] ?? '#f9d686'; ?>">
                                        <input type="text" class="form-control ms-2" value="<?php echo $current_theme['primary_color'] ?? '#f9d686'; ?>" 
                                               onchange="document.getElementById('primary_color').value = this.value">
                                    </div>
                                    <div class="form-text">Màu chính của website (logo, nút, viền)</div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="primary_dark" class="form-label">Màu chính đậm</label>
                                    <div class="d-flex align-items-center">
                                        <input type="color" class="form-control form-control-color" id="primary_dark" name="primary_dark" 
                                               value="<?php echo $current_theme['primary_dark'] ?? '#e5c677'; ?>">
                                        <input type="text" class="form-control ms-2" value="<?php echo $current_theme['primary_dark'] ?? '#e5c677'; ?>" 
                                               onchange="document.getElementById('primary_dark').value = this.value">
                                    </div>
                                    <div class="form-text">Màu hover cho các nút</div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="secondary_color" class="form-label">Màu phụ</label>
                                    <div class="d-flex align-items-center">
                                        <input type="color" class="form-control form-control-color" id="secondary_color" name="secondary_color"
                                               value="<?php echo $current_theme['secondary_color'] ?? '#343a40'; ?>">
                                        <input type="text" class="form-control ms-2" value="<?php echo $current_theme['secondary_color'] ?? '#343a40'; ?>"
                                               onchange="document.getElementById('secondary_color').value = this.value">
                                    </div>
                                    <div class="form-text">Màu phụ cho text và background</div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="text_color" class="form-label">Màu chữ</label>
                                    <div class="d-flex align-items-center">
                                        <input type="color" class="form-control form-control-color" id="text_color" name="text_color"
                                               value="<?php echo $current_theme['text_color'] ?? '#f8f9fa'; ?>">
                                        <input type="text" class="form-control ms-2" value="<?php echo $current_theme['text_color'] ?? '#f8f9fa'; ?>"
                                               onchange="document.getElementById('text_color').value = this.value">
                                    </div>
                                    <div class="form-text">Màu chữ chính</div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="success_color" class="form-label">Màu thành công</label>
                                    <div class="d-flex align-items-center">
                                        <input type="color" class="form-control form-control-color" id="success_color" name="success_color"
                                               value="<?php echo $current_theme['success_color'] ?? '#28a745'; ?>">
                                        <input type="text" class="form-control ms-2" value="<?php echo $current_theme['success_color'] ?? '#28a745'; ?>"
                                               onchange="document.getElementById('success_color').value = this.value">
                                    </div>
                                    <div class="form-text">Màu cho thông báo thành công</div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="warning_color" class="form-label">Màu cảnh báo</label>
                                    <div class="d-flex align-items-center">
                                        <input type="color" class="form-control form-control-color" id="warning_color" name="warning_color"
                                               value="<?php echo $current_theme['warning_color'] ?? '#ffc107'; ?>">
                                        <input type="text" class="form-control ms-2" value="<?php echo $current_theme['warning_color'] ?? '#ffc107'; ?>"
                                               onchange="document.getElementById('warning_color').value = this.value">
                                    </div>
                                    <div class="form-text">Màu cho thông báo cảnh báo</div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="danger_color" class="form-label">Màu lỗi</label>
                                    <div class="d-flex align-items-center">
                                        <input type="color" class="form-control form-control-color" id="danger_color" name="danger_color"
                                               value="<?php echo $current_theme['danger_color'] ?? '#dc3545'; ?>">
                                        <input type="text" class="form-control ms-2" value="<?php echo $current_theme['danger_color'] ?? '#dc3545'; ?>"
                                               onchange="document.getElementById('danger_color').value = this.value">
                                    </div>
                                    <div class="form-text">Màu cho thông báo lỗi</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Giao diện Admin -->
                <div class="tab-pane fade" id="admin" role="tabpanel">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="mb-0"><i class="fas fa-cog"></i> Giao diện Admin</h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="admin_bg_color" class="form-label">Màu nền Admin</label>
                                    <div class="d-flex align-items-center">
                                        <input type="color" class="form-control form-control-color" id="admin_bg_color" name="admin_bg_color"
                                               value="<?php echo $current_theme['admin_bg_color'] ?? '#1a1a1a'; ?>">
                                        <input type="text" class="form-control ms-2" value="<?php echo $current_theme['admin_bg_color'] ?? '#1a1a1a'; ?>"
                                               onchange="document.getElementById('admin_bg_color').value = this.value">
                                    </div>
                                    <div class="form-text">Màu nền chính của trang admin</div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="admin_card_bg" class="form-label">Màu nền Card</label>
                                    <div class="d-flex align-items-center">
                                        <input type="color" class="form-control form-control-color" id="admin_card_bg" name="admin_card_bg"
                                               value="<?php echo $current_theme['admin_card_bg'] ?? '#2a2a2a'; ?>">
                                        <input type="text" class="form-control ms-2" value="<?php echo $current_theme['admin_card_bg'] ?? '#2a2a2a'; ?>"
                                               onchange="document.getElementById('admin_card_bg').value = this.value">
                                    </div>
                                    <div class="form-text">Màu nền các card trong admin</div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="admin_header_bg" class="form-label">Màu nền Header</label>
                                    <div class="d-flex align-items-center">
                                        <input type="color" class="form-control form-control-color" id="admin_header_bg" name="admin_header_bg"
                                               value="<?php echo $current_theme['admin_header_bg'] ?? '#000000'; ?>">
                                        <input type="text" class="form-control ms-2" value="<?php echo $current_theme['admin_header_bg'] ?? '#000000'; ?>"
                                               onchange="document.getElementById('admin_header_bg').value = this.value">
                                    </div>
                                    <div class="form-text">Màu nền header admin</div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="toolbar_color" class="form-label">Màu thanh công cụ</label>
                                    <div class="d-flex align-items-center">
                                        <input type="color" class="form-control form-control-color" id="toolbar_color" name="toolbar_color"
                                               value="<?php echo $current_theme['toolbar_color'] ?? '#333333'; ?>">
                                        <input type="text" class="form-control ms-2" value="<?php echo $current_theme['toolbar_color'] ?? '#333333'; ?>"
                                               onchange="document.getElementById('toolbar_color').value = this.value">
                                    </div>
                                    <div class="form-text">Màu thanh công cụ và menu</div>
                                </div>
                                <div class="col-md-12 mb-3">
                                    <label for="background_image" class="form-label">Ảnh nền</label>
                                    <input type="text" class="form-control" id="background_image" name="background_image"
                                           value="<?php echo $current_theme['background_image'] ?? 'assets/homepage/images/bg-page1.jpg'; ?>">
                                    <div class="form-text">Đường dẫn đến ảnh nền website</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Hiệu ứng -->
                <div class="tab-pane fade" id="effects" role="tabpanel">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="mb-0"><i class="fas fa-magic"></i> Hiệu ứng và Style</h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="button_hover_effect" class="form-label">Hiệu ứng hover nút</label>
                                    <select class="form-control" id="button_hover_effect" name="button_hover_effect">
                                        <option value="enabled" <?php echo ($current_theme['button_hover_effect'] ?? 'enabled') === 'enabled' ? 'selected' : ''; ?>>Bật</option>
                                        <option value="disabled" <?php echo ($current_theme['button_hover_effect'] ?? 'enabled') === 'disabled' ? 'selected' : ''; ?>>Tắt</option>
                                    </select>
                                    <div class="form-text">Hiệu ứng khi hover vào nút</div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="border_radius" class="form-label">Bo góc</label>
                                    <input type="text" class="form-control" id="border_radius" name="border_radius"
                                           value="<?php echo $current_theme['border_radius'] ?? '10px'; ?>">
                                    <div class="form-text">Độ bo góc cho các element (vd: 10px, 5px)</div>
                                </div>
                                <div class="col-md-12 mb-3">
                                    <label for="box_shadow" class="form-label">Đổ bóng</label>
                                    <input type="text" class="form-control" id="box_shadow" name="box_shadow"
                                           value="<?php echo $current_theme['box_shadow'] ?? '0 4px 6px rgba(0, 0, 0, 0.1)'; ?>">
                                    <div class="form-text">CSS box-shadow cho các card và element</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Preview Section -->
                    <div class="card">
                        <div class="card-header">
                            <h3 class="mb-0"><i class="fas fa-eye"></i> Xem trước</h3>
                        </div>
                        <div class="card-body">
                            <div class="preview-section">
                                <h5 style="color: var(--primary-color);">Xem trước giao diện</h5>
                                <p>Đây là đoạn text mẫu với màu chữ hiện tại.</p>
                                <button type="button" class="btn btn-primary me-2">Nút chính</button>
                                <button type="button" class="btn btn-secondary">Nút phụ</button>
                                <div class="mt-3">
                                    <div class="alert alert-success">Thông báo thành công</div>
                                    <div class="alert alert-warning">Thông báo cảnh báo</div>
                                    <div class="alert alert-danger">Thông báo lỗi</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="text-center mt-4">
                <button type="submit" class="btn btn-primary btn-lg">
                    <i class="fas fa-save"></i> Lưu cấu hình Theme
                </button>
                <a href="index.php" class="btn btn-secondary btn-lg ms-2">
                    <i class="fas fa-times"></i> Hủy bỏ
                </a>
            </div>
        </form>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>

</html>
