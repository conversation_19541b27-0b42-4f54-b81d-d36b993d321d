<?php
/**
 * Utility functions for password reset
 */

/**
 * Generate a secure random token
 * 
 * @param int $length Token length
 * @return string Random token
 */
function generateToken($length = 32) {
    return bin2hex(random_bytes($length));
}

/**
 * Validate email address
 * 
 * @param string $email Email address to validate
 * @return bool True if email is valid
 */
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * Validate password strength
 * 
 * @param string $password Password to validate
 * @param int $minLength Minimum password length
 * @return array Validation result with status and message
 */
function validatePassword($password, $minLength = 6) {
    $result = [
        'valid' => true,
        'message' => 'Mật khẩu hợp lệ'
    ];
    
    if (strlen($password) < $minLength) {
        $result['valid'] = false;
        $result['message'] = "Mật khẩu phải có ít nhất {$minLength} ký tự";
        return $result;
    }
    
    return $result;
}

/**
 * Check if passwords match
 * 
 * @param string $password Password
 * @param string $confirmPassword Password confirmation
 * @return bool True if passwords match
 */
function passwordsMatch($password, $confirmPassword) {
    return $password === $confirmPassword;
}

/**
 * Sanitize input data
 * 
 * @param string $data Input data
 * @return string Sanitized data
 */
function sanitizeInput($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

/**
 * Log debug messages
 * 
 * @param string $message Message to log
 * @return void
 */
function debugLog($message) {
    error_log("[PASSWORD_RESET] " . $message);
}

/**
 * Check if request is AJAX
 * 
 * @return bool True if request is AJAX
 */
function isAjaxRequest() {
    return !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
           strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
}

/**
 * Send JSON response
 * 
 * @param bool $success Success status
 * @param string $message Response message
 * @param array $data Additional data
 * @return void
 */
function sendJsonResponse($success, $message, $data = []) {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => $success,
        'message' => $message,
        'data' => $data
    ]);
    exit;
}

/**
 * Redirect with message
 * 
 * @param string $url URL to redirect to
 * @param string $message Message to display
 * @param string $type Message type (success, error)
 * @return void
 */
function redirectWithMessage($url, $message, $type = 'error') {
    $url = $url . (strpos($url, '?') !== false ? '&' : '?') . $type . '=' . urlencode($message);
    header("Location: {$url}");
    exit;
}
