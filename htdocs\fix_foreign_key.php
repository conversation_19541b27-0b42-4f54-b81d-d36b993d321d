<?php
// Bật hiển thị lỗi để debug
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Thông tin kết nối cơ sở dữ liệu
$host = 'localhost';
$dbname = 'account';
$username = 'root';
$password = '';

echo "<h1>Sửa lỗi ràng buộc khóa ngoại</h1>";

try {
    // Kết nối đến cơ sở dữ liệu
    $conn = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<p>Kết nối thành công đến cơ sở dữ liệu: $dbname</p>";
    
    // Kiểm tra cấu trúc bảng bank_transactions
    $result = $conn->query("SHOW CREATE TABLE bank_transactions");
    $row = $result->fetch(PDO::FETCH_ASSOC);
    
    echo "<h2>Cấu trúc bảng bank_transactions hiện tại:</h2>";
    echo "<pre>" . $row['Create Table'] . "</pre>";
    
    // Kiểm tra xem có ràng buộc khóa ngoại không
    if (strpos($row['Create Table'], 'FOREIGN KEY') !== false) {
        echo "<p>Đang xóa ràng buộc khóa ngoại...</p>";
        
        // Lấy tên của ràng buộc khóa ngoại
        preg_match('/CONSTRAINT `([^`]+)` FOREIGN KEY/', $row['Create Table'], $matches);
        
        if (isset($matches[1])) {
            $constraintName = $matches[1];
            echo "<p>Tên ràng buộc khóa ngoại: $constraintName</p>";
            
            // Xóa ràng buộc khóa ngoại
            $conn->exec("ALTER TABLE bank_transactions DROP FOREIGN KEY `$constraintName`");
            echo "<p style='color:green'>Đã xóa ràng buộc khóa ngoại thành công.</p>";
        } else {
            echo "<p style='color:orange'>Không tìm thấy tên ràng buộc khóa ngoại.</p>";
        }
    } else {
        echo "<p style='color:orange'>Không tìm thấy ràng buộc khóa ngoại trong bảng bank_transactions.</p>";
    }
    
    // Kiểm tra lại cấu trúc bảng sau khi sửa
    $result = $conn->query("SHOW CREATE TABLE bank_transactions");
    $row = $result->fetch(PDO::FETCH_ASSOC);
    
    echo "<h2>Cấu trúc bảng bank_transactions sau khi sửa:</h2>";
    echo "<pre>" . $row['Create Table'] . "</pre>";
    
    // Kiểm tra xem có ràng buộc INDEX không
    if (strpos($row['Create Table'], 'KEY `user_id`') !== false) {
        echo "<p>Đang xóa chỉ mục user_id...</p>";
        
        // Xóa chỉ mục
        $conn->exec("ALTER TABLE bank_transactions DROP INDEX `user_id`");
        echo "<p style='color:green'>Đã xóa chỉ mục user_id thành công.</p>";
    } else {
        echo "<p style='color:orange'>Không tìm thấy chỉ mục user_id trong bảng bank_transactions.</p>";
    }
    
    // Kiểm tra lại cấu trúc bảng sau khi sửa
    $result = $conn->query("SHOW CREATE TABLE bank_transactions");
    $row = $result->fetch(PDO::FETCH_ASSOC);
    
    echo "<h2>Cấu trúc bảng bank_transactions sau khi sửa hoàn tất:</h2>";
    echo "<pre>" . $row['Create Table'] . "</pre>";
    
    echo "<p style='color:green'>Đã sửa xong lỗi ràng buộc khóa ngoại. Bây giờ bạn có thể nạp tiền qua ATM mà không gặp lỗi.</p>";
    
} catch (PDOException $e) {
    echo "<h2 style='color:red'>Lỗi cơ sở dữ liệu</h2>";
    echo "<p>Lỗi: " . $e->getMessage() . "</p>";
}
?>
