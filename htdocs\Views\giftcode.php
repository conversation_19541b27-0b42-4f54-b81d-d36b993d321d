<?php
// Start the session at the beginning of the file
session_start();

// Check if user is logged in
if (!isset($_SESSION['team_user']) || empty($_SESSION['team_user'])) {
    header("Location: ../index.php?error=" . urlencode('Vui lòng đăng nhập để sử dụng giftcode.'));
    exit();
}

// Include database configuration
require_once '../Controllers/database.php';

// Connect to databases
try {
    // Connect to account database
    $conn = connectPDO($config1);

    // Connect to game_db database
    $conn1 = connectPDO($config2);
} catch (PDOException $e) {
    die("Lỗi kết nối cơ sở dữ liệu: " . $e->getMessage());
}

// Get user information
$userId = $_SESSION['id'];
$username = $_SESSION['team_user'];

// Initialize message variables
$message = '';
$messageType = '';

// Handle giftcode submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['submit_giftcode'])) {
    $giftcodeInput = trim($_POST['giftcode'] ?? '');
    
    if (empty($giftcodeInput)) {
        $message = 'Vui lòng nhập mã giftcode.';
        $messageType = 'danger';
    } else {
        try {
            // Check if giftcode exists
            $stmt = $conn1->prepare("SELECT * FROM giftcode WHERE giftcode = :giftcode");
            $stmt->bindParam(':giftcode', $giftcodeInput, PDO::PARAM_STR);
            $stmt->execute();
            $giftcodeData = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$giftcodeData) {
                $message = 'Giftcode không tồn tại.';
                $messageType = 'danger';
            } else {
                // Check if giftcode has expired
                // Sử dụng '0000-00-00 00:00:00' để kiểm tra giftcode không có thời hạn
                if (!empty($giftcodeData['expire']) && $giftcodeData['expire'] != '0000-00-00 00:00:00' && strtotime($giftcodeData['expire']) < time()) {
                    $message = 'Giftcode đã hết hạn.';
                    $messageType = 'danger';
                } else {
                    // Check if user has already used this giftcode
                    $stmt = $conn1->prepare("SELECT id FROM giftcode_used WHERE giftcode_id = :giftcode_id AND user_id = :user_id");
                    $stmt->bindParam(':giftcode_id', $giftcodeData['id'], PDO::PARAM_INT);
                    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
                    $stmt->execute();
                    
                    if ($stmt->rowCount() > 0) {
                        $message = 'Bạn đã sử dụng giftcode này rồi.';
                        $messageType = 'danger';
                    } else {
                        // Check if giftcode has reached its usage limit
                        $stmt = $conn1->prepare("SELECT COUNT(*) FROM giftcode_used WHERE giftcode_id = :giftcode_id");
                        $stmt->bindParam(':giftcode_id', $giftcodeData['id'], PDO::PARAM_INT);
                        $stmt->execute();
                        $usedCount = $stmt->fetchColumn();
                        
                        if ($usedCount >= $giftcodeData['limit_use']) {
                            $message = 'Giftcode đã đạt giới hạn sử dụng.';
                            $messageType = 'danger';
                        } else {
                            // Begin transaction
                            $conn1->beginTransaction();
                            
                            try {
                                // Record giftcode usage
                                $stmt = $conn1->prepare("INSERT INTO giftcode_used (giftcode_id, user_id, username) VALUES (:giftcode_id, :user_id, :username)");
                                $stmt->bindParam(':giftcode_id', $giftcodeData['id'], PDO::PARAM_INT);
                                $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
                                $stmt->bindParam(':username', $username, PDO::PARAM_STR);
                                $stmt->execute();
                                
                                // Update user's balance
                                $rewards = [];
                                
                                // Add xu if available
                                if ($giftcodeData['xu'] > 0) {
                                    $stmt = $conn->prepare("UPDATE team_user SET xu = xu + :xu WHERE id = :user_id");
                                    $stmt->bindParam(':xu', $giftcodeData['xu'], PDO::PARAM_INT);
                                    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
                                    $stmt->execute();
                                    $rewards[] = number_format($giftcodeData['xu']) . ' Xu';
                                }
                                
                                // Add luong if available
                                if ($giftcodeData['luong'] > 0) {
                                    $stmt = $conn->prepare("UPDATE team_user SET luong = luong + :luong WHERE id = :user_id");
                                    $stmt->bindParam(':luong', $giftcodeData['luong'], PDO::PARAM_INT);
                                    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
                                    $stmt->execute();
                                    $rewards[] = number_format($giftcodeData['luong']) . ' Lượng';
                                }
                                
                                // Add luongLock if available
                                if ($giftcodeData['luongLock'] > 0) {
                                    $stmt = $conn->prepare("UPDATE team_user SET luongkhoa = luongkhoa + :luongLock WHERE id = :user_id");
                                    $stmt->bindParam(':luongLock', $giftcodeData['luongLock'], PDO::PARAM_INT);
                                    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
                                    $stmt->execute();
                                    $rewards[] = number_format($giftcodeData['luongLock']) . ' Lượng khóa';
                                }
                                
                                // Process items if available
                                if (!empty($giftcodeData['item'])) {
                                    // TODO: Implement item processing logic
                                    $rewards[] = 'Vật phẩm';
                                }
                                
                                // Commit transaction
                                $conn1->commit();
                                
                                // Update session variables
                                if ($giftcodeData['xu'] > 0 || $giftcodeData['luong'] > 0 || $giftcodeData['luongLock'] > 0) {
                                    // Get updated user data
                                    $stmt = $conn->prepare("SELECT xu, luong, luongkhoa FROM team_user WHERE id = :user_id");
                                    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
                                    $stmt->execute();
                                    $userData = $stmt->fetch(PDO::FETCH_ASSOC);
                                    
                                    // Update session
                                    $_SESSION['xu'] = $userData['xu'];
                                    $_SESSION['luong'] = $userData['luong'];
                                    $_SESSION['luongkhoa'] = $userData['luongkhoa'];
                                }
                                
                                $message = 'Sử dụng giftcode thành công! Bạn nhận được: ' . implode(', ', $rewards);
                                $messageType = 'success';
                            } catch (PDOException $e) {
                                // Rollback transaction on error
                                $conn1->rollBack();
                                $message = 'Lỗi khi sử dụng giftcode: ' . $e->getMessage();
                                $messageType = 'danger';
                            }
                        }
                    }
                }
            }
        } catch (PDOException $e) {
            $message = 'Lỗi: ' . $e->getMessage();
            $messageType = 'danger';
        }
    }
}

// Get user's giftcode usage history
$usageHistory = [];
try {
    $stmt = $conn1->prepare("
        SELECT g.giftcode, g.xu, g.luong, g.luongLock, g.item, gu.used_at
        FROM giftcode_used gu
        JOIN giftcode g ON gu.giftcode_id = g.id
        WHERE gu.user_id = :user_id
        ORDER BY gu.used_at DESC
        LIMIT 10
    ");
    $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
    $stmt->execute();
    $usageHistory = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    // Handle error silently
}
?>

<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Giftcode - KPAH</title>
    <link href="../assets/homepage/css/bootstrap.min.css" rel="stylesheet" type="text/css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <script src="../assets/homepage/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/homepage/js/jquery-1.9.1.min.js"></script>
    <style>
        body {
            background-color: #1a1a1a;
            color: #f9d686;
            font-family: Arial, sans-serif;
        }

        .container {
            padding: 20px;
        }

        .header {
            background-color: #000;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border: 1px solid #f9d686;
        }

        .title {
            margin: 0;
            color: #f9d686;
            font-weight: bold;
        }

        .user-info {
            color: #fff;
        }

        .card {
            background-color: #2a2a2a;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #f9d686;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .card h2 {
            color: #f9d686;
            font-size: 1.5rem;
            margin-bottom: 15px;
            border-bottom: 1px solid #f9d686;
            padding-bottom: 10px;
        }

        .form-control {
            background-color: #333;
            border: 1px solid #444;
            color: #fff;
        }

        .form-control:focus {
            background-color: #444;
            border-color: #f9d686;
            color: #fff;
            box-shadow: 0 0 0 0.25rem rgba(249, 214, 134, 0.25);
        }

        .btn-custom {
            background-color: #f9d686;
            color: #000;
            border: none;
            font-weight: bold;
            padding: 8px 16px;
        }

        .btn-custom:hover {
            background-color: #e5c677;
            color: #000;
        }

        .table {
            color: #f9d686;
        }

        .table th {
            background-color: #333;
            color: #f9d686;
            border-color: #444;
        }

        .table td {
            border-color: #444;
        }

        .badge {
            margin-right: 5px;
        }

        .alert-success {
            background-color: rgba(40, 167, 69, 0.2);
            border-color: rgba(40, 167, 69, 0.3);
            color: #28a745;
        }

        .alert-danger {
            background-color: rgba(220, 53, 69, 0.2);
            border-color: rgba(220, 53, 69, 0.3);
            color: #dc3545;
        }

        .menu {
            background-color: #2a2a2a;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid #f9d686;
        }

        .menu ul {
            list-style: none;
            padding: 0;
            margin: 0;
            display: flex;
            flex-wrap: wrap;
        }

        .menu li {
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .menu a {
            display: block;
            padding: 8px 15px;
            background-color: #333;
            color: #f9d686;
            text-decoration: none;
            border-radius: 3px;
            border: 1px solid rgba(249, 214, 134, 0.3);
            transition: all 0.3s ease;
        }

        .menu a:hover,
        .menu a.active {
            background-color: #f9d686;
            color: #000;
        }

        .menu a i {
            margin-right: 5px;
        }

        @media (max-width: 768px) {
            .header {
                flex-direction: column;
                text-align: center;
            }

            .user-info {
                margin-top: 10px;
            }

            .menu ul {
                flex-direction: column;
            }

            .menu li {
                margin-right: 0;
                margin-bottom: 5px;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1 class="title">KPAH - Giftcode</h1>
            <div class="user-info">
                Xin chào, <strong><?php echo htmlspecialchars($username); ?></strong> |
                <a href="../Views/Account.php" class="text-light">Tài khoản</a> |
                <a href="../handle/logout.php" class="text-light">Đăng xuất</a>
            </div>
        </div>

        <div class="menu">
            <ul>
                <li><a href="../index.php"><i class="fas fa-home"></i> Trang chủ</a></li>
                <li><a href="../Views/Account.php"><i class="fas fa-user"></i> Tài khoản</a></li>
                <li><a href="../Views/topup.php"><i class="fas fa-credit-card"></i> Nạp thẻ</a></li>
                <li><a href="../Views/bank_topup.php"><i class="fas fa-university"></i> Nạp ATM/Momo</a></li>
                <li><a href="../Views/giftcode.php" class="active"><i class="fas fa-gift"></i> Giftcode</a></li>
                <li><a href="../Views/ChangePassword.php"><i class="fas fa-key"></i> Đổi mật khẩu</a></li>
            </ul>
        </div>

        <?php if (!empty($message)): ?>
            <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                <?php echo $message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <h2><i class="fas fa-gift"></i> Nhập Giftcode</h2>
                    <form method="post" action="">
                        <div class="mb-3">
                            <label for="giftcode" class="form-label">Mã Giftcode</label>
                            <input type="text" class="form-control" id="giftcode" name="giftcode" placeholder="Nhập mã giftcode" required>
                        </div>
                        <button type="submit" name="submit_giftcode" class="btn btn-custom">
                            <i class="fas fa-check"></i> Sử dụng Giftcode
                        </button>
                    </form>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <h2><i class="fas fa-info-circle"></i> Hướng dẫn</h2>
                    <p>Giftcode là mã quà tặng đặc biệt, bạn có thể nhận được từ các sự kiện, hoạt động của game.</p>
                    <p>Mỗi giftcode chỉ có thể sử dụng một lần và có thể có thời hạn sử dụng.</p>
                    <p>Phần thưởng từ giftcode có thể bao gồm: Xu, Lượng, Lượng khóa và các vật phẩm đặc biệt.</p>
                    <p>Theo dõi fanpage và tham gia các sự kiện của game để nhận được nhiều giftcode hấp dẫn!</p>
                </div>
            </div>
        </div>

        <div class="card">
            <h2><i class="fas fa-history"></i> Lịch sử sử dụng Giftcode</h2>
            <?php if (empty($usageHistory)): ?>
                <div class="alert alert-info">Bạn chưa sử dụng giftcode nào.</div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Giftcode</th>
                                <th>Phần thưởng</th>
                                <th>Thời gian sử dụng</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($usageHistory as $usage): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($usage['giftcode']); ?></td>
                                    <td>
                                        <?php if ($usage['xu'] > 0): ?>
                                            <span class="badge bg-success"><?php echo number_format($usage['xu']); ?> Xu</span>
                                        <?php endif; ?>
                                        <?php if ($usage['luong'] > 0): ?>
                                            <span class="badge bg-primary"><?php echo number_format($usage['luong']); ?> Lượng</span>
                                        <?php endif; ?>
                                        <?php if ($usage['luongLock'] > 0): ?>
                                            <span class="badge bg-info"><?php echo number_format($usage['luongLock']); ?> Lượng khóa</span>
                                        <?php endif; ?>
                                        <?php if (!empty($usage['item'])): ?>
                                            <span class="badge bg-warning">Vật phẩm</span>
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo date('d/m/Y H:i:s', strtotime($usage['used_at'])); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            // Auto-dismiss alerts after 5 seconds
            setTimeout(function() {
                $('.alert').alert('close');
            }, 5000);
        });
    </script>
</body>

</html>
