/* Main CSS file for KPAH website */

/* Global styles */
:root {
    --primary-color: #f9d686;
    --primary-dark: #e5c677;
    --secondary-color: #343a40;
    --text-color: #f8f9fa;
    --bg-dark: rgba(0, 0, 0, 0.7);
    --bg-darker: rgba(0, 0, 0, 0.8);
    --border-color: rgba(249, 214, 134, 0.3);
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
}

body {
    background-image: url('../homepage/images/bg-page1.jpg');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    color: var(--primary-color);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Container styles */
.main-container {
    background-color: var(--bg-dark);
    border-radius: 10px;
    padding: 20px;
    margin-top: 30px;
    margin-bottom: 30px;
    border: 1px solid var(--primary-color);
    box-shadow: 0 0 15px var(--border-color);
}

/* Header styles */
.main-header {
    text-align: center;
    margin-bottom: 30px;
    border-bottom: 1px solid var(--primary-color);
    padding-bottom: 15px;
}

.main-header h1 {
    color: var(--primary-color);
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

/* Section styles */
.content-section {
    margin-bottom: 30px;
    padding: 20px;
    background-color: var(--bg-darker);
    border-radius: 8px;
    border: 1px solid var(--primary-color);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.content-section:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px var(--border-color);
}

.section-title {
    color: var(--primary-color);
    font-size: 1.5rem;
    margin-bottom: 20px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    position: relative;
    padding-bottom: 10px;
}

.section-title:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-dark));
    border-radius: 3px;
}

/* Form styles */
.custom-form {
    background-color: var(--bg-darker);
    padding: 20px;
    border-radius: 8px;
    border: 1px solid var(--primary-color);
}

.form-label {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 8px;
}

.form-control {
    background-color: rgba(255, 255, 255, 0.9);
    border: 1px solid var(--primary-color);
    border-radius: 6px;
    padding: 10px 15px;
    color: var(--secondary-color);
    transition: all 0.3s ease;
}

.form-control:focus {
    background-color: #fff;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(249, 214, 134, 0.25);
}

.form-select {
    background-color: rgba(255, 255, 255, 0.9);
    border: 1px solid var(--primary-color);
    border-radius: 6px;
    padding: 10px 15px;
    color: var(--secondary-color);
    transition: all 0.3s ease;
}

.form-select:focus {
    background-color: #fff;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(249, 214, 134, 0.25);
}

.form-text {
    color: #adb5bd;
    font-size: 0.85rem;
    margin-top: 5px;
}

/* Button styles */
.btn-custom {
    background-color: var(--primary-color);
    color: var(--secondary-color);
    border: none;
    font-weight: 600;
    padding: 10px 20px;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.btn-custom:hover {
    background-color: var(--primary-dark);
    color: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.btn-custom:active {
    transform: translateY(0);
}

.btn-outline-custom {
    background-color: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    font-weight: 600;
    padding: 9px 19px;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.btn-outline-custom:hover {
    background-color: var(--primary-color);
    color: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Table styles */
.custom-table {
    width: 100%;
    margin-top: 15px;
    border-collapse: separate;
    border-spacing: 0;
    border-radius: 8px;
    overflow: hidden;
}

.custom-table th {
    background-color: rgba(249, 214, 134, 0.2);
    color: var(--primary-color);
    padding: 12px 15px;
    text-align: left;
    font-weight: 600;
    border-bottom: 1px solid rgba(249, 214, 134, 0.2);
}

.custom-table td {
    padding: 12px 15px;
    border-bottom: 1px solid rgba(249, 214, 134, 0.1);
    color: var(--text-color);
}

.custom-table tr:last-child td {
    border-bottom: none;
}

.custom-table tr:hover td {
    background-color: rgba(249, 214, 134, 0.05);
}

/* Status indicators */
.status-pending {
    color: var(--warning-color);
    font-weight: 600;
}

.status-success, .status-approved {
    color: var(--success-color);
    font-weight: 600;
}

.status-failed, .status-rejected {
    color: var(--danger-color);
    font-weight: 600;
}

/* Alert styles */
.custom-alert {
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid transparent;
}

.alert-success {
    background-color: rgba(40, 167, 69, 0.1);
    border-color: rgba(40, 167, 69, 0.2);
    color: var(--success-color);
}

.alert-warning {
    background-color: rgba(255, 193, 7, 0.1);
    border-color: rgba(255, 193, 7, 0.2);
    color: var(--warning-color);
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.1);
    border-color: rgba(220, 53, 69, 0.2);
    color: var(--danger-color);
}

.alert-info {
    background-color: rgba(23, 162, 184, 0.1);
    border-color: rgba(23, 162, 184, 0.2);
    color: var(--info-color);
}

/* Card styles */
.custom-card {
    background-color: var(--bg-darker);
    border-radius: 10px;
    border: 1px solid var(--primary-color);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
}

.custom-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.card-header {
    background-color: rgba(249, 214, 134, 0.1);
    border-bottom: 1px solid var(--primary-color);
    padding: 15px;
}

.card-body {
    padding: 20px;
}

.card-title {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 15px;
}

.card-text {
    color: var(--text-color);
    margin-bottom: 15px;
}

/* Info display */
.info-item {
    margin-bottom: 15px;
    display: flex;
    flex-wrap: wrap;
}

.info-label {
    font-weight: 600;
    width: 150px;
    color: var(--text-color);
}

.info-value {
    flex: 1;
    color: var(--primary-color);
}

/* Modal styles */
.modal-content {
    background-color: var(--bg-dark);
    border: 1px solid var(--primary-color);
    border-radius: 10px;
}

.modal-header {
    border-bottom: 1px solid var(--primary-color);
    background-color: rgba(249, 214, 134, 0.1);
}

.modal-title {
    color: var(--primary-color);
}

.modal-footer {
    border-top: 1px solid var(--primary-color);
}

/* Responsive styles */
@media (max-width: 768px) {
    .main-container {
        margin-top: 15px;
        padding: 15px;
    }

    .content-section {
        padding: 15px;
    }

    .info-label {
        width: 100%;
        margin-bottom: 5px;
    }

    .info-value {
        width: 100%;
    }
    
    .custom-table {
        display: block;
        overflow-x: auto;
        white-space: nowrap;
    }
}

@media (max-width: 576px) {
    .section-title {
        font-size: 1.3rem;
    }
    
    .btn-custom, .btn-outline-custom {
        padding: 8px 16px;
        font-size: 0.9rem;
    }
    
    .form-label {
        font-size: 0.9rem;
    }
    
    .custom-table th,
    .custom-table td {
        padding: 8px 10px;
        font-size: 0.9rem;
    }
}
