<?php
// <PERSON><PERSON><PERSON> hiển thị lỗi để debug
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Hàm ghi log debug
function debugLog($message) {
    error_log("[DB_CHECK] " . $message);
}

// Database connection
require_once 'config/database.php';
try {
    $conn = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    debugLog("Connected to database successfully");

    // Check if bank_accounts table exists
    $stmt = $conn->query("SHOW TABLES LIKE 'bank_accounts'");
    if ($stmt->rowCount() == 0) {
        debugLog("Creating bank_accounts table");
        $sql = "CREATE TABLE bank_accounts (
            id INT(11) NOT NULL AUTO_INCREMENT,
            bank_name VARCHAR(255) NOT NULL,
            account_name VARCHAR(255) NOT NULL,
            account_number VARCHAR(50) NOT NULL,
            is_active TINYINT(1) NOT NULL DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id)
        )";
        $conn->exec($sql);
        debugLog("bank_accounts table created successfully");
        
        // Insert sample bank account
        $sql = "INSERT INTO bank_accounts (bank_name, account_name, account_number, is_active) 
                VALUES ('Vietcombank', 'NGUYEN VAN A', '**********', 1)";
        $conn->exec($sql);
        debugLog("Sample bank account added");
    } else {
        debugLog("bank_accounts table already exists");
    }

    // Check if bank_transactions table exists
    $stmt = $conn->query("SHOW TABLES LIKE 'bank_transactions'");
    if ($stmt->rowCount() == 0) {
        debugLog("Creating bank_transactions table");
        $sql = "CREATE TABLE bank_transactions (
            id INT(11) NOT NULL AUTO_INCREMENT,
            user_id INT(11) NOT NULL,
            bank_account_id INT(11) NOT NULL,
            amount DECIMAL(15,2) NOT NULL,
            xu_amount INT(11) NOT NULL DEFAULT 0,
            transaction_code VARCHAR(255),
            status ENUM('pending', 'approved', 'rejected') NOT NULL DEFAULT 'pending',
            notes TEXT,
            transaction_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY user_id (user_id),
            KEY bank_account_id (bank_account_id)
        )";
        $conn->exec($sql);
        debugLog("bank_transactions table created successfully");
    } else {
        debugLog("bank_transactions table already exists");
    }

    echo "Database check completed successfully.";
} catch(PDOException $e) {
    debugLog("Database error: " . $e->getMessage());
    echo "Database error: " . $e->getMessage();
}
?>