<?php
// Start the session at the beginning of the file
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['team_user']) || empty($_SESSION['team_user']) || !isset($_SESSION['is_admin']) || !$_SESSION['is_admin']) {
    header("Location: ../index.php?error=" . urlencode('Bạn không có quyền truy cập trang này.'));
    exit();
}

// Include database configuration
require_once '../Controllers/database.php';

// Connect to database
try {
    $conn = connectPDO($config1);
} catch (PDOException $e) {
    die("Lỗi kết nối cơ sở dữ liệu: " . $e->getMessage());
}

// Get user information
$userId = $_SESSION['id'];
$username = $_SESSION['team_user'];

// Handle form submissions
$message = '';
$success = false;

// Handle API deletion
if (isset($_GET['action']) && $_GET['action'] == 'delete' && isset($_GET['id'])) {
    $apiId = (int)$_GET['id'];

    try {
        $stmt = $conn->prepare("DELETE FROM card_api_config WHERE id = :id");
        $stmt->bindParam(':id', $apiId, PDO::PARAM_INT);
        $stmt->execute();

        $message = 'Đã xóa cấu hình API thành công!';
        $success = true;
    } catch (PDOException $e) {
        $message = 'Lỗi khi xóa cấu hình API: ' . $e->getMessage();
        $success = false;
    }
}

// Handle API activation/deactivation
if (isset($_GET['action']) && $_GET['action'] == 'toggle' && isset($_GET['id'])) {
    $apiId = (int)$_GET['id'];

    try {
        // Get current status
        $stmt = $conn->prepare("SELECT is_active FROM card_api_config WHERE id = :id");
        $stmt->bindParam(':id', $apiId, PDO::PARAM_INT);
        $stmt->execute();
        $currentStatus = $stmt->fetchColumn();

        // Toggle status
        $newStatus = $currentStatus ? 0 : 1;

        $stmt = $conn->prepare("UPDATE card_api_config SET is_active = :status WHERE id = :id");
        $stmt->bindParam(':status', $newStatus, PDO::PARAM_INT);
        $stmt->bindParam(':id', $apiId, PDO::PARAM_INT);
        $stmt->execute();

        $message = 'Đã ' . ($newStatus ? 'kích hoạt' : 'vô hiệu hóa') . ' cấu hình API thành công!';
        $success = true;
    } catch (PDOException $e) {
        $message = 'Lỗi khi cập nhật trạng thái API: ' . $e->getMessage();
        $success = false;
    }
}

// Handle API addition
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_api'])) {
    $apiName = $_POST['api_name'];
    $apiUrl = $_POST['api_url'];
    $merchantId = $_POST['merchant_id'];
    $apiKey = $_POST['api_key'];
    $secretKey = $_POST['secret_key'];
    $callbackUrl = $_POST['callback_url'];
    $requestMethod = $_POST['request_method'];
    $isActive = isset($_POST['is_active']) ? 1 : 0;

    try {
        // Check if API with the same name already exists
        $stmt = $conn->prepare("SELECT COUNT(*) FROM card_api_config WHERE api_name = :api_name");
        $stmt->bindParam(':api_name', $apiName, PDO::PARAM_STR);
        $stmt->execute();
        $exists = $stmt->fetchColumn();

        if ($exists) {
            $message = 'Cấu hình API với tên "' . htmlspecialchars($apiName) . '" đã tồn tại!';
            $success = false;
        } else {
            // Insert new API configuration
            $stmt = $conn->prepare("INSERT INTO card_api_config (
                api_name, api_url, merchant_id, api_key, secret_key, callback_url, request_method, is_active
            ) VALUES (
                :api_name, :api_url, :merchant_id, :api_key, :secret_key, :callback_url, :request_method, :is_active
            )");

            $stmt->bindParam(':api_name', $apiName, PDO::PARAM_STR);
            $stmt->bindParam(':api_url', $apiUrl, PDO::PARAM_STR);
            $stmt->bindParam(':merchant_id', $merchantId, PDO::PARAM_STR);
            $stmt->bindParam(':api_key', $apiKey, PDO::PARAM_STR);
            $stmt->bindParam(':secret_key', $secretKey, PDO::PARAM_STR);
            $stmt->bindParam(':callback_url', $callbackUrl, PDO::PARAM_STR);
            $stmt->bindParam(':request_method', $requestMethod, PDO::PARAM_STR);
            $stmt->bindParam(':is_active', $isActive, PDO::PARAM_INT);

            $stmt->execute();

            $message = 'Đã thêm cấu hình API mới thành công!';
            $success = true;
        }
    } catch (PDOException $e) {
        $message = 'Lỗi khi thêm cấu hình API: ' . $e->getMessage();
        $success = false;
    }
}

// Handle API update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_api'])) {
    $apiId = (int)$_POST['api_id'];
    $apiName = $_POST['api_name'];
    $apiUrl = $_POST['api_url'];
    $merchantId = $_POST['merchant_id'];
    $apiKey = $_POST['api_key'];
    $secretKey = $_POST['secret_key'];
    $callbackUrl = $_POST['callback_url'];
    $requestMethod = $_POST['request_method'];
    $isActive = isset($_POST['is_active']) ? 1 : 0;

    try {
        // Update API configuration
        $stmt = $conn->prepare("UPDATE card_api_config SET 
            api_name = :api_name,
            api_url = :api_url,
            merchant_id = :merchant_id,
            api_key = :api_key,
            secret_key = :secret_key,
            callback_url = :callback_url,
            request_method = :request_method,
            is_active = :is_active
            WHERE id = :id");

        $stmt->bindParam(':api_name', $apiName, PDO::PARAM_STR);
        $stmt->bindParam(':api_url', $apiUrl, PDO::PARAM_STR);
        $stmt->bindParam(':merchant_id', $merchantId, PDO::PARAM_STR);
        $stmt->bindParam(':api_key', $apiKey, PDO::PARAM_STR);
        $stmt->bindParam(':secret_key', $secretKey, PDO::PARAM_STR);
        $stmt->bindParam(':callback_url', $callbackUrl, PDO::PARAM_STR);
        $stmt->bindParam(':request_method', $requestMethod, PDO::PARAM_STR);
        $stmt->bindParam(':is_active', $isActive, PDO::PARAM_INT);
        $stmt->bindParam(':id', $apiId, PDO::PARAM_INT);

        $stmt->execute();

        $message = 'Đã cập nhật cấu hình API thành công!';
        $success = true;
    } catch (PDOException $e) {
        $message = 'Lỗi khi cập nhật cấu hình API: ' . $e->getMessage();
        $success = false;
    }
}

// Get API configurations
$apiConfigs = [];
try {
    $stmt = $conn->query("SELECT * FROM card_api_config ORDER BY api_name ASC");
    $apiConfigs = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $message = 'Lỗi khi lấy danh sách cấu hình API: ' . $e->getMessage();
    $success = false;
}

// Get API configuration for editing
$editApi = null;
if (isset($_GET['action']) && $_GET['action'] == 'edit' && isset($_GET['id'])) {
    $apiId = (int)$_GET['id'];

    try {
        $stmt = $conn->prepare("SELECT * FROM card_api_config WHERE id = :id");
        $stmt->bindParam(':id', $apiId, PDO::PARAM_INT);
        $stmt->execute();
        $editApi = $stmt->fetch(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        $message = 'Lỗi khi lấy thông tin API: ' . $e->getMessage();
        $success = false;
    }
}
?>

<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quản lý API - KPAH</title>
    <link href="../assets/homepage/css/bootstrap.min.css" rel="stylesheet" type="text/css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <script src="../assets/homepage/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/homepage/js/jquery-1.9.1.min.js"></script>
    <style>
        body {
            background-color: #1a1a1a;
            color: #f9d686;
            font-family: Arial, sans-serif;
        }

        .admin-container {
            padding: 20px;
        }

        .admin-header {
            background-color: #000;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border: 1px solid #f9d686;
        }

        .admin-title {
            margin: 0;
            color: #f9d686;
            font-weight: bold;
        }

        .admin-user {
            color: #fff;
        }

        .admin-card {
            background-color: #2a2a2a;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #f9d686;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .admin-card h2 {
            color: #f9d686;
            font-size: 1.5rem;
            margin-bottom: 15px;
            border-bottom: 1px solid #f9d686;
            padding-bottom: 10px;
        }

        .admin-table {
            width: 100%;
            margin-top: 15px;
        }

        .admin-table th {
            background-color: #333;
            color: #f9d686;
            padding: 10px;
            text-align: left;
        }

        .admin-table td {
            padding: 10px;
            border-bottom: 1px solid #444;
        }

        .admin-table tr:hover td {
            background-color: #3a3a3a;
        }

        .btn-admin {
            background-color: #f9d686;
            color: #000;
            border: none;
            font-weight: bold;
            padding: 8px 16px;
            margin-right: 5px;
        }

        .btn-admin:hover {
            background-color: #e5c677;
            color: #000;
        }

        .btn-danger {
            background-color: #dc3545;
            color: #fff;
        }

        .btn-danger:hover {
            background-color: #c82333;
            color: #fff;
        }

        .admin-menu {
            background-color: #2a2a2a;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid #f9d686;
        }

        .admin-menu ul {
            list-style: none;
            padding: 0;
            margin: 0;
            display: flex;
            flex-wrap: wrap;
        }

        .admin-menu li {
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .admin-menu a {
            display: block;
            padding: 8px 15px;
            background-color: #333;
            color: #f9d686;
            text-decoration: none;
            border-radius: 3px;
            border: 1px solid rgba(249, 214, 134, 0.3);
            transition: all 0.3s ease;
        }

        .admin-menu a:hover,
        .admin-menu a.active {
            background-color: #f9d686;
            color: #000;
        }

        .admin-menu a i {
            margin-right: 5px;
        }

        .form-control {
            background-color: #333;
            border: 1px solid #444;
            color: #fff;
        }

        .form-control:focus {
            background-color: #444;
            border-color: #f9d686;
            color: #fff;
            box-shadow: 0 0 0 0.25rem rgba(249, 214, 134, 0.25);
        }

        .form-label {
            color: #f9d686;
        }

        .form-text {
            color: #aaa;
        }

        .form-check-input:checked {
            background-color: #f9d686;
            border-color: #f9d686;
        }

        .status-active {
            color: #28a745;
            font-weight: bold;
        }

        .status-inactive {
            color: #dc3545;
            font-weight: bold;
        }

        .alert-success {
            background-color: rgba(40, 167, 69, 0.2);
            border-color: rgba(40, 167, 69, 0.3);
            color: #28a745;
        }

        .alert-danger {
            background-color: rgba(220, 53, 69, 0.2);
            border-color: rgba(220, 53, 69, 0.3);
            color: #dc3545;
        }

        @media (max-width: 768px) {
            .admin-header {
                flex-direction: column;
                text-align: center;
            }

            .admin-user {
                margin-top: 10px;
            }

            .admin-menu ul {
                flex-direction: column;
            }

            .admin-menu li {
                margin-right: 0;
                margin-bottom: 5px;
            }
        }
    </style>
</head>

<body>
    <div class="container admin-container">
        <div class="admin-header">
            <h1 class="admin-title">KPAH - Quản lý API</h1>
            <div class="admin-user">
                Xin chào, <strong><?php echo htmlspecialchars($username); ?></strong> |
                <a href="../Views/Account.php" class="text-light">Tài khoản</a> |
                <a href="../handle/logout.php" class="text-light">Đăng xuất</a>
            </div>
        </div>

        <div class="admin-menu">
            <ul>
                <li><a href="index.php"><i class="fas fa-tachometer-alt"></i> Tổng quan</a></li>
                <li><a href="users.php"><i class="fas fa-users"></i> Quản lý người dùng</a></li>
                <li><a href="bank_topups.php"><i class="fas fa-university"></i> Quản lý nạp ATM</a></li>
                <li><a href="bank_accounts.php"><i class="fas fa-credit-card"></i> Quản lý tài khoản ngân hàng</a></li>
                <li><a href="activations.php"><i class="fas fa-check-circle"></i> Kích hoạt tài khoản</a></li>
                <li><a href="giftcodes.php"><i class="fas fa-gift"></i> Quản lý Giftcode</a></li>
                <li><a href="api_management.php" class="active"><i class="fas fa-plug"></i> Quản lý API</a></li>
                <li><a href="password_decoder.php"><i class="fas fa-key"></i> Giải mã mật khẩu</a></li>
                <li><a href="site_settings.php"><i class="fas fa-cogs"></i> Cấu hình trang web</a></li>
                <li><a href="../index.php"><i class="fas fa-home"></i> Về trang chủ</a></li>
            </ul>
        </div>

        <?php if (!empty($message)): ?>
            <div class="alert <?php echo $success ? 'alert-success' : 'alert-danger'; ?> alert-dismissible fade show" role="alert">
                <?php echo $message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <div class="row">
            <div class="col-md-12">
                <div class="admin-card">
                    <h2><i class="fas fa-plug"></i> Danh sách cấu hình API</h2>
                    <?php if (empty($apiConfigs)): ?>
                        <p>Chưa có cấu hình API nào.</p>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="admin-table">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Tên API</th>
                                        <th>URL API</th>
                                        <th>Partner ID</th>
                                        <th>Callback URL</th>
                                        <th>Phương thức</th>
                                        <th>Trạng thái</th>
                                        <th>Thao tác</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($apiConfigs as $api): ?>
                                        <tr>
                                            <td><?php echo $api['id']; ?></td>
                                            <td><?php echo htmlspecialchars($api['api_name']); ?></td>
                                            <td><?php echo htmlspecialchars($api['api_url']); ?></td>
                                            <td><?php echo htmlspecialchars($api['merchant_id']); ?></td>
                                            <td><?php echo htmlspecialchars($api['callback_url']); ?></td>
                                            <td><?php echo htmlspecialchars($api['request_method']); ?></td>
                                            <td>
                                                <?php if ($api['is_active']): ?>
                                                    <span class="status-active">Đang hoạt động</span>
                                                <?php else: ?>
                                                    <span class="status-inactive">Đã vô hiệu hóa</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <a href="?action=edit&id=<?php echo $api['id']; ?>" class="btn btn-sm btn-admin">
                                                    <i class="fas fa-edit"></i> Sửa
                                                </a>
                                                <a href="?action=toggle&id=<?php echo $api['id']; ?>" class="btn btn-sm btn-admin">
                                                    <?php if ($api['is_active']): ?>
                                                        <i class="fas fa-ban"></i> Vô hiệu hóa
                                                    <?php else: ?>
                                                        <i class="fas fa-check"></i> Kích hoạt
                                                    <?php endif; ?>
                                                </a>
                                                <a href="?action=delete&id=<?php echo $api['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('Bạn có chắc chắn muốn xóa cấu hình API này?');">
                                                    <i class="fas fa-trash"></i> Xóa
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-12">
                <div class="admin-card">
                    <h2>
                        <?php if ($editApi): ?>
                            <i class="fas fa-edit"></i> Sửa cấu hình API
                        <?php else: ?>
                            <i class="fas fa-plus"></i> Thêm cấu hình API mới
                        <?php endif; ?>
                    </h2>
                    <form method="post" action="">
                        <?php if ($editApi): ?>
                            <input type="hidden" name="api_id" value="<?php echo $editApi['id']; ?>">
                        <?php endif; ?>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="api_name" class="form-label">Tên API</label>
                                <input type="text" class="form-control" id="api_name" name="api_name" value="<?php echo $editApi ? htmlspecialchars($editApi['api_name']) : ''; ?>" required>
                                <div class="form-text">Tên nhận dạng API (ví dụ: THESIEURE, CARDVIP)</div>
                            </div>

                            <div class="col-md-6">
                                <label for="api_url" class="form-label">URL API</label>
                                <input type="text" class="form-control" id="api_url" name="api_url" value="<?php echo $editApi ? htmlspecialchars($editApi['api_url']) : ''; ?>" required>
                                <div class="form-text">URL endpoint của API</div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label for="merchant_id" class="form-label">Partner ID / Merchant ID</label>
                                <input type="text" class="form-control" id="merchant_id" name="merchant_id" value="<?php echo $editApi ? htmlspecialchars($editApi['merchant_id']) : ''; ?>">
                                <div class="form-text">ID đối tác của bạn</div>
                            </div>

                            <div class="col-md-4">
                                <label for="api_key" class="form-label">API Key</label>
                                <input type="text" class="form-control" id="api_key" name="api_key" value="<?php echo $editApi ? htmlspecialchars($editApi['api_key']) : ''; ?>">
                                <div class="form-text">Khóa API (nếu có)</div>
                            </div>

                            <div class="col-md-4">
                                <label for="secret_key" class="form-label">Secret Key / Partner Key</label>
                                <input type="text" class="form-control" id="secret_key" name="secret_key" value="<?php echo $editApi ? htmlspecialchars($editApi['secret_key']) : ''; ?>">
                                <div class="form-text">Khóa bí mật để tạo chữ ký</div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-8">
                                <label for="callback_url" class="form-label">Callback URL</label>
                                <input type="text" class="form-control" id="callback_url" name="callback_url" value="<?php echo $editApi ? htmlspecialchars($editApi['callback_url']) : 'http://yourdomain.com/api/card_callback.php'; ?>">
                                <div class="form-text">URL callback để nhận kết quả từ API</div>
                            </div>

                            <div class="col-md-4">
                                <label for="request_method" class="form-label">Phương thức request</label>
                                <select class="form-control" id="request_method" name="request_method">
                                    <option value="POST" <?php echo ($editApi && $editApi['request_method'] == 'POST') ? 'selected' : ''; ?>>POST</option>
                                    <option value="GET" <?php echo ($editApi && $editApi['request_method'] == 'GET') ? 'selected' : ''; ?>>GET</option>
                                </select>
                                <div class="form-text">Phương thức gửi request đến API</div>
                            </div>
                        </div>

                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="is_active" name="is_active" <?php echo (!$editApi || $editApi['is_active']) ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="is_active">Kích hoạt API</label>
                        </div>

                        <button type="submit" name="<?php echo $editApi ? 'update_api' : 'add_api'; ?>" class="btn btn-admin">
                            <?php echo $editApi ? 'Cập nhật API' : 'Thêm API mới'; ?>
                        </button>

                        <?php if ($editApi): ?>
                            <a href="api_management.php" class="btn btn-danger">Hủy</a>
                        <?php endif; ?>
                    </form>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-12">
                <div class="admin-card">
                    <h2><i class="fas fa-info-circle"></i> Hướng dẫn cấu hình API</h2>

                    <div class="mb-4">
                        <h4>TheSieuRe.com</h4>
                        <ul>
                            <li><strong>Tên API:</strong> THESIEURE</li>
                            <li><strong>URL API:</strong> https://thesieure.com/chargingws/v2</li>
                            <li><strong>Partner ID:</strong> ID đối tác của bạn trên TheSieuRe.com</li>
                            <li><strong>Secret Key:</strong> Khóa bí mật của bạn trên TheSieuRe.com</li>
                            <li><strong>Callback URL:</strong> URL callback để nhận kết quả từ TheSieuRe.com (ví dụ: http://yourdomain.com/api/card_callback.php)</li>
                            <li><strong>Phương thức request:</strong> POST</li>
                        </ul>
                    </div>

                    <div class="mb-4">
                        <h4>CardVIP</h4>
                        <ul>
                            <li><strong>Tên API:</strong> CARDVIP</li>
                            <li><strong>URL API:</strong> https://api.cardvip.vn/api/createExchange</li>
                            <li><strong>Partner ID:</strong> ID đối tác của bạn trên CardVIP</li>
                            <li><strong>API Key:</strong> API Key của bạn trên CardVIP</li>
                            <li><strong>Secret Key:</strong> Khóa bí mật của bạn trên CardVIP</li>
                            <li><strong>Callback URL:</strong> URL callback để nhận kết quả từ CardVIP (ví dụ: http://yourdomain.com/api/card_callback.php)</li>
                            <li><strong>Phương thức request:</strong> POST</li>
                        </ul>
                    </div>

                    <div class="alert alert-warning">
                        <strong>Lưu ý:</strong> Sau khi cấu hình API, bạn cần thông báo cho nhà cung cấp API về URL callback của bạn để họ có thể gửi kết quả về đúng địa chỉ.
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            // Auto-hide alerts after 5 seconds
            setTimeout(function() {
                $('.alert').alert('close');
            }, 5000);
        });
    </script>
</body>

</html>