<?php
// Include database configuration
require_once 'Controllers/database.php';

// Connect to database
try {
    $conn = connectPDO($config1);
    echo "Connected to database successfully.<br>";
} catch (PDOException $e) {
    die("Connection failed: " . $e->getMessage());
}

// Create card_transactions table
try {
    $sql = "CREATE TABLE IF NOT EXISTS card_transactions (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        user_id INT(11) NOT NULL,
        card_type VARCHAR(50) NOT NULL,
        card_serial VARCHAR(50) NOT NULL,
        card_number VARCHAR(50) NOT NULL,
        amount INT(11) NOT NULL,
        xu_amount INT(11) NOT NULL DEFAULT 0,
        status VARCHAR(20) NOT NULL DEFAULT 'pending',
        message TEXT,
        transaction_id VARCHAR(100),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES team_user(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    $conn->exec($sql);
    echo "Table card_transactions created successfully.<br>";
} catch(PDOException $e) {
    echo "Error creating table: " . $e->getMessage() . "<br>";
}

// Create card_rates table for storing card rates
try {
    $sql = "CREATE TABLE IF NOT EXISTS card_rates (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        card_type VARCHAR(50) NOT NULL,
        amount INT(11) NOT NULL,
        rate FLOAT NOT NULL DEFAULT 1.0,
        xu_rate INT(11) NOT NULL DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY card_amount_unique (card_type, amount)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    $conn->exec($sql);
    echo "Table card_rates created successfully.<br>";
    
    // Insert default card rates if table is empty
    $checkSql = "SELECT COUNT(*) FROM card_rates";
    $count = $conn->query($checkSql)->fetchColumn();
    
    if ($count == 0) {
        // Define card types
        $cardTypes = ['VIETTEL', 'MOBIFONE', 'VINAPHONE', 'VIETNAMOBILE', 'ZING', 'GATE'];
        
        // Define amounts
        $amounts = [10000, 20000, 30000, 50000, 100000, 200000, 300000, 500000, 1000000];
        
        // Insert rates for each card type and amount
        $insertSql = "INSERT INTO card_rates (card_type, amount, rate, xu_rate) VALUES (:card_type, :amount, :rate, :xu_rate)";
        $stmt = $conn->prepare($insertSql);
        
        foreach ($cardTypes as $cardType) {
            foreach ($amounts as $amount) {
                // Default rate is 0.8 (80%)
                $rate = 0.8;
                
                // Calculate xu rate (1 VND = 1 xu)
                $xuRate = floor($amount * $rate);
                
                $stmt->bindParam(':card_type', $cardType, PDO::PARAM_STR);
                $stmt->bindParam(':amount', $amount, PDO::PARAM_INT);
                $stmt->bindParam(':rate', $rate, PDO::PARAM_STR);
                $stmt->bindParam(':xu_rate', $xuRate, PDO::PARAM_INT);
                $stmt->execute();
            }
        }
        
        echo "Default card rates inserted successfully.<br>";
    }
} catch(PDOException $e) {
    echo "Error creating card_rates table: " . $e->getMessage() . "<br>";
}

// Create card_api_config table for storing API configuration
try {
    $sql = "CREATE TABLE IF NOT EXISTS card_api_config (
        id INT(11) AUTO_INCREMENT PRIMARY KEY,
        api_name VARCHAR(50) NOT NULL,
        api_url VARCHAR(255) NOT NULL,
        merchant_id VARCHAR(100),
        api_key VARCHAR(255),
        secret_key VARCHAR(255),
        callback_url VARCHAR(255),
        is_active TINYINT(1) NOT NULL DEFAULT 1,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    $conn->exec($sql);
    echo "Table card_api_config created successfully.<br>";
    
    // Insert default API configuration if table is empty
    $checkSql = "SELECT COUNT(*) FROM card_api_config";
    $count = $conn->query($checkSql)->fetchColumn();
    
    if ($count == 0) {
        $insertSql = "INSERT INTO card_api_config (api_name, api_url, merchant_id, api_key, secret_key, callback_url, is_active) 
                      VALUES ('CARDVIP', 'https://api.cardvip.vn/api/createExchange', '', '', '', 'https://yourdomain.com/api/card_callback.php', 1)";
        $conn->exec($insertSql);
        echo "Default API configuration inserted successfully.<br>";
    }
} catch(PDOException $e) {
    echo "Error creating card_api_config table: " . $e->getMessage() . "<br>";
}

echo "All tables created successfully.";
?>
