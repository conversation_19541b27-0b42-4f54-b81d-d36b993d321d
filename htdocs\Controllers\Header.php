<?php
// Khởi động session nếu chưa được khởi động
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Include database configuration
require_once __DIR__ . '/database.php';

// Thiết lập biến toàn cục cho bảo trì
$_AuthLog = 0; // 0: Cho phép đăng nhập, 1: <PERSON><PERSON><PERSON> trì đăng nhập
$_ThongBao = ''; // Biến lưu thông báo

// Khởi tạo kết nối PDO
try {
    $conn = connectPDO($config1);
    $conn2 = connectPDO($config2);
} catch (PDOException $e) {
    die("Lỗi kết nối cơ sở dữ liệu: " . $e->getMessage());
}

// Kiểm tra trạng thái đăng nhập
$isLoggedIn = isset($_SESSION['team_user']) && !empty($_SESSION['team_user']);
$username = $isLoggedIn ? $_SESSION['team_user'] : '';

// Hàm kiểm tra quyền admin
function isAdmin() {
    return isset($_SESSION['is_admin']) && $_SESSION['is_admin'] === true;
}

// Hàm lấy số dư tài khoản
function getUserBalance($userId) {
    global $conn;
    try {
        $stmt = $conn->prepare("SELECT game_coins, cash_coins FROM user_balance WHERE user_id = :user_id");
        $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } else {
            // Tạo bản ghi mới nếu chưa tồn tại
            $stmt = $conn->prepare("INSERT INTO user_balance (user_id, username, game_coins, cash_coins) VALUES (:user_id, :username, 0, 0)");
            $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
            $stmt->bindParam(':username', $_SESSION['team_user'], PDO::PARAM_STR);
            $stmt->execute();

            return ['game_coins' => 0, 'cash_coins' => 0];
        }
    } catch(PDOException $e) {
        error_log("Lỗi khi lấy số dư tài khoản: " . $e->getMessage());
        return ['game_coins' => 0, 'cash_coins' => 0];
    }
}

// Lấy số dư tài khoản nếu đã đăng nhập
$userBalance = $isLoggedIn && isset($_SESSION['user_id']) ? getUserBalance($_SESSION['user_id']) : ['game_coins' => 0, 'cash_coins' => 0];

// Hàm kiểm tra trạng thái kích hoạt
function getActivationStatus($userId) {
    global $conn2;
    try {
        $stmt = $conn2->prepare("SELECT time_end FROM 5h_active WHERE userID = :user_id ORDER BY id DESC LIMIT 1");
        $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            return $result['time_end'];
        } else {
            return 0; // Không tìm thấy bản ghi
        }
    } catch(PDOException $e) {
        error_log("Lỗi khi kiểm tra trạng thái kích hoạt: " . $e->getMessage());
        return 0;
    }
}

// Kiểm tra trạng thái kích hoạt nếu đã đăng nhập
$activationStatus = $isLoggedIn && isset($_SESSION['user_id']) ? getActivationStatus($_SESSION['user_id']) : 0;

// Chỉ bao gồm phần HTML header nếu được yêu cầu
if (!defined('HEADER_ONLY')) {
?>
<?php } ?>