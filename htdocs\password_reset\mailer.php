<?php

/**
 * Email functionality for password reset
 */

require_once __DIR__ . '/config.php';
require_once __DIR__ . '/../vendor/autoload.php';

use P<PERSON>Mailer\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\Exception;

/**
 * Send a password reset email
 * 
 * @param string $email Recipient email
 * @param string $username Recipient username
 * @param string $token Reset token
 * @return bool True if email was sent successfully
 */
function sendPasswordResetEmail($email, $username, $token)
{
    global $email_config, $app_config;

    $mail = new PHPMailer(true);

    try {
        // Server settings
        $mail->isSMTP();
        $mail->Host = $email_config['host'];
        $mail->SMTPAuth = true;
        $mail->Username = $email_config['username'];
        $mail->Password = $email_config['password'];

        if ($email_config['encryption'] === 'ssl') {
            $mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS;
        } elseif ($email_config['encryption'] === 'tls') {
            $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
        }

        $mail->Port = $email_config['port'];
        $mail->CharSet = 'UTF-8';

        // TLS/SSL options
        $mail->SMTPOptions = [
            'ssl' => [
                'verify_peer' => false,
                'verify_peer_name' => false,
                'allow_self_signed' => true
            ]
        ];

        // Recipients
        $mail->setFrom($email_config['from_email'], $email_config['from_name']);
        $mail->addAddress($email, $username);
        $mail->addReplyTo($email_config['reply_to'], $email_config['reply_to_name']);

        // Create the reset link
        $resetLink = $app_config['site_url'] . '/password_reset/reset.php?token=' . $token;

        // Email content
        $mail->isHTML(true);
        $mail->Subject = 'Đặt lại mật khẩu - KPAH Game';

        // Email body
        $mail->Body = getEmailTemplate($username, $resetLink);

        // Plain text version
        $mail->AltBody = "Xin chào {$username},\n\n"
            . "Chúng tôi nhận được yêu cầu đặt lại mật khẩu cho tài khoản của bạn. "
            . "Vui lòng truy cập liên kết sau để đặt lại mật khẩu:\n\n"
            . "{$resetLink}\n\n"
            . "Liên kết này sẽ hết hạn sau {$app_config['token_expiry']} giờ.\n\n"
            . "Nếu bạn không yêu cầu đặt lại mật khẩu, vui lòng bỏ qua email này.\n\n"
            . "KPAH Game";

        // Send email
        $mail->send();
        return true;
    } catch (Exception $e) {
        error_log("Error sending password reset email: " . $mail->ErrorInfo);
        return false;
    }
}

/**
 * Get HTML email template for password reset
 * 
 * @param string $username Recipient username
 * @param string $resetLink Password reset link
 * @return string HTML email template
 */
function getEmailTemplate($username, $resetLink)
{
    global $app_config;

    return '
    <html>
    <head>
        <style>
            body {
                font-family: Arial, sans-serif;
                line-height: 1.6;
                color: #333;
                margin: 0;
                padding: 0;
            }
            .container {
                max-width: 600px;
                margin: 0 auto;
                padding: 20px;
                border: 1px solid #ddd;
                border-radius: 5px;
            }
            .header {
                background-color: #f9d686;
                color: #000;
                padding: 15px;
                text-align: center;
                border-radius: 5px 5px 0 0;
            }
            .content {
                padding: 20px;
                background-color: #fff;
            }
            .button {
                display: inline-block;
                background-color: #f9d686;
                color: #000;
                padding: 12px 24px;
                text-decoration: none;
                border-radius: 5px;
                font-weight: bold;
                margin: 20px 0;
            }
            .footer {
                text-align: center;
                margin-top: 20px;
                padding: 15px;
                font-size: 12px;
                color: #777;
                border-top: 1px solid #eee;
            }
            .note {
                background-color: #f8f9fa;
                padding: 10px;
                border-left: 4px solid #f9d686;
                margin: 15px 0;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h2>Đặt lại mật khẩu</h2>
            </div>
            <div class="content">
                <p>Xin chào <strong>' . htmlspecialchars($username) . '</strong>,</p>
                <p>Chúng tôi nhận được yêu cầu đặt lại mật khẩu cho tài khoản của bạn. Vui lòng nhấp vào nút bên dưới để đặt lại mật khẩu:</p>
                <p style="text-align: center;">
                    <a href="' . $resetLink . '" class="button">Đặt lại mật khẩu</a>
                </p>
                <p>Hoặc bạn có thể sao chép và dán liên kết sau vào trình duyệt của mình:</p>
                <div class="note">
                    <p>' . $resetLink . '</p>
                </div>
                <p>Liên kết này sẽ hết hạn sau ' . $app_config['token_expiry'] . ' giờ.</p>
                <p>Nếu bạn không yêu cầu đặt lại mật khẩu, vui lòng bỏ qua email này hoặc liên hệ với chúng tôi nếu bạn có thắc mắc.</p>
            </div>
            <div class="footer">
                <p>© ' . date('Y') . ' KPAH Game. Tất cả các quyền được bảo lưu.</p>
            </div>
        </div>
    </body>
    </html>';
}
