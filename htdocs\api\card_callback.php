<?php
// <PERSON><PERSON><PERSON> hiển thị lỗi để debug
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Hàm ghi log debug
function debugLog($message)
{
    // Sử dụng error_log thay vì ghi vào file riêng
    error_log("[CARD_CALLBACK] " . $message);
}

// Include database configuration
require_once '../Controllers/database.php';

// Include AccountManager class
require_once '../Controllers/Account.php';

// Xử lý callback từ TheSieuRe.com (GET) hoặc CardVIP (POST)

// Kiểm tra xem có dữ liệu GET không (TheSieuRe.com)
if (!empty($_GET)) {
    debugLog("Received GET callback data: " . json_encode($_GET));
    $data = $_GET;

    // Kiểm tra các trường bắt buộc cho TheSieuRe.com
    if (!isset($data['request_id']) || !isset($data['status_code'])) {
        debugLog("Missing required fields in GET data");
        http_response_code(400);
        echo json_encode(['status' => 'error', 'message' => 'Missing required fields']);
        exit;
    }
} else {
    // Xử lý dữ liệu POST (CardVIP)
    $requestData = file_get_contents('php://input');
    debugLog("Received POST callback data: " . $requestData);

    // Kiểm tra xem có dữ liệu không
    if (empty($requestData)) {
        debugLog("Empty request data");
        http_response_code(400);
        echo json_encode(['status' => 'error', 'message' => 'Empty request data']);
        exit;
    }

    // Phân tích dữ liệu JSON
    $data = json_decode($requestData, true);

    // Kiểm tra xem có phân tích được JSON không
    if ($data === null) {
        debugLog("Invalid JSON data");
        http_response_code(400);
        echo json_encode(['status' => 'error', 'message' => 'Invalid JSON data']);
        exit;
    }

    // Kiểm tra các trường bắt buộc cho CardVIP
    if (!isset($data['request_id']) || !isset($data['status'])) {
        debugLog("Missing required fields in POST data");
        http_response_code(400);
        echo json_encode(['status' => 'error', 'message' => 'Missing required fields']);
        exit;
    }
}

try {
    // Kết nối đến database
    $conn = connectPDO($config1);
    $conn1 = connectPDO($config2);

    // Khởi tạo AccountManager
    $accountManager = new AccountManager($conn, $conn1);

    // Xử lý callback
    $result = $accountManager->processCardCallback($data);

    if ($result) {
        debugLog("Callback processed successfully");
        http_response_code(200);
        echo json_encode(['status' => 'success', 'message' => 'Callback processed successfully']);
    } else {
        debugLog("Failed to process callback");
        http_response_code(500);
        echo json_encode(['status' => 'error', 'message' => 'Failed to process callback']);
    }
} catch (Exception $e) {
    debugLog("Error processing callback: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['status' => 'error', 'message' => 'Error processing callback: ' . $e->getMessage()]);
}
