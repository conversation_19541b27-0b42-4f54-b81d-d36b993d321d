<?php
// Start the session at the beginning of the file
session_start();

// Check if user is logged in and is an admin
if (!isset($_SESSION['team_user']) || empty($_SESSION['team_user']) || !isset($_SESSION['is_admin']) || !$_SESSION['is_admin']) {
    header("Location: ../index.php?error=" . urlencode('Bạn không có quyền truy cập trang này.'));
    exit();
}

// Include database configuration
require_once '../Controllers/database.php';

// Connect to databases
try {
    // Connect to account database
    $conn = connectPDO($config1);

    // Connect to game_db database
    $conn1 = connectPDO($config2);
} catch (PDOException $e) {
    die("Lỗi kết nối cơ sở dữ liệu: " . $e->getMessage());
}

// Get user information
$userId = $_SESSION['id'];
$username = $_SESSION['team_user'];

// Initialize variables
$message = '';
$messageType = '';
$searchTerm = isset($_GET['search']) ? trim($_GET['search']) : '';
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$perPage = 10;
$offset = ($page - 1) * $perPage;

// Handle user actions
if (isset($_GET['action'])) {
    $action = $_GET['action'];
    $targetUserId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

    // View user details
    if ($action === 'view' && $targetUserId > 0) {
        try {
            // Get user details
            $stmt = $conn->prepare("SELECT * FROM team_user WHERE id = :id");
            $stmt->bindParam(':id', $targetUserId, PDO::PARAM_INT);
            $stmt->execute();
            $userDetails = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$userDetails) {
                $message = 'Không tìm thấy người dùng.';
                $messageType = 'danger';
            }
        } catch (PDOException $e) {
            $message = 'Lỗi khi lấy thông tin người dùng: ' . $e->getMessage();
            $messageType = 'danger';
        }
    }

    // Ban user
    if ($action === 'ban' && $targetUserId > 0) {
        try {
            $stmt = $conn->prepare("UPDATE team_user SET ban = 1 WHERE id = :id");
            $stmt->bindParam(':id', $targetUserId, PDO::PARAM_INT);
            $stmt->execute();

            $message = 'Đã khóa tài khoản người dùng thành công.';
            $messageType = 'success';
        } catch (PDOException $e) {
            $message = 'Lỗi khi khóa tài khoản: ' . $e->getMessage();
            $messageType = 'danger';
        }
    }

    // Unban user
    if ($action === 'unban' && $targetUserId > 0) {
        try {
            $stmt = $conn->prepare("UPDATE team_user SET ban = 0 WHERE id = :id");
            $stmt->bindParam(':id', $targetUserId, PDO::PARAM_INT);
            $stmt->execute();

            $message = 'Đã mở khóa tài khoản người dùng thành công.';
            $messageType = 'success';
        } catch (PDOException $e) {
            $message = 'Lỗi khi mở khóa tài khoản: ' . $e->getMessage();
            $messageType = 'danger';
        }
    }

    // Make admin
    if ($action === 'makeadmin' && $targetUserId > 0) {
        try {
            $stmt = $conn->prepare("UPDATE team_user SET is_admin = 1 WHERE id = :id");
            $stmt->bindParam(':id', $targetUserId, PDO::PARAM_INT);
            $stmt->execute();

            $message = 'Đã cấp quyền quản trị viên cho người dùng thành công.';
            $messageType = 'success';
        } catch (PDOException $e) {
            $message = 'Lỗi khi cấp quyền quản trị viên: ' . $e->getMessage();
            $messageType = 'danger';
        }
    }

    // Remove admin
    if ($action === 'removeadmin' && $targetUserId > 0) {
        try {
            $stmt = $conn->prepare("UPDATE team_user SET is_admin = 0 WHERE id = :id");
            $stmt->bindParam(':id', $targetUserId, PDO::PARAM_INT);
            $stmt->execute();

            $message = 'Đã thu hồi quyền quản trị viên từ người dùng thành công.';
            $messageType = 'success';
        } catch (PDOException $e) {
            $message = 'Lỗi khi thu hồi quyền quản trị viên: ' . $e->getMessage();
            $messageType = 'danger';
        }
    }
}

// Get users with search and pagination
$users = [];
$totalUsers = 0;

try {
    // Build query based on search term
    $query = "SELECT * FROM team_user";
    $countQuery = "SELECT COUNT(*) FROM team_user";
    $params = [];

    if (!empty($searchTerm)) {
        $query .= " WHERE username LIKE :search OR email LIKE :search";
        $countQuery .= " WHERE username LIKE :search OR email LIKE :search";
        $params[':search'] = "%$searchTerm%";
    }

    $query .= " ORDER BY regdate DESC LIMIT :offset, :perPage";

    // Get total count for pagination
    $stmt = $conn->prepare($countQuery);
    if (!empty($searchTerm)) {
        $stmt->bindParam(':search', $params[':search'], PDO::PARAM_STR);
    }
    $stmt->execute();
    $totalUsers = $stmt->fetchColumn();

    // Get users for current page
    $stmt = $conn->prepare($query);
    if (!empty($searchTerm)) {
        $stmt->bindParam(':search', $params[':search'], PDO::PARAM_STR);
    }
    $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
    $stmt->bindParam(':perPage', $perPage, PDO::PARAM_INT);
    $stmt->execute();
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get activation status for each user
    foreach ($users as &$user) {
        try {
            $stmt = $conn1->prepare("SELECT * FROM 5h_active WHERE userID = :userId OR username = :username");
            $stmt->bindParam(':userId', $user['id'], PDO::PARAM_INT);
            $stmt->bindParam(':username', $user['username'], PDO::PARAM_STR);
            $stmt->execute();
            $activation = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($activation) {
                if ($activation['time_end'] == -1) {
                    $user['activation_status'] = 'Đã kích hoạt';
                } elseif ($activation['time_end'] > 0) {
                    $user['activation_status'] = 'Kích hoạt tạm thời';
                } else {
                    $user['activation_status'] = 'Chưa kích hoạt';
                }
            } else {
                $user['activation_status'] = 'Chưa kích hoạt';
            }
        } catch (PDOException $e) {
            $user['activation_status'] = 'Không xác định';
        }
    }
} catch (PDOException $e) {
    $message = 'Lỗi khi lấy danh sách người dùng: ' . $e->getMessage();
    $messageType = 'danger';
}

// Calculate pagination
$totalPages = ceil($totalUsers / $perPage);
?>

<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quản lý người dùng - KPAH</title>
    <link href="../assets/homepage/css/bootstrap.min.css" rel="stylesheet" type="text/css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <script src="../assets/homepage/js/bootstrap.bundle.min.js"></script>
    <script src="../assets/homepage/js/jquery-1.9.1.min.js"></script>
    <style>
        body {
            background-color: #1a1a1a;
            color: #f9d686;
            font-family: Arial, sans-serif;
        }

        .admin-container {
            padding: 20px;
        }

        .admin-header {
            background-color: #000;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border: 1px solid #f9d686;
        }

        .admin-title {
            margin: 0;
            color: #f9d686;
            font-weight: bold;
        }

        .admin-user {
            color: #fff;
        }

        .admin-card {
            background-color: #2a2a2a;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #f9d686;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .admin-card h2 {
            color: #f9d686;
            font-size: 1.5rem;
            margin-bottom: 15px;
            border-bottom: 1px solid #f9d686;
            padding-bottom: 10px;
        }

        .admin-table {
            width: 100%;
            margin-top: 15px;
        }

        .admin-table th {
            background-color: #333;
            color: #f9d686;
            padding: 10px;
            text-align: left;
        }

        .admin-table td {
            padding: 10px;
            border-bottom: 1px solid #444;
        }

        .admin-table tr:hover td {
            background-color: #3a3a3a;
        }

        .btn-admin {
            background-color: #f9d686;
            color: #000;
            border: none;
            font-weight: bold;
            padding: 8px 16px;
            margin-right: 5px;
            margin-bottom: 5px;
        }

        .btn-admin:hover {
            background-color: #e5c677;
            color: #000;
        }

        .btn-danger {
            background-color: #dc3545;
            color: #fff;
        }

        .btn-danger:hover {
            background-color: #c82333;
            color: #fff;
        }

        .admin-menu {
            background-color: #2a2a2a;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid #f9d686;
        }

        .admin-menu ul {
            list-style: none;
            padding: 0;
            margin: 0;
            display: flex;
            flex-wrap: wrap;
        }

        .admin-menu li {
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .admin-menu a {
            display: block;
            padding: 8px 15px;
            background-color: #333;
            color: #f9d686;
            text-decoration: none;
            border-radius: 3px;
            border: 1px solid rgba(249, 214, 134, 0.3);
            transition: all 0.3s ease;
        }

        .admin-menu a:hover,
        .admin-menu a.active {
            background-color: #f9d686;
            color: #000;
        }

        .admin-menu a i {
            margin-right: 5px;
        }

        .search-form {
            margin-bottom: 20px;
        }

        .search-form .form-control {
            background-color: #333;
            border: 1px solid #f9d686;
            color: #fff;
        }

        .search-form .form-control:focus {
            box-shadow: 0 0 0 0.2rem rgba(249, 214, 134, 0.25);
        }

        .pagination {
            margin-top: 20px;
            justify-content: center;
        }

        .pagination .page-item .page-link {
            background-color: #333;
            border-color: #f9d686;
            color: #f9d686;
        }

        .pagination .page-item.active .page-link {
            background-color: #f9d686;
            border-color: #f9d686;
            color: #000;
        }

        .pagination .page-item .page-link:hover {
            background-color: #444;
        }

        .user-details {
            background-color: #333;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .user-details h3 {
            color: #f9d686;
            margin-bottom: 15px;
            border-bottom: 1px solid #f9d686;
            padding-bottom: 10px;
        }

        .user-details .info-item {
            margin-bottom: 10px;
        }

        .user-details .info-label {
            font-weight: bold;
            color: #f9d686;
            width: 150px;
            display: inline-block;
        }

        .badge-admin {
            background-color: #f9d686;
            color: #000;
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 0.8rem;
            margin-left: 5px;
        }

        .badge-banned {
            background-color: #dc3545;
            color: #fff;
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 0.8rem;
            margin-left: 5px;
        }

        .badge-active {
            background-color: #28a745;
            color: #fff;
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 0.8rem;
            margin-left: 5px;
        }

        .badge-inactive {
            background-color: #6c757d;
            color: #fff;
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 0.8rem;
            margin-left: 5px;
        }

        @media (max-width: 768px) {
            .admin-header {
                flex-direction: column;
                text-align: center;
            }

            .admin-user {
                margin-top: 10px;
            }

            .admin-menu ul {
                flex-direction: column;
            }

            .admin-menu li {
                margin-right: 0;
                margin-bottom: 5px;
            }

            .btn-admin {
                display: block;
                width: 100%;
                margin-bottom: 5px;
            }
        }
    </style>
</head>

<body>
    <div class="container admin-container">
        <div class="admin-header">
            <h1 class="admin-title">KPAH - Quản lý người dùng</h1>
            <div class="admin-user">
                Xin chào, <strong><?php echo htmlspecialchars($username); ?></strong> |
                <a href="../Views/Account.php" class="text-light">Tài khoản</a> |
                <a href="../handle/logout.php" class="text-light">Đăng xuất</a>
            </div>
        </div>

        <div class="admin-menu">
            <ul>
                <li><a href="index.php"><i class="fas fa-tachometer-alt"></i> Tổng quan</a></li>
                <li><a href="users.php" class="active"><i class="fas fa-users"></i> Quản lý người dùng</a></li>
                <li><a href="bank_topups.php"><i class="fas fa-university"></i> Quản lý nạp ATM</a></li>
                <li><a href="bank_accounts.php"><i class="fas fa-credit-card"></i> Quản lý tài khoản ngân hàng</a></li>
                <li><a href="activations.php"><i class="fas fa-check-circle"></i> Kích hoạt tài khoản</a></li>
                <li><a href="giftcodes.php"><i class="fas fa-gift"></i> Quản lý Giftcode</a></li>
                <li><a href="api_management.php"><i class="fas fa-plug"></i> Quản lý API</a></li>
                <li><a href="password_decoder.php"><i class="fas fa-key"></i> Giải mã mật khẩu</a></li>
                <li><a href="site_settings.php"><i class="fas fa-cogs"></i> Cấu hình trang web</a></li>

                <li><a href="../index.php"><i class="fas fa-home"></i> Về trang chủ</a></li>
            </ul>
        </div>

        <?php if (!empty($message)): ?>
            <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show" role="alert">
                <?php echo $message; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($userDetails)): ?>
            <div class="admin-card">
                <h2><i class="fas fa-user"></i> Chi tiết người dùng</h2>
                <div class="user-details">
                    <div class="info-item">
                        <span class="info-label">ID:</span>
                        <?php echo $userDetails['id']; ?>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Tên đăng nhập:</span>
                        <?php echo htmlspecialchars($userDetails['username']); ?>
                        <?php if ($userDetails['is_admin'] == 1): ?>
                            <span class="badge-admin">Admin</span>
                        <?php endif; ?>
                        <?php if ($userDetails['ban'] == 1): ?>
                            <span class="badge-banned">Đã khóa</span>
                        <?php endif; ?>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Email:</span>
                        <?php echo htmlspecialchars($userDetails['email'] ?? 'Chưa có'); ?>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Số điện thoại:</span>
                        <?php echo htmlspecialchars($userDetails['phone'] ?? 'Chưa có'); ?>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Ngày đăng ký:</span>
                        <?php echo date('d/m/Y H:i', strtotime($userDetails['regdate'])); ?>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Trạng thái:</span>
                        <?php if ($userDetails['ban'] == 1): ?>
                            <span class="text-danger">Đã khóa</span>
                        <?php else: ?>
                            <span class="text-success">Đang hoạt động</span>
                        <?php endif; ?>
                    </div>

                    <div class="mt-4">
                        <h4>Thao tác</h4>
                        <div class="d-flex flex-wrap">
                            <?php if ($userDetails['ban'] == 0): ?>
                                <a href="users.php?action=ban&id=<?php echo $userDetails['id']; ?>"
                                    class="btn btn-danger me-2 mb-2"
                                    onclick="return confirm('Bạn có chắc chắn muốn khóa tài khoản này?');">
                                    <i class="fas fa-ban"></i> Khóa tài khoản
                                </a>
                            <?php else: ?>
                                <a href="users.php?action=unban&id=<?php echo $userDetails['id']; ?>"
                                    class="btn btn-admin me-2 mb-2">
                                    <i class="fas fa-unlock"></i> Mở khóa tài khoản
                                </a>
                            <?php endif; ?>

                            <?php if ($userDetails['is_admin'] == 0): ?>
                                <a href="users.php?action=makeadmin&id=<?php echo $userDetails['id']; ?>"
                                    class="btn btn-admin me-2 mb-2"
                                    onclick="return confirm('Bạn có chắc chắn muốn cấp quyền quản trị viên cho tài khoản này?');">
                                    <i class="fas fa-user-shield"></i> Cấp quyền Admin
                                </a>
                            <?php else: ?>
                                <a href="users.php?action=removeadmin&id=<?php echo $userDetails['id']; ?>"
                                    class="btn btn-danger me-2 mb-2"
                                    onclick="return confirm('Bạn có chắc chắn muốn thu hồi quyền quản trị viên từ tài khoản này?');">
                                    <i class="fas fa-user-slash"></i> Thu hồi quyền Admin
                                </a>
                            <?php endif; ?>

                            <a href="activations.php?action=activate&id=<?php echo $userDetails['id']; ?>"
                                class="btn btn-admin me-2 mb-2">
                                <i class="fas fa-check-circle"></i> Kích hoạt tài khoản
                            </a>
                        </div>
                    </div>
                </div>

                <div class="mt-3">
                    <a href="users.php" class="btn btn-admin">Quay lại danh sách</a>
                </div>
            </div>
        <?php else: ?>
            <div class="admin-card">
                <h2><i class="fas fa-users"></i> Danh sách người dùng</h2>

                <form class="search-form" method="get" action="users.php">
                    <div class="input-group">
                        <input type="text" class="form-control" placeholder="Tìm kiếm theo tên đăng nhập hoặc email"
                            name="search" value="<?php echo htmlspecialchars($searchTerm); ?>">
                        <button class="btn btn-admin" type="submit">Tìm kiếm</button>
                        <?php if (!empty($searchTerm)): ?>
                            <a href="users.php" class="btn btn-danger">Xóa bộ lọc</a>
                        <?php endif; ?>
                    </div>
                </form>

                <?php if (empty($users)): ?>
                    <p>Không tìm thấy người dùng nào.</p>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="admin-table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Tên đăng nhập</th>
                                    <th>Email</th>
                                    <th>Ngày đăng ký</th>
                                    <th>Trạng thái</th>
                                    <th>Kích hoạt</th>
                                    <th>Thao tác</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($users as $user): ?>
                                    <tr>
                                        <td><?php echo $user['id']; ?></td>
                                        <td>
                                            <?php echo htmlspecialchars($user['username']); ?>
                                            <?php if ($user['is_admin'] == 1): ?>
                                                <span class="badge-admin">Admin</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo htmlspecialchars($user['email'] ?? 'Chưa có'); ?></td>
                                        <td><?php echo date('d/m/Y', strtotime($user['regdate'])); ?></td>
                                        <td>
                                            <?php if ($user['ban'] == 1): ?>
                                                <span class="badge-banned">Đã khóa</span>
                                            <?php else: ?>
                                                <span class="badge-active">Hoạt động</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($user['activation_status'] === 'Đã kích hoạt'): ?>
                                                <span class="badge-active">Đã kích hoạt</span>
                                            <?php else: ?>
                                                <span class="badge-inactive">Chưa kích hoạt</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <a href="users.php?action=view&id=<?php echo $user['id']; ?>"
                                                class="btn btn-sm btn-admin">Chi tiết</a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <?php if ($totalPages > 1): ?>
                        <nav aria-label="Page navigation">
                            <ul class="pagination">
                                <?php if ($page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link"
                                            href="users.php?page=<?php echo $page - 1; ?><?php echo !empty($searchTerm) ? '&search=' . urlencode($searchTerm) : ''; ?>"
                                            aria-label="Previous">
                                            <span aria-hidden="true">&laquo;</span>
                                        </a>
                                    </li>
                                <?php endif; ?>

                                <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                    <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                        <a class="page-link"
                                            href="users.php?page=<?php echo $i; ?><?php echo !empty($searchTerm) ? '&search=' . urlencode($searchTerm) : ''; ?>">
                                            <?php echo $i; ?>
                                        </a>
                                    </li>
                                <?php endfor; ?>

                                <?php if ($page < $totalPages): ?>
                                    <li class="page-item">
                                        <a class="page-link"
                                            href="users.php?page=<?php echo $page + 1; ?><?php echo !empty($searchTerm) ? '&search=' . urlencode($searchTerm) : ''; ?>"
                                            aria-label="Next">
                                            <span aria-hidden="true">&raquo;</span>
                                        </a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>

    <script>
        $(document).ready(function() {
            // Auto-dismiss alerts after 5 seconds
            setTimeout(function() {
                $('.alert').alert('close');
            }, 5000);
        });
    </script>
</body>

</html>