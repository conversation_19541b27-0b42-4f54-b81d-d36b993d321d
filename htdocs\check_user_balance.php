<?php
// Include database configuration
require_once 'Controllers/database.php';

// Connect to database
try {
    $conn = connectPDO($config1);
    echo "Connected to database successfully.<br>";
    
    // Kiểm tra xem có tham số user_id không
    if (isset($_GET['user_id'])) {
        $userId = (int)$_GET['user_id'];
        
        // Lấy thông tin người dùng
        $stmt = $conn->prepare("SELECT * FROM team_user WHERE id = :user_id");
        $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
        $stmt->execute();
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($user) {
            echo "<h3>User Information:</h3>";
            echo "<ul>";
            echo "<li>ID: " . $user['id'] . "</li>";
            echo "<li>Username: " . htmlspecialchars($user['username']) . "</li>";
            echo "<li>Email: " . htmlspecialchars($user['email'] ?? 'N/A') . "</li>";
            echo "<li>Registration Date: " . ($user['regdate'] ?? 'N/A') . "</li>";
            echo "</ul>";
            
            // Lấy số dư tài khoản
            $stmt = $conn->prepare("SELECT * FROM user_balance WHERE user_id = :user_id");
            $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
            $stmt->execute();
            $balance = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($balance) {
                echo "<h3>Account Balance:</h3>";
                echo "<ul>";
                echo "<li>Balance: " . number_format($balance['balance']) . " xu</li>";
                echo "<li>Last Updated: " . $balance['updated_at'] . "</li>";
                echo "</ul>";
            } else {
                echo "<p>No balance information found for this user.</p>";
            }
            
            // Lấy lịch sử giao dịch thẻ cào
            $stmt = $conn->prepare("SELECT * FROM card_transactions WHERE user_id = :user_id ORDER BY created_at DESC LIMIT 10");
            $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
            $stmt->execute();
            $transactions = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (count($transactions) > 0) {
                echo "<h3>Recent Card Transactions:</h3>";
                echo "<table border='1' cellpadding='5' cellspacing='0'>";
                echo "<tr><th>ID</th><th>Transaction ID</th><th>Card Type</th><th>Amount</th><th>Xu Amount</th><th>Status</th><th>Created At</th></tr>";
                
                foreach ($transactions as $transaction) {
                    echo "<tr>";
                    echo "<td>" . $transaction['id'] . "</td>";
                    echo "<td>" . htmlspecialchars($transaction['transaction_id']) . "</td>";
                    echo "<td>" . htmlspecialchars($transaction['card_type']) . "</td>";
                    echo "<td>" . number_format($transaction['amount']) . "</td>";
                    echo "<td>" . number_format($transaction['xu_amount']) . "</td>";
                    echo "<td>" . htmlspecialchars($transaction['status']) . "</td>";
                    echo "<td>" . $transaction['created_at'] . "</td>";
                    echo "</tr>";
                }
                
                echo "</table>";
            } else {
                echo "<p>No card transactions found for this user.</p>";
            }
            
            // Lấy lịch sử giao dịch xu
            $stmt = $conn->prepare("SELECT * FROM balance_transactions WHERE user_id = :user_id ORDER BY created_at DESC LIMIT 10");
            $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
            $stmt->execute();
            $balanceTransactions = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (count($balanceTransactions) > 0) {
                echo "<h3>Recent Balance Transactions:</h3>";
                echo "<table border='1' cellpadding='5' cellspacing='0'>";
                echo "<tr><th>ID</th><th>Amount</th><th>Type</th><th>Description</th><th>Status</th><th>Created At</th></tr>";
                
                foreach ($balanceTransactions as $transaction) {
                    echo "<tr>";
                    echo "<td>" . $transaction['id'] . "</td>";
                    echo "<td>" . number_format($transaction['amount']) . "</td>";
                    echo "<td>" . htmlspecialchars($transaction['transaction_type']) . "</td>";
                    echo "<td>" . htmlspecialchars($transaction['description']) . "</td>";
                    echo "<td>" . htmlspecialchars($transaction['status']) . "</td>";
                    echo "<td>" . $transaction['created_at'] . "</td>";
                    echo "</tr>";
                }
                
                echo "</table>";
            } else {
                echo "<p>No balance transactions found for this user.</p>";
            }
        } else {
            echo "<p>User not found with ID: " . $userId . "</p>";
        }
    } else {
        // Lấy danh sách người dùng
        $stmt = $conn->query("SELECT id, username, email, regdate FROM team_user ORDER BY id DESC LIMIT 20");
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($users) > 0) {
            echo "<h3>Users:</h3>";
            echo "<table border='1' cellpadding='5' cellspacing='0'>";
            echo "<tr><th>ID</th><th>Username</th><th>Email</th><th>Registration Date</th><th>Action</th></tr>";
            
            foreach ($users as $user) {
                echo "<tr>";
                echo "<td>" . $user['id'] . "</td>";
                echo "<td>" . htmlspecialchars($user['username']) . "</td>";
                echo "<td>" . htmlspecialchars($user['email'] ?? 'N/A') . "</td>";
                echo "<td>" . ($user['regdate'] ?? 'N/A') . "</td>";
                echo "<td><a href='?user_id=" . $user['id'] . "'>Check Balance</a></td>";
                echo "</tr>";
            }
            
            echo "</table>";
        } else {
            echo "<p>No users found.</p>";
        }
        
        // Form để tìm người dùng theo ID
        echo "<h3>Search for a user:</h3>";
        echo "<form method='get'>";
        echo "<input type='number' name='user_id' placeholder='Enter user ID'>";
        echo "<button type='submit'>Search</button>";
        echo "</form>";
    }
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage();
}
?>
