/* CSS cho tính năng nạp thẻ và nạp ATM */

/* <PERSON><PERSON><PERSON> chung */
.topup-section {
    margin-bottom: 2rem;
}

.topup-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: var(--primary-color);
    text-align: center;
    position: relative;
    padding-bottom: 0.5rem;
}

.topup-title:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-dark));
    border-radius: 3px;
}

/* Card cho phương thức nạp tiền */
.topup-methods {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    justify-content: center;
}

.topup-method-card {
    flex: 1;
    min-width: 250px;
    max-width: 350px;
    background-color: var(--bg-darker);
    border-radius: 10px;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid var(--primary-color);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.topup-method-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
    border-color: var(--primary-color);
}

.topup-method-header {
    padding: 1.5rem;
    text-align: center;
    background-color: rgba(249, 214, 134, 0.1);
    border-bottom: 1px solid var(--primary-color);
}

.topup-method-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: var(--primary-color);
    display: inline-block;
    background: rgba(249, 214, 134, 0.1);
    width: 80px;
    height: 80px;
    line-height: 80px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.topup-method-card:hover .topup-method-icon {
    transform: scale(1.1);
    background: rgba(249, 214, 134, 0.2);
}

.topup-method-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

.topup-method-description {
    color: var(--text-color);
    font-size: 0.9rem;
    margin-bottom: 1rem;
    min-height: 40px;
}

.topup-method-body {
    padding: 1.5rem;
    text-align: center;
}

.topup-btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-dark));
    color: var(--secondary-color);
    font-weight: 600;
    border-radius: 50px;
    text-decoration: none;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    width: 100%;
    text-align: center;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.topup-btn:hover {
    background: linear-gradient(90deg, var(--primary-dark), var(--primary-color));
    transform: translateY(-2px);
    box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
}

.topup-btn i {
    margin-right: 0.5rem;
}

/* Modal styles */
.topup-modal .modal-content {
    background-color: var(--bg-dark);
    color: var(--text-color);
    border: 1px solid var(--primary-color);
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.topup-modal .modal-header {
    border-bottom: 1px solid var(--primary-color);
    padding: 1.5rem;
    background-color: rgba(249, 214, 134, 0.1);
}

.topup-modal .modal-title {
    font-weight: 600;
    color: var(--primary-color);
}

.topup-modal .modal-body {
    padding: 1.5rem;
}

.topup-modal .modal-footer {
    border-top: 1px solid var(--primary-color);
    padding: 1.5rem;
}

.topup-modal .close {
    color: var(--text-color);
    opacity: 0.8;
}

.topup-modal .close:hover {
    opacity: 1;
}

/* Form styles */
.topup-form .form-label {
    color: var(--primary-color);
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.topup-form .form-control {
    background-color: rgba(255, 255, 255, 0.9);
    border: 1px solid var(--primary-color);
    color: var(--secondary-color);
    border-radius: 8px;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.topup-form .form-control:focus {
    background-color: #fff;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(249, 214, 134, 0.25);
}

.topup-form .form-select {
    background-color: rgba(255, 255, 255, 0.9);
    border: 1px solid var(--primary-color);
    color: var(--secondary-color);
    border-radius: 8px;
    padding: 0.75rem 1rem;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
    transition: all 0.3s ease;
}

.topup-form .form-select:focus {
    background-color: #fff;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(249, 214, 134, 0.25);
}

.topup-form .form-text {
    color: #adb5bd;
    font-size: 0.85rem;
    margin-top: 0.25rem;
}

/* Alert styles */
.topup-alert {
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 1.5rem;
    border: 1px solid transparent;
}

.topup-alert-info {
    background-color: rgba(23, 162, 184, 0.1);
    border-color: rgba(23, 162, 184, 0.2);
    color: var(--info-color);
}

.topup-alert-warning {
    background-color: rgba(255, 193, 7, 0.1);
    border-color: rgba(255, 193, 7, 0.2);
    color: var(--warning-color);
}

.topup-alert-danger {
    background-color: rgba(220, 53, 69, 0.1);
    border-color: rgba(220, 53, 69, 0.2);
    color: var(--danger-color);
}

.topup-alert-success {
    background-color: rgba(40, 167, 69, 0.1);
    border-color: rgba(40, 167, 69, 0.2);
    color: var(--success-color);
}

.topup-alert strong {
    color: inherit;
}

.topup-alert ul {
    margin-bottom: 0;
    padding-left: 1.5rem;
}

.topup-alert li {
    margin-bottom: 0.25rem;
}

.topup-alert li:last-child {
    margin-bottom: 0;
}

/* Bank info styles */
.bank-info {
    background-color: var(--bg-darker);
    border: 1px solid var(--primary-color);
    border-radius: 10px;
    padding: 1.5rem;
    height: 100%;
}

.bank-info-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--primary-color);
    text-align: center;
}

.bank-details p {
    margin-bottom: 0.75rem;
    color: var(--text-color);
}

.bank-details strong {
    color: var(--primary-color);
    margin-right: 0.5rem;
}

.qr-code {
    text-align: center;
    margin-top: 1.5rem;
}

.qr-code img {
    max-width: 200px;
    border-radius: 10px;
    border: 5px solid var(--primary-color);
}

.bank-note {
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid var(--primary-color);
}

.bank-note p {
    margin-bottom: 0;
    color: var(--text-color);
}

.bank-note strong {
    color: var(--primary-color);
}

.bank-note .username {
    color: var(--primary-color);
    font-weight: 600;
}

/* Transaction history styles */
.transaction-history {
    margin-top: 2rem;
}

.transaction-history-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.transaction-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    border-radius: 10px;
    overflow: hidden;
    background-color: var(--bg-darker);
    border: 1px solid var(--primary-color);
}

.transaction-table th,
.transaction-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid rgba(249, 214, 134, 0.1);
}

.transaction-table th {
    background-color: rgba(249, 214, 134, 0.1);
    color: var(--primary-color);
    font-weight: 600;
}

.transaction-table tr:last-child td {
    border-bottom: none;
}

.transaction-table td {
    color: var(--text-color);
}

.status-pending {
    color: var(--warning-color);
    font-weight: 500;
}

.status-approved,
.status-success {
    color: var(--success-color);
    font-weight: 500;
}

.status-rejected,
.status-failed {
    color: var(--danger-color);
    font-weight: 500;
}

/* Responsive styles */
@media (max-width: 991.98px) {
    .topup-methods {
        flex-direction: column;
        align-items: center;
    }

    .topup-method-card {
        width: 100%;
        max-width: 100%;
        margin-bottom: 1.5rem;
    }
}

@media (max-width: 767.98px) {
    .topup-modal .modal-dialog {
        margin: 0.5rem;
    }

    .bank-info {
        margin-top: 1.5rem;
    }

    .transaction-table {
        display: block;
        overflow-x: auto;
        white-space: nowrap;
    }

    .transaction-table th,
    .transaction-table td {
        padding: 0.75rem;
    }
}

@media (max-width: 575.98px) {
    .topup-title {
        font-size: 1.25rem;
    }

    .topup-method-icon {
        width: 60px;
        height: 60px;
        line-height: 60px;
        font-size: 1.75rem;
    }

    .topup-method-title {
        font-size: 1.1rem;
    }
}

@media (max-width: 575.98px) {
    .topup-btn {
        padding: 0.6rem 1.2rem;
        font-size: 0.9rem;
    }

    .topup-modal .modal-header,
    .topup-modal .modal-body,
    .topup-modal .modal-footer {
        padding: 1rem;
    }

    .transaction-history-title {
        font-size: 1.1rem;
    }

    .transaction-table th,
    .transaction-table td {
        padding: 0.75rem;
        font-size: 0.85rem;
    }
}
