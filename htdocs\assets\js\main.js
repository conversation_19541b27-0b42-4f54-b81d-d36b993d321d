/**
 * Main JavaScript file for KPAH website
 * Handles responsive design, modal interactions, and UI improvements
 */

// Wait for DOM to be ready
document.addEventListener('DOMContentLoaded', function() {
    
    // Initialize all components
    initializeModals();
    initializeResponsive();
    initializeFormValidation();
    initializeImageOptimization();
    initializeAccessibility();
    
    console.log('KPAH website initialized successfully');
});

/**
 * Initialize modal functionality
 */
function initializeModals() {
    // Fix modal backdrop issues
    const modals = document.querySelectorAll('.modal');
    modals.forEach(function(modal) {
        modal.addEventListener('show.bs.modal', function() {
            document.body.classList.add('modal-open');
        });
        
        modal.addEventListener('hidden.bs.modal', function() {
            document.body.classList.remove('modal-open');
            // Clear form fields when modal is closed
            const forms = modal.querySelectorAll('form');
            forms.forEach(function(form) {
                form.reset();
            });
        });
    });

    // Handle modal switching
    const modalSwitchButtons = document.querySelectorAll('[data-bs-toggle="modal"][data-bs-target]');
    modalSwitchButtons.forEach(function(button) {
        button.addEventListener('click', function() {
            const currentModal = button.closest('.modal');
            if (currentModal) {
                const modalInstance = bootstrap.Modal.getInstance(currentModal);
                if (modalInstance) {
                    modalInstance.hide();
                }
            }
        });
    });
}

/**
 * Initialize responsive design features
 */
function initializeResponsive() {
    // Handle window resize
    let resizeTimer;
    window.addEventListener('resize', function() {
        clearTimeout(resizeTimer);
        resizeTimer = setTimeout(function() {
            adjustLayoutForScreenSize();
        }, 250);
    });

    // Initial layout adjustment
    adjustLayoutForScreenSize();

    // Handle orientation change
    window.addEventListener('orientationchange', function() {
        setTimeout(adjustLayoutForScreenSize, 500);
    });
}

/**
 * Adjust layout based on screen size
 */
function adjustLayoutForScreenSize() {
    const screenWidth = window.innerWidth;
    const isMobile = screenWidth <= 768;
    
    // Adjust modal sizes
    const modals = document.querySelectorAll('.modal-dialog');
    modals.forEach(function(modal) {
        if (isMobile) {
            modal.style.maxWidth = '95%';
            modal.style.margin = '10px auto';
        } else {
            modal.style.maxWidth = '';
            modal.style.margin = '';
        }
    });

    // Adjust button groups
    const btnGroups = document.querySelectorAll('.btn-group-download');
    btnGroups.forEach(function(group) {
        if (isMobile) {
            group.classList.add('flex-column');
        } else {
            group.classList.remove('flex-column');
        }
    });

    // Adjust navigation
    const navElements = document.querySelectorAll('.admin-menu ul');
    navElements.forEach(function(nav) {
        if (isMobile) {
            nav.style.flexDirection = 'column';
        } else {
            nav.style.flexDirection = 'row';
        }
    });
}

/**
 * Initialize form validation
 */
function initializeFormValidation() {
    const forms = document.querySelectorAll('form');
    forms.forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!validateForm(form)) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        });
    });
}

/**
 * Validate form fields
 */
function validateForm(form) {
    let isValid = true;
    
    // Check required fields
    const requiredFields = form.querySelectorAll('[required]');
    requiredFields.forEach(function(field) {
        if (!field.value.trim()) {
            isValid = false;
            showFieldError(field, 'Trường này là bắt buộc');
        } else {
            clearFieldError(field);
        }
    });

    // Validate email fields
    const emailFields = form.querySelectorAll('input[type="email"]');
    emailFields.forEach(function(field) {
        if (field.value && !isValidEmail(field.value)) {
            isValid = false;
            showFieldError(field, 'Email không hợp lệ');
        }
    });

    // Validate password confirmation
    const passwordField = form.querySelector('input[name="password"]');
    const confirmPasswordField = form.querySelector('input[name="RePasswordword"]');
    if (passwordField && confirmPasswordField) {
        if (passwordField.value !== confirmPasswordField.value) {
            isValid = false;
            showFieldError(confirmPasswordField, 'Mật khẩu xác nhận không khớp');
        }
    }

    return isValid;
}

/**
 * Show field error
 */
function showFieldError(field, message) {
    clearFieldError(field);
    field.classList.add('is-invalid');
    
    const errorDiv = document.createElement('div');
    errorDiv.className = 'invalid-feedback';
    errorDiv.textContent = message;
    field.parentNode.appendChild(errorDiv);
}

/**
 * Clear field error
 */
function clearFieldError(field) {
    field.classList.remove('is-invalid');
    const errorDiv = field.parentNode.querySelector('.invalid-feedback');
    if (errorDiv) {
        errorDiv.remove();
    }
}

/**
 * Validate email format
 */
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * Initialize image optimization
 */
function initializeImageOptimization() {
    // Lazy loading for images
    const images = document.querySelectorAll('img');
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver(function(entries, observer) {
            entries.forEach(function(entry) {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    if (img.dataset.src) {
                        img.src = img.dataset.src;
                        img.removeAttribute('data-src');
                    }
                    observer.unobserve(img);
                }
            });
        });

        images.forEach(function(img) {
            if (img.dataset.src) {
                imageObserver.observe(img);
            }
        });
    }

    // Handle image load errors
    images.forEach(function(img) {
        img.addEventListener('error', function() {
            this.style.display = 'none';
            console.warn('Failed to load image:', this.src);
        });
    });
}

/**
 * Initialize accessibility features
 */
function initializeAccessibility() {
    // Add keyboard navigation for custom elements
    const customButtons = document.querySelectorAll('.zoom-50, .brightness');
    customButtons.forEach(function(button) {
        if (!button.hasAttribute('tabindex')) {
            button.setAttribute('tabindex', '0');
        }
        
        button.addEventListener('keydown', function(event) {
            if (event.key === 'Enter' || event.key === ' ') {
                event.preventDefault();
                button.click();
            }
        });
    });

    // Improve focus visibility
    const focusableElements = document.querySelectorAll('a, button, input, select, textarea, [tabindex]');
    focusableElements.forEach(function(element) {
        element.addEventListener('focus', function() {
            this.style.outline = '2px solid var(--primary-color)';
            this.style.outlineOffset = '2px';
        });
        
        element.addEventListener('blur', function() {
            this.style.outline = '';
            this.style.outlineOffset = '';
        });
    });
}

/**
 * Show alert message
 */
function showAlert(message, type = 'info', duration = 5000) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.style.position = 'fixed';
    alertDiv.style.top = '20px';
    alertDiv.style.left = '50%';
    alertDiv.style.transform = 'translateX(-50%)';
    alertDiv.style.zIndex = '9999';
    alertDiv.style.minWidth = '300px';
    alertDiv.style.maxWidth = '80%';
    
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // Auto dismiss
    setTimeout(function() {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, duration);
}

/**
 * Utility function to get URL parameters
 */
function getUrlParameter(name) {
    name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
    const regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
    const results = regex.exec(location.search);
    return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
}

/**
 * Handle URL parameters for messages
 */
function handleUrlMessages() {
    const error = getUrlParameter('error');
    const success = getUrlParameter('success');
    
    if (error) {
        showAlert(error, 'danger');
    }
    
    if (success) {
        showAlert(success, 'success');
    }
}

// Initialize URL message handling
document.addEventListener('DOMContentLoaded', handleUrlMessages);

/**
 * Performance optimization
 */
function optimizePerformance() {
    // Debounce scroll events
    let scrollTimer;
    window.addEventListener('scroll', function() {
        clearTimeout(scrollTimer);
        scrollTimer = setTimeout(function() {
            // Handle scroll events here if needed
        }, 100);
    });

    // Preload critical resources
    const criticalImages = [
        'assets/homepage/images/bg-page1.jpg',
        'assets/homepage/images/logo.png'
    ];
    
    criticalImages.forEach(function(src) {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'image';
        link.href = src;
        document.head.appendChild(link);
    });
}

// Initialize performance optimizations
document.addEventListener('DOMContentLoaded', optimizePerformance);
